<?php

namespace App\Http\Resources\V3\ReturnMethods;

use Illuminate\Http\Resources\Json\ResourceCollection;

class ReturnMethodsCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'methods' => $this->collection->map(function ($item) {
                return new ReturnMethodResource($item);
            }),
            'defaultMethod' => $this->collection->where('is_default', true)->first()?->id ?? null
        ];
    }
}
