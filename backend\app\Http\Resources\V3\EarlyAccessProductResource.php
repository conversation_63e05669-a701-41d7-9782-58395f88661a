<?php

namespace App\Http\Resources\V3;

use App\Services\Product\EarlyAccessProductsService;
use Illuminate\Http\Resources\Json\JsonResource;

class EarlyAccessProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $earlyAccessProductsService = new EarlyAccessProductsService();
        $isEarlyAccessValid = $earlyAccessProductsService->isEarlyAccessValid($this);
        
        // Calculate commission value
        $commissionRate = 15; // Default commission rate (15%)
        $commissionValue = ($this->dropshipper_price * $commissionRate) / 100;
        
        // Calculate discount percentage
        $discountPercentage = 0;
        if ($this->unit_price > 0 && $this->discount > 0) {
            $discountPercentage = $this->discount;
        }
        
        // Get average rating
        $averageRating = $this->reviews->avg('rating') ?? 0;
        
        // Get features from the product
        $features = [];
        if (!empty($this->choice_options)) {
            foreach ($this->choice_options as $option) {
                if (isset($option['title']) && isset($option['options'])) {
                    $features[] = $option['title'] . ': ' . implode(', ', $option['options']);
                }
            }
        }
        
        // Get specifications
        $specifications = [];
        if (!empty($this->attributes)) {
            foreach ($this->attributes as $attribute) {
                if (isset($attribute['name']) && isset($attribute['value'])) {
                    $specifications[$attribute['name']] = $attribute['value'];
                }
            }
        }
        
        // Get tags
        $tags = [];
        if (!empty($this->tags)) {
            $tags = explode(',', $this->tags);
        }
        
        // Add early access tag
        $tags[] = 'Early Access';
        
        // Add featured tag if applicable
        if ($this->featured) {
            $tags[] = 'Featured';
        }
        
        if ($this->seller_featured) {
            $tags[] = 'Seller Featured';
        }
        
        return [
            'id' => $this->id,
            'name' => $this->getTranslation('name'),
            'description' => $this->getTranslation('description'),
            'price' => (float) $this->dropshipper_price,
            'originalPrice' => (float) $this->unit_price,
            'discount' => (float) $discountPercentage,
            'thumbnail' => $this->thumbnail ? uploaded_asset($this->thumbnail->id) : null,
            'images' => $this->photos ? explode(',', $this->photos) : [],
            'category' => $this->category ? $this->category->name : null,
            'brand' => $this->brand ? $this->brand->name : null,
            'rating' => (float) number_format($averageRating, 1),
            'reviewCount' => $this->reviews->count(),
            'inStock' => $this->current_stock > 0,
            'stockQuantity' => (int) $this->current_stock,
            'features' => $features,
            'specifications' => $specifications,
            'supplierPrice' => (float) $this->purchase_price,
            'commissionRate' => (float) $commissionRate,
            'commissionValue' => (float) number_format($commissionValue, 2),
            'minimumOrderQuantity' => (int) $this->min_qty,
            'shippingFee' => 0, // Assuming free shipping for early access products
            'estimatedDelivery' => '3-5 days', // Default estimated delivery
            'isEarlyAccess' => (bool) $isEarlyAccessValid,
            'earlyAccessExpiry' => $this->early_access_expiry ? $this->early_access_expiry->toIso8601String() : null,
            'tags' => array_values(array_unique($tags)),
            'createdAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String(),
        ];
    }
}
