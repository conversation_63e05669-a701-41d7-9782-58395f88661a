<?php

namespace App\Http\Controllers;

use App\Models\SellerPackagePayment;
use App\Models\SellerPackage;
use App\Models\Seller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class SellerPackagePaymentController extends Controller
{
    /**
     * Display offline seller package payment requests
     */
    public function offline_payment_request()
    {
        $package_payment_requests = SellerPackagePayment::where('offline_payment', 1)
                                                       ->where('approval', 0)
                                                       ->with(['user', 'seller_package'])
                                                       ->orderBy('id', 'desc')
                                                       ->paginate(20);
        
        return view('backend.seller_packages.offline_payment_requests', compact('package_payment_requests'));
    }

    /**
     * Approve or reject offline seller package payment
     */
    public function offline_payment_approval(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:seller_package_payments,id',
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data'
            ], 400);
        }

        try {
            $package_payment = SellerPackagePayment::findOrFail($request->id);
            $package_details = SellerPackage::findOrFail($package_payment->seller_package_id);
            
            $package_payment->approval = $request->status;
            
            if ($package_payment->save()) {
                // If approved, update seller package
                if ($request->status == 1) {
                    $seller = $package_payment->user->seller;
                    
                    if ($seller) {
                        $seller->seller_package_id = $package_payment->seller_package_id;
                        
                        // Calculate new expiry date
                        $current_date = $seller->invalid_at ? Carbon::parse($seller->invalid_at) : Carbon::now();
                        $new_expiry = $current_date->addDays($package_details->duration);
                        $seller->invalid_at = $new_expiry->format('Y-m-d');
                        
                        $seller->save();
                        
                        flash(translate('Seller package payment approved successfully'))->success();
                    }
                } else {
                    flash(translate('Seller package payment rejected'))->warning();
                }
                
                return response()->json([
                    'success' => true,
                    'message' => $request->status == 1 ? 'Payment approved successfully' : 'Payment rejected'
                ]);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment status'
            ], 500);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display payment details
     */
    public function show($id)
    {
        $payment = SellerPackagePayment::with(['user', 'seller_package'])
                                      ->findOrFail($id);
        
        return view('backend.seller_packages.payment_details', compact('payment'));
    }

    /**
     * Get payment statistics
     */
    public function getStats()
    {
        $stats = [
            'pending_payments' => SellerPackagePayment::where('offline_payment', 1)
                                                     ->where('approval', 0)
                                                     ->count(),
            'approved_payments' => SellerPackagePayment::where('offline_payment', 1)
                                                      ->where('approval', 1)
                                                      ->count(),
            'rejected_payments' => SellerPackagePayment::where('offline_payment', 1)
                                                      ->where('approval', 2)
                                                      ->count(),
            'total_amount' => SellerPackagePayment::where('offline_payment', 1)
                                                 ->where('approval', 1)
                                                 ->sum('amount')
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Bulk approve payments
     */
    public function bulkApprove(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_ids' => 'required|array',
            'payment_ids.*' => 'exists:seller_package_payments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid payment IDs'
            ], 400);
        }

        try {
            $approved_count = 0;
            
            foreach ($request->payment_ids as $payment_id) {
                $result = $this->offline_payment_approval(new Request([
                    'id' => $payment_id,
                    'status' => 1
                ]));
                
                if ($result->getData()->success) {
                    $approved_count++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully approved {$approved_count} payments"
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk approval failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export payment requests
     */
    public function export(Request $request)
    {
        $payments = SellerPackagePayment::where('offline_payment', 1)
                                       ->with(['user', 'seller_package'])
                                       ->get();

        // This can be implemented with Excel export functionality
        return response()->json([
            'success' => true,
            'message' => 'Export functionality to be implemented',
            'data' => $payments
        ]);
    }
} 