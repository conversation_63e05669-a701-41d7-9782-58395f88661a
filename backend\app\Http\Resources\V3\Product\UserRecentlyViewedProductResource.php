<?php

namespace App\Http\Resources\V3\Product;

use App\Http\Resources\V3\ProductResource;
use App\Http\Resources\V3\CategoryResource;
use App\Http\Resources\V3\BrandResource;
use Illuminate\Http\Resources\Json\JsonResource;

class UserRecentlyViewedProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'productId' => $this->product ? $this->product->id : null, // FIXED: Use numeric product ID instead of slug
            'viewCount' => $this->view_count,
            'viewedAt' => $this->viewed_at ? $this->viewed_at->toIso8601String() : null,
            'product' => $this->product ? new ProductResource($this->product) : null,
            'category' => ($this->product && $this->product->category) ? new CategoryResource($this->product->category) : null,
            'brand' => ($this->product && $this->product->brand) ? new BrandResource($this->product->brand) : null,
        ];
    }
}
