<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class CartCollectionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($item) {
            return [
                'id' => (int) $item->id,
                'temp_user_id' => $item->temp_user_id,
                'product_id' => (int) $item->product_id,
                'variation' => $item->variation,
                'price' => (float) $item->price,
                'tax' => (float) $item->tax,
                'shipping_cost' => (float) $item->shipping_cost,
                'quantity' => (int) $item->quantity,
                'coupon_applied' => (boolean) $item->coupon_applied,
                'coupon_code' => (string) $item->coupon_code,
                'discount' => (float) $item->discount,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at

            ];
        });
    }
}
