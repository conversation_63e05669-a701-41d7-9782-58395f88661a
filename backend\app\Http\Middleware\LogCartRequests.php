<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LogCartRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only log cart-related requests
        if (strpos($request->path(), 'api/v3/cart') !== false || 
            strpos($request->path(), 'api/v3/add-to-cart') !== false) {
            
            // Log the request data
            Log::channel('api_cart')->info('Cart API Request', [
                'method' => $request->method(),
                'path' => $request->path(),
                'url' => $request->fullUrl(),
                'payload' => $request->all(),
                'headers' => $request->header(),
                'user_id' => auth()->check() ? auth()->id() : 'guest',
                'ip' => $request->ip(),
            ]);
        }

        return $next($request);
    }
}