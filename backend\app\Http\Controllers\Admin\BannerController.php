<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Banner;
use Illuminate\Support\Str;

class BannerController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check
        $this->middleware(['permission:view_all_banners'])->only('index');
        $this->middleware(['permission:add_banner'])->only(['create', 'store']);
        $this->middleware(['permission:edit_banner'])->only(['edit', 'update']);
        $this->middleware(['permission:delete_banner'])->only('destroy');
    }

    /**
     * Display a listing of banners.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $sort_search = null;
        $banners = Banner::latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $banners = $banners->where('title', 'like', '%' . $sort_search . '%');
        }

        $banners = $banners->paginate(15);
        return view('backend.marketing.banners.index', compact('banners', 'sort_search'));
    }

    /**
     * Show the form for creating a new banner.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.marketing.banners.create');
    }

    /**
     * Store a newly created banner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'photo' => 'required',
            'url' => 'nullable|url',
            'position' => 'required|integer',
        ]);

        $banner = new Banner;
        $banner->title = $request->title;
        $banner->photo = $request->photo;
        $banner->url = $request->url;
        $banner->position = $request->position;
        $banner->published = $request->published ?? 1;

        if ($banner->save()) {
            flash(translate('Banner has been inserted successfully'))->success();
            return redirect()->route('admin.banners.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show the form for editing the specified banner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $banner = Banner::findOrFail($id);
        return view('backend.marketing.banners.edit', compact('banner'));
    }

    /**
     * Update the specified banner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'photo' => 'required',
            'url' => 'nullable|url',
            'position' => 'required|integer',
        ]);

        $banner = Banner::findOrFail($id);
        $banner->title = $request->title;
        $banner->photo = $request->photo;
        $banner->url = $request->url;
        $banner->position = $request->position;
        $banner->published = $request->published ?? 1;

        if ($banner->save()) {
            flash(translate('Banner has been updated successfully'))->success();
            return redirect()->route('admin.banners.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Remove the specified banner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $banner = Banner::findOrFail($id);

        if ($banner->delete()) {
            flash(translate('Banner has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Update banner status
     */
    public function updateStatus(Request $request)
    {
        $banner = Banner::findOrFail($request->id);
        $banner->published = $request->status;

        if ($banner->save()) {
            return response()->json(['success' => true, 'message' => translate('Banner status updated successfully')]);
        }

        return response()->json(['success' => false, 'message' => translate('Something went wrong')]);
    }
} 