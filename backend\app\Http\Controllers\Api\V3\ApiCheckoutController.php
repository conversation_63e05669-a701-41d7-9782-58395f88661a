<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Resources\V3\Checkout\CheckoutShippingResource;
use App\Models\Coupon;
use App\Services\Checkout\ApiCheckoutService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Address;
use App\Models\CartInfo;
use App\Models\Cart;
use App\Services\ShippingMethod\ApiShippingMethodService;
use App\Services\PaymentMethod\ApiPaymentMethodService;
use App\Models\ShippingMethod;
use App\Models\PaymentMethod;

class ApiCheckoutController extends ApiResponse
{
    protected $checkoutService;
    protected $shippingMethodService;
    protected $paymentMethodService;

    /**
     * Create a new controller instance.
     *
     * @param ApiCheckoutService $checkoutService
     * @param ApiShippingMethodService $shippingMethodService
     * @param ApiPaymentMethodService $paymentMethodService
     * @return void
     */
    public function __construct(
        ApiCheckoutService $checkoutService,
        ApiShippingMethodService $shippingMethodService = null,
        ApiPaymentMethodService $paymentMethodService = null
    ) {
        $this->checkoutService = $checkoutService;
        $this->shippingMethodService = $shippingMethodService ?? new ApiShippingMethodService();
        $this->paymentMethodService = $paymentMethodService ?? new ApiPaymentMethodService();
    }

    /**
     * Initialize checkout process
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function initialize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'cartId' => 'required|exists:cart_infos,id',
            'email' => 'nullable|email'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $cartId = $request->cartId;
        $email = $request->email ?? Auth::user()->email;
        
        // Find cart
        $cartInfo = CartInfo::find($cartId);
        if (!$cartInfo) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        
        // Initialize checkout
        try {
            $checkout = $this->checkoutService->initializeCheckout($cartInfo, $email);
            
            return $this->success(
                $checkout,
                'Checkout initialized successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Checkout Initialization Failed',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Get current checkout
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCurrent(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        try {
            $checkout = $this->checkoutService->getCurrentCheckout($user->id);
            
            if (!$checkout) {
                return $this->error(
                    'No Active Checkout',
                    'No active checkout found',
                    null,
                    404
                );
            }
            
            return $this->success(
                $checkout,
                'Checkout retrieved successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Retrieving Checkout',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Apply coupon code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function apply_coupon_code(Request $request)
    {
        $coupon_code = $request->coupon_code;

        $coupon = Coupon::where('code', $coupon_code)->first();

        if ($coupon != null) {
            if (strtotime(date('d-m-Y')) >= $coupon->start_date && strtotime(date('d-m-Y')) <= $coupon->end_date) {
                // TODO: Implement coupon application logic
                return $this->success(['message' => 'Coupon applied successfully']);
            } else {
                return $this->error('COUPON_EXPIRED', 'Applied coupon code is expired', null, 400);
            }
        } else {
            return $this->error('COUPON_NOT_FOUND', 'Coupon code not found', null, 404);
        }
    }
    
    /**
     * Remove coupon code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function removeCoupon(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'coupon_code' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $couponCode = $request->coupon_code;
        $user = Auth::user();
        
        try {
            $checkout = $this->checkoutService->removeCoupon($user->id, $couponCode);
            
            return $this->success(
                $checkout,
                'Coupon removed successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Removing Coupon',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Update shipping address
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateShippingAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'addressLine1' => 'required|string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postalCode' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'phoneNumber' => 'required|string|max:20',
            'email' => 'nullable|email|max:255'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        
        try {
            $checkout = $this->checkoutService->updateShippingAddress($user->id, $request->all());
            
            return $this->success(
                $checkout,
                'Shipping address updated successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Updating Shipping Address',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Update billing address
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateBillingAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sameAsShipping' => 'required|boolean',
            'firstName' => 'required_if:sameAsShipping,false|string|max:255',
            'lastName' => 'required_if:sameAsShipping,false|string|max:255',
            'addressLine1' => 'required_if:sameAsShipping,false|string|max:255',
            'addressLine2' => 'nullable|string|max:255',
            'city' => 'required_if:sameAsShipping,false|string|max:255',
            'state' => 'required_if:sameAsShipping,false|string|max:255',
            'postalCode' => 'required_if:sameAsShipping,false|string|max:20',
            'country' => 'required_if:sameAsShipping,false|string|max:255',
            'phoneNumber' => 'required_if:sameAsShipping,false|string|max:20',
            'email' => 'nullable|email|max:255'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        $sameAsShipping = $request->sameAsShipping;
        
        try {
            if ($sameAsShipping) {
                $checkout = $this->checkoutService->setBillingAddressSameAsShipping($user->id);
            } else {
                $checkout = $this->checkoutService->updateBillingAddress($user->id, $request->all());
            }
            
            return $this->success(
                $checkout,
                'Billing address updated successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Updating Billing Address',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Get shipping methods
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getShippingMethods(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        try {
            $methods = $this->shippingMethodService->getAvailableShippingMethods($user->id);
            
            return $this->success(
                ['methods' => $methods],
                'Shipping methods retrieved successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Retrieving Shipping Methods',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Select shipping method for checkout
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function selectShippingMethod(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'shipping_method_id' => 'required|exists:shipping_methods,id'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid shipping method information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        $shippingMethodId = $request->shipping_method_id;
        
        try {
            // Get the shipping method
            $shippingMethod = ShippingMethod::findOrFail($shippingMethodId);
            
            if (!$shippingMethod->is_active) {
                return $this->error(
                    'Shipping Method Unavailable',
                    'The selected shipping method is currently unavailable',
                    null,
                    400
                );
            }
            
            // Apply shipping method to checkout
            $checkout = $this->checkoutService->selectShippingMethod($user->id, $shippingMethodId);
            
            return $this->success(
                $checkout,
                'Shipping method selected successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Selecting Shipping Method',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Get payment methods
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentMethods(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        try {
            $methods = $this->paymentMethodService->getAvailablePaymentMethods($user->id);
            
            return $this->success(
                ['methods' => $methods],
                'Payment methods retrieved successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Retrieving Payment Methods',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Select payment method
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function selectPaymentMethod(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'paymentMethodId' => 'required|string',
            'additionalData' => 'nullable|array'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid payment method information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        $paymentMethodId = $request->paymentMethodId;
        $additionalData = $request->additionalData;
        
        try {
            // Apply payment method to checkout
            $checkout = $this->checkoutService->selectPaymentMethod(
                $user->id,
                $paymentMethodId,
                $additionalData
            );
            
            return $this->success(
                $checkout,
                'Payment method selected successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Selecting Payment Method',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Update gift options
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateGiftOptions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'isGift' => 'required|boolean',
            'giftMessage' => 'nullable|string|max:500',
            'giftWrapping' => 'nullable|boolean',
            'giftWrappingCost' => 'nullable|numeric'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid gift information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        
        try {
            $checkout = $this->checkoutService->updateGiftOptions($user->id, $request->all());
            
            return $this->success(
                $checkout,
                'Gift options updated successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Updating Gift Options',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Validate checkout step
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateStep(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'step' => 'required|string|in:information,shipping,payment,review'
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid step information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = Auth::user();
        $step = $request->step;
        
        try {
            $result = $this->checkoutService->validateStep($user->id, $step);
            
            return $this->success(
                $result,
                $result['isValid'] ? 'Step validation successful' : 'Step validation failed',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Validating Step',
                $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Get address
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'address_id' => 'required|exists:addresses,id'
        ]);


        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid address information',
                $validator->errors()->messages(),
                400
            );
        }

        $addressId = $request->address_id;
        $user = Auth::user();
        
        try {
            $address = Address::findOrFail($addressId);

            $checkout = $this->checkoutService->setAddress($user->id, $addressId);
            
            return $this->success(
                $checkout,
                'Address set successfully',
                200
            );
        } catch (\Exception $e) {
            return $this->error(
                'Error Setting Address',
                $e->getMessage(),
                null,   
                500
            );
        }
    }
}
