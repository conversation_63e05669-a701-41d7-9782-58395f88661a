<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Customer\CustomerOfferResource;
use App\Services\OfferService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiPersonalizedOfferController extends ApiResponse
{
    protected $offerService;

    public function __construct(OfferService $offerService)
    {
        $this->offerService = $offerService;
    }

    /**
     * Get all offers with optional filters
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:50',
                'search' => 'string|max:255',
                'active' => 'boolean',
                'type' => 'string|in:discount,bogo,flash_sale,limited_time,bundle,product_recommendation',
                'exclusive' => 'boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $filters = $request->only(['search', 'active', 'type', 'exclusive']);
            $offers = $this->offerService->getOffers($filters);

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get offers by specific type
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function getOffersByType(Request $request, string $type): JsonResponse
    {
        try {
            $validator = Validator::make(['type' => $type], [
                'type' => 'required|string|in:discount,bogo,flash_sale,limited_time,bundle,product_recommendation'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Invalid offer type', 422);
            }
            
            $limit = $request->input('limit', 10);
            $offers = $this->offerService->getOffersByType($type, $limit);

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a specific offer by ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getOfferById(string $id): JsonResponse
    {
        try {
            $offer = $this->offerService->getOfferById($id);
            
            if (!$offer) {
                return $this->error('Offer not found', 'The requested offer does not exist', 404);
            }
            
            return $this->success(new CustomerOfferResource($offer));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get top priority offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTopPriorityOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:10'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            
            $limit = $request->input('limit', 3);
            $offers = $this->offerService->getTopPriorityOffers($limit);
                
            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get exclusive offers
     *
     * @return JsonResponse
     */
    public function getExclusiveOffers(): JsonResponse
    {
        try {
            $offers = $this->offerService->getExclusiveOffers();
            
            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Save an offer for a user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function saveOffer(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'offer_id' => 'required|string|exists:dropshipper_offers,offer_id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            
            $offerId = $request->input('offer_id');
            $userId = Auth::id();
            
            // Check if already saved
            $existingSaved = \DB::table('user_saved_offers')
                ->where('user_id', $userId)
                ->where('offer_id', $offerId)
                ->exists();
                
            if ($existingSaved) {
                return $this->success(['message' => 'Offer already saved']);
            }
            
            // Save the offer
            \DB::table('user_saved_offers')->insert([
                'user_id' => $userId,
                'offer_id' => $offerId,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return $this->success([
                'message' => 'Offer saved successfully'
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Unsave an offer for a user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function unsaveOffer(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'offer_id' => 'required|string|exists:dropshipper_offers,offer_id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            
            $offerId = $request->input('offer_id');
            $userId = Auth::id();
            
            // Delete the saved offer
            \DB::table('user_saved_offers')
                ->where('user_id', $userId)
                ->where('offer_id', $offerId)
                ->delete();
                
            return $this->success(['message' => 'Offer removed from saved offers']);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get saved offers for a user
     *
     * @return JsonResponse
     */
    public function getSavedOffers(): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            // Get saved offer IDs
            $savedOfferIds = \DB::table('user_saved_offers')
                ->where('user_id', $userId)
                ->pluck('offer_id')
                ->toArray();
                
            if (empty($savedOfferIds)) {
                return $this->success([]);
            }
            
            // Get offer details using the OfferService
            $offers = \DB::table('dropshipper_offers')
                ->whereIn('offer_id', $savedOfferIds)
                ->where('status', 'active')
                ->where('start_date', '<=', now())
                ->where(function($q) {
                    $q->where('end_date', '>=', now())
                      ->orWhereNull('end_date');
                })
                ->orderBy('priority', 'desc')
                ->get();
                
            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Check if an offer is valid
     *
     * @param string $id
     * @return JsonResponse
     */
    public function checkOfferValidity(string $id): JsonResponse
    {
        try {
            $offer = $this->offerService->getOfferById($id);
            
            if (!$offer) {
                return $this->error('Offer not found', 'The requested offer does not exist', 404);
            }
            
            $userId = Auth::id();
            $now = now();
            
            // Check if offer is active
            $isActive = $offer->status === 'active' &&
                $offer->start_date <= $now &&
                ($offer->end_date === null || $offer->end_date >= $now);
                
            // Check if user is in target group (if applicable)
            $isUserEligible = true;
            
            // Check if user has already used this offer
            $hasUsed = false;
            if ($userId && $offer->usage_limit > 0) {
                $hasUsed = $offer->used_count >= $offer->usage_limit;
            }
            
            $isValid = $isActive && $isUserEligible && !$hasUsed;
            
            return $this->success([
                'is_valid' => $isValid,
                'message' => $isValid ? 'Offer is valid' : 'Offer is not valid',
                'details' => [
                    'is_active' => $isActive,
                    'is_user_eligible' => $isUserEligible,
                    'has_used' => $hasUsed
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Apply an offer to a cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyOffer(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'offer_id' => 'required|string|exists:dropshipper_offers,offer_id',
                'cart_id' => 'string'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            
            $offerId = $request->input('offer_id');
            $cartId = $request->input('cart_id');
            $userId = Auth::id();
            
            // Get the cart
            $cart = null;
            if ($userId) {
                $cart = \DB::table('carts')
                    ->where('user_id', $userId)
                    ->first();
            } elseif ($cartId) {
                $cart = \DB::table('carts')
                    ->where('id', $cartId)
                    ->orWhere('temp_user_id', $cartId)
                    ->first();
            }
            
            if (!$cart) {
                return $this->error('Cart not found', 'Unable to find shopping cart', 404);
            }
            
            // Apply the offer to the cart
            $result = $this->offerService->applyOfferToCart($offerId, $cart);
            
            if (!$result['success']) {
                return $this->error('Apply failed', $result['message'], 400);
            }
            
            return $this->success([
                'message' => $result['message'],
                'discount_amount' => $result['discount_amount'],
                'offer' => new CustomerOfferResource($result['offer']),
                'cart_total' => $result['cart_total']
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get personalized offers for a product
     *
     * @param string $productId
     * @return JsonResponse
     */
    public function getOffersForProduct(string $productId): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            // Get offers for this product using OfferService
            $filters = [
                'product_id' => $productId,
                'user_id' => $userId
            ];
            
            $offers = $this->offerService->getOffersByFilter($filters);
            
            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get offer usage statistics
     *
     * @return JsonResponse
     */
    public function getOfferStats(): JsonResponse
    {
        try {
            $now = now();
            
            // Total active offers
            $totalActiveOffers = \DB::table('dropshipper_offers')
                ->where('status', 'active')
                ->where('start_date', '<=', $now)
                ->where(function($q) use ($now) {
                    $q->where('end_date', '>=', $now)
                      ->orWhereNull('end_date');
                })
                ->count();
                
            // Offers by discount type
            $offersByType = \DB::table('dropshipper_offers')
                ->selectRaw('discount_type, count(*) as count')
                ->where('status', 'active')
                ->groupBy('discount_type')
                ->get()
                ->mapWithKeys(function($item) {
                    return [$item->discount_type => $item->count];
                })
                ->toArray();
                
            // Exclusive offers count
            $exclusiveOffers = \DB::table('dropshipper_offers')
                ->where('status', 'active')
                ->where('is_exclusive', true)
                ->count();
                
            // Seasonal offers count
            $seasonalOffers = \DB::table('dropshipper_offers')
                ->where('status', 'active')
                ->where('is_seasonal', true)
                ->count();
                
            // Most claimed offers
            $mostClaimedOffers = \DB::table('dropshipper_offers')
                ->orderBy('used_count', 'desc')
                ->limit(5)
                ->get(['offer_id', 'title', 'used_count']);
                
            // Total savings from offers (from offer redemptions table if it exists)
            $totalSavings = 0;
            try {
                $totalSavings = \DB::table('offer_redemptions')
                    ->sum('discount_amount');
            } catch (\Exception $e) {
                // Table might not exist, use total savings_amount from offers
                $totalSavings = \DB::table('dropshipper_offers')
                    ->where('status', 'active')
                    ->sum('savings_amount');
            }
                
            return $this->success([
                'total_active_offers' => $totalActiveOffers,
                'offers_by_discount_type' => $offersByType,
                'exclusive_offers_count' => $exclusiveOffers,
                'seasonal_offers_count' => $seasonalOffers,
                'most_claimed_offers' => $mostClaimedOffers,
                'total_savings' => $totalSavings
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 