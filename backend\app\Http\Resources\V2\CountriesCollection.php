<?php

namespace App\Http\Resources\V2;

use Illuminate\Http\Resources\Json\ResourceCollection;

class CountriesCollection extends ResourceCollection
{
    public function toArray($request)
    {

          return  $this->collection->map(function($data) {
                return [
                    'id'      => (int) $data->id,
                    'code' => $data->code,
                    'name' => $data->name,
                    'status' => (int) $data->status,
                ];
            });

    }

    public function with($request)
    {
        return [
            'success' => true,
            'status' => 200
        ];
    }
}
