<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Resources\V3\User\UserResource;
use App\Models\User;
use App\Notifications\OTPEmailVerificationNotification;
use App\Rules\Recaptcha;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ApiRegisterController extends ApiResponse
{
    public function signup(Request $request)
    {
        $messages = array(
            'name.required' => translate('Name is required'),
            'email.required' => translate('Email is required') ,
            'email.email' => translate('Email must be a valid email address'),
            'email.unique' =>  translate('The email has already been taken') ,
            'password.required' => translate('Password is required'),
            'password.confirmed' => translate('Password confirmation does not match'),
            'password.min' => translate('Minimum 6 digits required for password')
        );
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'password' => 'required|min:6|confirmed',
            'email' => 'required|email|unique:users,email',
            'g-recaptcha-response' => [
                Rule::when(get_setting('google_recaptcha') == 1, ['required', new Recaptcha()], ['sometimes'])
            ]
        ], $messages);

        if ($validator->fails()) {
           return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        try {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->email_verification_token =  Str::uuid();
            $user->save();
            $user->notify(new OTPEmailVerificationNotification());
            $data = $this->generateAuthData($user);
            return $this->success($data,'Registration successful. Please verify your email.');
        } catch (\Throwable $th) {
            $user->delete();
            return $this->error(400,'Registration failed.', 'Registration failed.Please try again later.');
        }
    }

    public function confirmCode(Request $request)
    {
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $userTokenExpiry = Carbon::parse($user->verification_token_expire_at);
        if ($userTokenExpiry->isPast()) {
            return $this->error(400,'Verification Code Expired.Please Click on Resend verification email !');
        }
        $user->verification_token_expire_at = Null;
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
       return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function verifyOtp(Request $request)
    {
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
        return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function resendOtp(Request $request)
    {
        $messages = array(
            'email.required' => translate('Email is required'),
            'email.email' => translate('Email must be a valid email address')
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return $this->error(400,'User not found');
        }
        $user->notify(new OTPEmailVerificationNotification());
        return $this->success(null,'Verification code sent successfully');
    }
}
