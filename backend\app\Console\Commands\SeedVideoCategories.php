<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\VideoCategory;
use Illuminate\Support\Str;

class SeedVideoCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:video-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed video categories for the video upload feature';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Seeding video categories...');

        $categories = [
            'Product Demo',
            'How-to Guide',
            'Tutorial',
            'Review',
            'Unboxing',
            'Feature Spotlight',
            'Case Study',
            'Testimonial'
        ];

        foreach ($categories as $category) {
            VideoCategory::updateOrCreate(
                ['name' => $category],
                [
                    'slug' => Str::slug($category),
                    'description' => $category . ' videos',
                    'status' => 'active',
                    'sequence' => 0,
                ]
            );
            $this->info("Added category: {$category}");
        }

        $this->info('Video categories seeded successfully!');
        return 0;
    }
} 