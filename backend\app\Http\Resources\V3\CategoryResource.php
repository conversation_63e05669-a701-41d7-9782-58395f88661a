<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => (string) $this->id,
            'name' => (string)$this->name,
            'image' => uploaded_asset($this->banner),
            'icon' => uploaded_asset($this->icon),
            'cover_image' => uploaded_asset($this->cover_image),
            'slug' => $this->slug,
            'description' => $this->description,
            'parentId' => $this->parent_id ? (string) $this->parent_id : null,
            'isFeatured' => (bool) $this->featured,
            'displayOrder' => (int) $this->order_level,
            'productCount' => (int) $this->getProductCount()
        ];
    }
}
