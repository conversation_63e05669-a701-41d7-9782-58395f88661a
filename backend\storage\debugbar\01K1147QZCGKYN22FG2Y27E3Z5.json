{"__meta": {"id": "01K1147QZCGKYN22FG2Y27E3Z5", "datetime": "2025-07-25 08:46:41", "utime": **********.261285, "method": "GET", "uri": "/buzfi-new-backend/api/v3/brands/top", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.676058, "end": **********.261295, "duration": 0.5852370262145996, "duration_str": "585ms", "measures": [{"label": "Booting", "start": **********.676058, "relative_start": 0, "end": **********.857397, "relative_end": **********.857397, "duration": 0.*****************, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.85741, "relative_start": 0.***************, "end": **********.261297, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.868177, "relative_start": 0.*****************, "end": **********.871321, "relative_end": **********.871321, "duration": 0.0031440258026123047, "duration_str": "3.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.258322, "relative_start": 0.****************, "end": **********.258626, "relative_end": **********.258626, "duration": 0.0003039836883544922, "duration_str": "304μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.260007, "relative_start": 0.****************, "end": **********.260044, "relative_end": **********.260044, "duration": 3.719329833984375e-05, "duration_str": "37μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.25506, "accumulated_duration_str": "255ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wizard_infos` where `slug` = 'home-page-top-brands' and `wizard_info_status` = 1 order by `wizard_info_position` asc limit 1", "type": "query", "params": [], "bindings": ["home-page-top-brands", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.896985, "duration": 0.012060000000000001, "duration_str": "12.06ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 4.728}, {"sql": "select * from `wizard_details` where `wizard_details`.`wizard_info_id` in (10) order by `wizard_detail_position` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.912252, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 4.728, "width_percent": 0.208}, {"sql": "select * from `products` where `products`.`id` in (0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9138448, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 4.936, "width_percent": 0.223}, {"sql": "select * from `brands` where `brands`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9484231, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.16, "width_percent": 0.467}, {"sql": "select * from `brands` where `brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.95035, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.626, "width_percent": 0.082}, {"sql": "select * from `brands` where `brands`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.950957, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.708, "width_percent": 0.067}, {"sql": "select * from `brands` where `brands`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.951483, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.775, "width_percent": 0.063}, {"sql": "select * from `brands` where `brands`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9519958, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.838, "width_percent": 0.102}, {"sql": "select * from `brands` where `brands`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.952714, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.94, "width_percent": 0.082}, {"sql": "select * from `brands` where `brands`.`id` = 55 limit 1", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.953346, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.022, "width_percent": 0.082}, {"sql": "select * from `brands` where `brands`.`id` = 71 limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.954227, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.104, "width_percent": 0.125}, {"sql": "select * from `brands` where `brands`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.954941, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.23, "width_percent": 0.11}, {"sql": "select * from `brands` where `brands`.`id` = 57 limit 1", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.955611, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.34, "width_percent": 0.078}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 1 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.956527, "duration": 0.023690000000000003, "duration_str": "23.69ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 6.418, "width_percent": 9.288}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028386' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028386"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.981476, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 15.706, "width_percent": 0.267}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 3 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.030723, "duration": 0.023280000000000002, "duration_str": "23.28ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 15.973, "width_percent": 9.127}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028380' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028380"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.05481, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 25.1, "width_percent": 0.11}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 13 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.055752, "duration": 0.02357, "duration_str": "23.57ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 25.21, "width_percent": 9.241}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028381' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028381"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.080143, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 34.451, "width_percent": 0.118}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 14 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.08116, "duration": 0.02633, "duration_str": "26.33ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 34.568, "width_percent": 10.323}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028382' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028382"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1084359, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 44.891, "width_percent": 0.149}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 18 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.109553, "duration": 0.02345, "duration_str": "23.45ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 45.04, "width_percent": 9.194}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028383' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028383"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.133862, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 54.234, "width_percent": 0.11}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 34 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.134802, "duration": 0.022920000000000003, "duration_str": "22.92ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 54.344, "width_percent": 8.986}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028379' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028379"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.158504, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 63.33, "width_percent": 0.106}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 55 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1596289, "duration": 0.02271, "duration_str": "22.71ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 63.436, "width_percent": 8.904}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028384' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028384"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1830359, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 72.34, "width_percent": 0.078}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 71 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.183872, "duration": 0.02293, "duration_str": "22.93ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 72.418, "width_percent": 8.99}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028378' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028378"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.20768, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 81.408, "width_percent": 0.149}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 38 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.208861, "duration": 0.023329999999999997, "duration_str": "23.33ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 81.557, "width_percent": 9.147}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028450' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028450"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.233093, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 90.704, "width_percent": 0.165}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 57 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2343042, "duration": 0.023289999999999998, "duration_str": "23.29ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 90.869, "width_percent": 9.131}]}, "models": {"data": {"App\\Models\\WizardDetail": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardDetail.php&line=1", "ajax": false, "filename": "WizardDetail.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Upload": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\WizardInfo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardInfo.php&line=1", "ajax": false, "filename": "WizardInfo.php", "line": "?"}}}, "count": 30, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 30}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/brands/top", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands", "uri": "GET api/v3/brands/top", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=370\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=370\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:370-411</a>", "middleware": "api", "duration": "586ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1518368439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1518368439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1883745977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1883745977\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-521502626 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6ImZILzVzOVEwd1p1SlBBY3ZiU3pOcUE9PSIsInZhbHVlIjoiVzBLSWtBR1hWRklyNFN5WGh5THVxTnNQejRNcStBcFFyd2ZhOGFsdmdrT0NjSWRHMmxIQjdxdG5uSmt2bkU3VjJ2K1U2ZWUzcXhsM3lkZUp4bTVOMnU5blZLZDBNVXpITDUrUGdPelJNaEFsNlJYTE5IRk1LeEFCQUtLYUxmNHQiLCJtYWMiOiJkNzNkNDM5ZDNkNmNkZjk1OTM5ZTczOTY1YTg1MGM3OWRlZDNlNTkxOTI3YzI3M2VjMzEzOGJlN2I1NzA2ZDFhIiwidGFnIjoiIn0%3D; buzficom_session=Plk6E8BFz9LVNxSatgiHhgI7fCeJrimFjo05GTtu</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521502626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1098951600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1098951600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1828850814 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">max-age=604800, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-cache</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">MISS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">581</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjdjazQ1UmdLek9JSCtDem1VUXllb2c9PSIsInZhbHVlIjoiUmFlWktWY2lzUmkxV0JveG1HTlYvQ25nQmxJU1d2UkpBSnpnQkhSZ05ITFR4WXNJbERZT01EQ3lmcmZhV3FCUXBCUmZmYnZ6bnEyYXJOQWRYanhDeVBhckdiaFVmRXNXSHJReFJEdk5kaHZUQytIZnBZUjkvRUh4dXhsVWdJU00iLCJtYWMiOiJiYjRjMzdiMmFkZWM2NzQ5ZDkyZmQyZjdkMTAwNjY3ZGVjZTFiZGU0ZTdiN2FiMTU3Yjk5MjczOTZmZTk1NDc3IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6Inl0YTdEemQ2TWVoSHpYb1BoWmdHK0E9PSIsInZhbHVlIjoiQ0FLUjAwS3VrSm85c0lEY25JMDJOYmFvRTBRcXZXd0MxM2NnQkVrdk93Slc5ZEdKOVhxTXJDaFYwdXI2RHFWMlVXL0JXSDNkaFRVTERZVmxtTzQwNDh5ZlRoUHNuZ1VFL2tJTDljSTUzVHBEME5qZC8zT0dWdytGY3puLzdhMFciLCJtYWMiOiJiYThjOWZlZjJhNjMwNDI5NmRhMTI4MTk5YTMwOTQyYzY0ZDZlZDMzMDJiZTE1MDFmNjA1ZGRjOTlkYjRhODhhIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjdjazQ1UmdLek9JSCtDem1VUXllb2c9PSIsInZhbHVlIjoiUmFlWktWY2lzUmkxV0JveG1HTlYvQ25nQmxJU1d2UkpBSnpnQkhSZ05ITFR4WXNJbERZT01EQ3lmcmZhV3FCUXBCUmZmYnZ6bnEyYXJOQWRYanhDeVBhckdiaFVmRXNXSHJReFJEdk5kaHZUQytIZnBZUjkvRUh4dXhsVWdJU00iLCJtYWMiOiJiYjRjMzdiMmFkZWM2NzQ5ZDkyZmQyZjdkMTAwNjY3ZGVjZTFiZGU0ZTdiN2FiMTU3Yjk5MjczOTZmZTk1NDc3IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6Inl0YTdEemQ2TWVoSHpYb1BoWmdHK0E9PSIsInZhbHVlIjoiQ0FLUjAwS3VrSm85c0lEY25JMDJOYmFvRTBRcXZXd0MxM2NnQkVrdk93Slc5ZEdKOVhxTXJDaFYwdXI2RHFWMlVXL0JXSDNkaFRVTERZVmxtTzQwNDh5ZlRoUHNuZ1VFL2tJTDljSTUzVHBEME5qZC8zT0dWdytGY3puLzdhMFciLCJtYWMiOiJiYThjOWZlZjJhNjMwNDI5NmRhMTI4MTk5YTMwOTQyYzY0ZDZlZDMzMDJiZTE1MDFmNjA1ZGRjOTlkYjRhODhhIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828850814\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-328878535 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://localhost/buzfi-new-backend/api/v3/brands/top</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328878535\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/brands/top", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands"}, "badge": null}}