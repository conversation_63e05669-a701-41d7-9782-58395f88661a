<?php

namespace App\Http\Resources\V3\Coupon;

use Illuminate\Http\Resources\Json\JsonResource;

class FeaturedCouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Parse details JSON
        $details = json_decode($this->details, true) ?? [];

        return [
            'id' => (string) $this->id,
            'code' => $this->code,
            'type' => $this->discount_type == 'percent' ? 'percentage' : 'fixed',
            'value' => (float) $this->discount,
            'minPurchase' => isset($details['min_buy']) ? (float) $details['min_buy'] : null,
            'maxDiscount' => isset($details['max_discount']) ? (float) $details['max_discount'] : null,
            'description' => $this->getDescription(),
            'startDate' => date('Y-m-d\TH:i:s\Z', $this->start_date),
            'endDate' => date('Y-m-d\TH:i:s\Z', $this->end_date),
            'status' => $this->isActive() ? 'active' : 'inactive',
            'image' => $this->image ? uploaded_asset($this->image) : null
        ];
    }
}
