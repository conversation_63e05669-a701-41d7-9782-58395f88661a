<?php

namespace App\Http\Controllers\Api\V2;

use App\Models\ClubPoint;
use App\Http\Resources\V2\RefundRequestCollection;
use App\Models\OrderDetail;
use App\Models\RefundRequest;
use App\Models\ReturnRequest;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;

class RetrunRequestController extends Controller
{
    public function send(Request $request)
    {
        $order_detail = OrderDetail::where('id', $request->id)->first();
        $return = new ReturnRequest;
        $return->user_id = auth()->user()->id;
        $return->order_id = $order_detail->order_id;
        $return->order_detail_id = $order_detail->id;
        $return->seller_id = $order_detail->seller_id;
        $return->seller_approval = 0;
        $return->reason = $request->reason;
        $return->admin_approval = 0;
        $return->admin_seen = 0;
        $return->return_amount = $order_detail->price + $order_detail->tax;
        $return->return_status = 0;
        OrderDetail::where('id', $request->id)->update(['return_status' => 3]);
        $return->save();
        return response()->json([
            'success' => true,
            'message' => translate('Request Sent')
        ]);
    }
}
