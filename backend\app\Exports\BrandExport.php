<?php

namespace App\Exports;

use App\Models\Brand;
use App\Models\Upload;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\Log;

class BrandExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        $brands = Brand::with([
            'user',
        ])->get();

        $data = [];

        foreach ($brands as $brand) {
            $userName = $brand->user ? $brand->user->name : '';
            $brandData = [
                'ID' => $brand->id,
                'Name' => $brand->name,
                'Logo' => $brand->logo,
                'Top' => $brand->top,
                'Slug' => $brand->slug,
                'Meta Title' => $brand->meta_title,
                'Meta Description' => $brand->meta_description,
                'Added By' => $userName,
                'Lang' => $brand->brand_translations[0]->lang,
            ];
            $data[] = $brandData;
        }
        return collect($data);
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Logo',
            'Top',
            'Slug',
            'Meta Title',
            'Meta Description',
            'Added By',
            'Lang',
        ];
    }
}
