<?php

namespace App\Console\Commands;

use App\Models\EmailVerify;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestEmailVerification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email-verification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email verification and auto-login functionality';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing Email Verification and Auto-Login Functionality');
        $this->info('=======================================================');

        // Test 1: Customer Registration with Auto-Login
        $this->info('Test 1: Customer Registration with Auto-Login');
        $this->testCustomerRegistration();

        // Test 2: Seller Registration with Auto-Login
        $this->info('Test 2: Seller Registration with Auto-Login');
        $this->testSellerRegistration();

        // Test 3: Dropshipper Registration with Auto-Login
        $this->info('Test 3: Dropshipper Registration with Auto-Login');
        $this->testDropshipperRegistration();

        // Test 4: Registration without Email Verification
        $this->info('Test 4: Registration without Email Verification');
        $this->testRegistrationWithoutVerification();

        $this->info('All tests completed!');
        return 0;
    }

    private function testCustomerRegistration()
    {
        // Create email verification record
        $email = '<EMAIL>';
        EmailVerify::updateOrCreate(
            ['email' => $email],
            [
                'otp' => '123456',
                'expires_at' => now()->addMinutes(10),
                'verified_at' => now(),
                'user_type' => 'customer'
            ]
        );

        $userData = [
            'name' => 'Test Customer',
            'email' => $email,
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer',
            'temp_user_id' => 'temp_customer_123'
        ];

        try {
            $response = Http::post(config('app.url') . '/api/v3/auth/register', $userData);
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info('✓ Customer registration successful');
                $this->info('  - User ID: ' . $data['data']['user']['id']);
                $this->info('  - Token provided: ' . (isset($data['data']['token']['access_token']) ? 'Yes' : 'No'));
                $this->info('  - Message: ' . $data['message']);
                
                // Verify user in database
                $user = User::where('email', $email)->first();
                if ($user && $user->email_verified_at) {
                    $this->info('  - Email verified in database: Yes');
                } else {
                    $this->error('  - Email verified in database: No');
                }
            } else {
                $this->error('✗ Customer registration failed');
                $this->error('  Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('✗ Customer registration error: ' . $e->getMessage());
        }

        $this->info('');
    }

    private function testSellerRegistration()
    {
        // Create email verification record
        $email = '<EMAIL>';
        EmailVerify::updateOrCreate(
            ['email' => $email],
            [
                'otp' => '123456',
                'expires_at' => now()->addMinutes(10),
                'verified_at' => now(),
                'user_type' => 'seller'
            ]
        );

        $sellerData = [
            'name' => 'Test Seller',
            'email' => $email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'business_name' => 'Test Business',
            'business_address' => [
                'street' => '123 Main St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zipCode' => '12345',
                'country' => 'Test Country'
            ],
            'tax_id' => 'TAX123',
            'business_type' => 'LLC',
            'website' => 'https://example.com',
            'category' => 'Electronics',
            'temp_user_id' => 'temp_seller_456'
        ];

        try {
            $response = Http::post(config('app.url') . '/api/v3/auth/register/seller', $sellerData);
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info('✓ Seller registration successful');
                $this->info('  - User ID: ' . $data['data']['user']['id']);
                $this->info('  - Seller profile created: ' . (isset($data['data']['seller']) ? 'Yes' : 'No'));
                $this->info('  - Token provided: ' . (isset($data['data']['token']['access_token']) ? 'Yes' : 'No'));
                $this->info('  - Message: ' . $data['message']);
            } else {
                $this->error('✗ Seller registration failed');
                $this->error('  Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('✗ Seller registration error: ' . $e->getMessage());
        }

        $this->info('');
    }

    private function testDropshipperRegistration()
    {
        // Create email verification record
        $email = '<EMAIL>';
        EmailVerify::updateOrCreate(
            ['email' => $email],
            [
                'otp' => '123456',
                'expires_at' => now()->addMinutes(10),
                'verified_at' => now(),
                'user_type' => 'dropshipper'
            ]
        );

        $dropshipperData = [
            'name' => 'Test Dropshipper',
            'email' => $email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+1234567890',
            'company_name' => 'Test Company',
            'business_address' => [
                'street' => '123 Business St',
                'city' => 'Business City',
                'state' => 'Business State',
                'zipCode' => '12345',
                'country' => 'Business Country'
            ],
            'shipping_address' => [
                'street' => '456 Shipping St',
                'city' => 'Shipping City',
                'state' => 'Shipping State',
                'zipCode' => '67890',
                'country' => 'Shipping Country'
            ],
            'tax_id' => 'TAX456',
            'business_type' => 'Corporation',
            'website' => 'https://dropshipper.com',
            'user_type' => 'b2b',
            'temp_user_id' => 'temp_dropshipper_789'
        ];

        try {
            $response = Http::post(config('app.url') . '/api/v3/auth/register/dropshipper', $dropshipperData);
            
            if ($response->successful()) {
                $data = $response->json();
                $this->info('✓ Dropshipper registration successful');
                $this->info('  - User ID: ' . $data['data']['user']['id']);
                $this->info('  - Dropshipper profile created: ' . (isset($data['data']['dropshipper']) ? 'Yes' : 'No'));
                $this->info('  - Token provided: ' . (isset($data['data']['token']['access_token']) ? 'Yes' : 'No'));
                $this->info('  - Message: ' . $data['message']);
            } else {
                $this->error('✗ Dropshipper registration failed');
                $this->error('  Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('✗ Dropshipper registration error: ' . $e->getMessage());
        }

        $this->info('');
    }

    private function testRegistrationWithoutVerification()
    {
        $userData = [
            'name' => 'Unverified User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'confirmPassword' => 'password123',
            'user_type' => 'customer'
        ];

        try {
            $response = Http::post(config('app.url') . '/api/v3/auth/register', $userData);
            
            if ($response->status() === 403) {
                $data = $response->json();
                $this->info('✓ Registration correctly blocked without email verification');
                $this->info('  - Message: ' . $data['message']);
            } else {
                $this->error('✗ Registration should have been blocked without email verification');
                $this->error('  Response: ' . $response->body());
            }
        } catch (\Exception $e) {
            $this->error('✗ Test error: ' . $e->getMessage());
        }

        $this->info('');
    }
}