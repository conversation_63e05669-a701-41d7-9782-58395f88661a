<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleStripeCookies
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only apply to Stripe-related routes
        if ($this->isStripeRoute($request)) {
            // Set secure headers for Stripe integration
            $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
            $response->headers->set('X-Content-Type-Options', 'nosniff');
            $response->headers->set('X-Frame-Options', 'DENY');
            
            // Set SameSite=None for Stripe cookies in production
            if (config('app.env') === 'production') {
                $response->headers->set('Set-Cookie', 
                    $response->headers->get('Set-Cookie') . '; SameSite=None; Secure'
                );
            }
        }

        return $response;
    }

    /**
     * Check if the current route is Stripe-related
     */
    private function isStripeRoute(Request $request): bool
    {
        $stripeRoutes = [
            '/stripe',
            '/api/stripe',
            '/api/v2/stripe',
            '/api/v3/stripe',
            '/payment/stripe',
            '/checkout/stripe'
        ];

        $path = $request->path();
        
        foreach ($stripeRoutes as $route) {
            if (str_starts_with($path, trim($route, '/'))) {
                return true;
            }
        }

        return false;
    }
} 