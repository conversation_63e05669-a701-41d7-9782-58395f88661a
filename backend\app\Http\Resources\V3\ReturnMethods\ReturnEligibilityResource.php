<?php

namespace App\Http\Resources\V3\ReturnMethods;

use Illuminate\Http\Resources\Json\JsonResource;

class ReturnEligibilityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $eligibleItems = collect($this['items'])->filter(function ($item) {
            return $item['eligible'];
        });
        
        $deadline = $this['deadline'] ?? null;
        $deadlineFormatted = $deadline ? date('F j, Y', strtotime($deadline)) : null;
        
        return [
            'eligible' => $this['eligible'],
            'deadline' => $this['deadline'],
            'items' => $this['items'],
            'message' => $this['eligible'] 
                ? "Some items in this order are eligible for return until {$deadlineFormatted}."
                : "This order is not eligible for return."
        ];
    }
}
