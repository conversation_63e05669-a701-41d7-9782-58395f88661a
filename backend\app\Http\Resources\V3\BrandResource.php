<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class BrandResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => (string) $this->slug,
            'name' => (string) $this->name,
            'image' => (string) uploaded_asset($this->logo),
            'slug' => (string)$this->slug,
            'url' => (string)$this->slug,
            'description' =>(string) $this->description,
            'isFeatured' => (bool) $this->featured,
            'displayOrder' => (int) $this->order_level,
            'productCount' => (int) $this->getProductCount()
        ];
    }
}
