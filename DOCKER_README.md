# Docker Setup for Laravel Backend

This project uses **<PERSON><PERSON>** to provide a complete Docker development environment for your Laravel backend with PHP 8.2, MySQL, Nginx, phpMyAdmin, and Composer.

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Git (for cloning if needed)

### Starting the Environment

1. **Make the startup script executable:**
   ```bash
   chmod +x docker-start.sh
   ```

2. **Start the Docker containers:**
   ```bash
   ./docker-start.sh
   ```

   Or manually:
   ```bash
   cd laradock
   docker-compose up -d nginx mysql phpmyadmin workspace
   ```

## 🌐 Access Points

| Service | URL | Port |
|---------|-----|------|
| Laravel Application | http://localhost | 80 |
| phpMyAdmin | http://localhost:8081 | 8081 |
| MySQL Database | localhost | 3306 |

## 🔧 Configuration

### Current Setup
- **PHP Version:** 8.2
- **Web Server:** Nginx
- **Database:** MySQL 8.4
- **Database Management:** phpMyAdmin

### Database Credentials
```
Host: mysql (from containers) / localhost (from host)
Database: default
Username: default
Password: secret
Root Password: root
Port: 3306
```

### Laravel .env Configuration
Update your `backend/.env` file with these database settings:
```env
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=default
DB_USERNAME=default
DB_PASSWORD=secret
```

## 💻 Development Workflow

### Accessing the Workspace Container
The workspace container includes PHP 8.2, Composer, Node.js, and other development tools:

```bash
# Enter the workspace container
docker-compose exec workspace bash

# Inside the container, navigate to your project
cd /var/www/backend

# Run Laravel commands
php artisan migrate
php artisan serve
composer install
npm install
```

### Common Commands

#### Laravel Commands
```bash
# Install dependencies
docker-compose exec workspace composer install

# Run migrations
docker-compose exec workspace php artisan migrate

# Generate application key
docker-compose exec workspace php artisan key:generate

# Clear cache
docker-compose exec workspace php artisan cache:clear
docker-compose exec workspace php artisan config:clear
docker-compose exec workspace php artisan route:clear
```

#### Database Operations
```bash
# Create a new migration
docker-compose exec workspace php artisan make:migration create_example_table

# Seed the database
docker-compose exec workspace php artisan db:seed

# Fresh migration with seeding
docker-compose exec workspace php artisan migrate:fresh --seed
```

## 📁 Project Structure

```
buzfi-docker/
├── backend/                 # Your Laravel application
├── frontend/               # Your frontend application (if any)
├── laradock/              # Docker configuration
│   ├── .env               # Laradock environment variables
│   ├── docker-compose.yml # Docker services configuration
│   └── nginx/sites/       # Nginx site configurations
├── docker-start.sh        # Quick start script
└── DOCKER_README.md       # This file
```

## 🛠 Customization

### Adding More Services
To add additional services (Redis, Elasticsearch, etc.), modify the docker-compose command:

```bash
docker-compose up -d nginx mysql phpmyadmin workspace redis elasticsearch
```

### Changing PHP Version
Edit `laradock/.env` and change:
```env
PHP_VERSION=8.2
```

### Custom Nginx Configuration
Edit files in `laradock/nginx/sites/` to customize your web server configuration.

## 🔄 Container Management

### Start Services
```bash
cd laradock
docker-compose up -d nginx mysql phpmyadmin workspace
```

### Stop Services
```bash
cd laradock
docker-compose down
```

### Restart Services
```bash
cd laradock
docker-compose restart
```

### View Logs
```bash
cd laradock
docker-compose logs -f nginx
docker-compose logs -f mysql
docker-compose logs -f php-fpm
```

### Check Container Status
```bash
cd laradock
docker-compose ps
```

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Stop other web servers (Apache, Nginx) running on your host
   - Or change ports in `laradock/.env`

2. **Permission Issues**
   - Ensure your user has Docker permissions
   - On Linux/Mac: `sudo usermod -aG docker $USER`

3. **Database Connection Issues**
   - Ensure MySQL container is running: `docker-compose ps`
   - Check database credentials in your Laravel `.env` file
   - Wait a few seconds after starting containers for MySQL to initialize

4. **Composer Issues**
   - Clear Composer cache: `docker-compose exec workspace composer clear-cache`
   - Update Composer: `docker-compose exec workspace composer self-update`

### Reset Everything
If you encounter persistent issues:
```bash
cd laradock
docker-compose down -v  # This will remove volumes (data will be lost!)
docker-compose up -d nginx mysql phpmyadmin workspace
```

## 📚 Additional Resources

- [Laradock Documentation](https://laradock.io/)
- [Laravel Documentation](https://laravel.com/docs)
- [Docker Documentation](https://docs.docker.com/)

## 🎯 Next Steps

1. Start the containers using `./docker-start.sh`
2. Access your Laravel app at http://localhost
3. Configure your database using phpMyAdmin at http://localhost:8081
4. Begin developing your Laravel application in the `backend/` folder
