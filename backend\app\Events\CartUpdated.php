<?php

namespace App\Events;

use App\Models\CartInfo;
use App\Models\Cart;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CartUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $cartInfo;
    public $cartItem;
    public $action;
    public $timestamp;

    /**
     * Create a new event instance.
     *
     * @param CartInfo $cartInfo
     * @param Cart|null $cartItem
     * @param string $action
     */
    public function __construct(CartInfo $cartInfo, $cartItem = null, $action = 'updated')
    {
        $this->cartInfo = $cartInfo;
        $this->cartItem = $cartItem;
        $this->action = $action;
        $this->timestamp = now();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [];
        
        // Broadcast to user channel if authenticated user
        if ($this->cartInfo->user_id) {
            $channels[] = new PrivateChannel('user.' . $this->cartInfo->user_id . '.cart');
        }
        
        // Broadcast to guest channel if temp user
        if ($this->cartInfo->temp_user_id) {
            $channels[] = new Channel('guest.' . $this->cartInfo->temp_user_id . '.cart');
        }
        
        return $channels;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'cart_id' => $this->cartInfo->id,
            'action' => $this->action,
            'item_count' => $this->cartInfo->item_count,
            'total_quantity' => $this->cartInfo->total_quantity,
            'subtotal' => $this->cartInfo->subtotal,
            'total' => $this->cartInfo->total,
            'currency' => $this->cartInfo->currency,
            'item' => $this->cartItem ? [
                'id' => $this->cartItem->id,
                'product_id' => $this->cartItem->product_id,
                'quantity' => $this->cartItem->quantity,
                'price' => $this->cartItem->price,
                'variation' => $this->cartItem->variation
            ] : null,
            'timestamp' => $this->timestamp->toISOString()
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'cart.updated';
    }
} 