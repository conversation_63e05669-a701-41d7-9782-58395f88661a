<?php

namespace App\Http\Resources\V3;

use App\Services\SavingsTrackerService;
use Illuminate\Http\Resources\Json\JsonResource;

class SavingsGoalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $savingsTrackerService = new SavingsTrackerService();
        return [
            'id' => $this->id,
            'goalAmount' => (float) $this->goal_amount,
            'currentAmount' =>  $savingsTrackerService->getSavingsForSpecificPeriod($this->start_date->format('Y-m-d'), $this->target_date->format('Y-m-d')),
            'startDate' => $this->start_date->format('Y-m-d'),
            'targetDate' => $this->target_date->format('Y-m-d'),
            'progress' => $this->progress,
            'status' => $this->status_text,
        ];
    }
}
