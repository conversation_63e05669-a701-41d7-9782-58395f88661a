#!/bin/bash

# Laravel Setup Script for Docker Environment
# This script helps configure your Laravel application for the Docker environment

echo "🔧 Setting up <PERSON><PERSON> for Docker environment..."

# Check if we're in the right directory
if [ ! -f "backend/artisan" ]; then
    echo "❌ Error: Laravel project not found in backend/ directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Navigate to backend directory
cd backend

echo "📝 Updating Laravel .env file for Docker..."

# Backup existing .env file
if [ -f ".env" ]; then
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
    echo "✅ Backed up existing .env file"
fi

# Create .env from .env.example if it doesn't exist
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "✅ Created .env from .env.example"
    else
        echo "❌ Error: No .env or .env.example file found"
        exit 1
    fi
fi

# Update database configuration for Docker
echo "🔄 Updating database configuration..."

# Use sed to update database settings
sed -i.bak 's/DB_HOST=.*/DB_HOST=mysql/' .env
sed -i.bak 's/DB_PORT=.*/DB_PORT=3306/' .env
sed -i.bak 's/DB_DATABASE=.*/DB_DATABASE=default/' .env
sed -i.bak 's/DB_USERNAME=.*/DB_USERNAME=default/' .env
sed -i.bak 's/DB_PASSWORD=.*/DB_PASSWORD=secret/' .env

# Update cache and session drivers for Docker
sed -i.bak 's/CACHE_DRIVER=.*/CACHE_DRIVER=file/' .env
sed -i.bak 's/SESSION_DRIVER=.*/SESSION_DRIVER=file/' .env

# Update app URL
sed -i.bak 's|APP_URL=.*|APP_URL=http://localhost|' .env

echo "✅ Updated .env file with Docker configuration"

# Remove backup files created by sed
rm -f .env.bak

echo ""
echo "🚀 Laravel is now configured for Docker!"
echo ""
echo "📋 Database Configuration:"
echo "   DB_HOST=mysql"
echo "   DB_PORT=3306"
echo "   DB_DATABASE=default"
echo "   DB_USERNAME=default"
echo "   DB_PASSWORD=secret"
echo ""
echo "🔧 Next steps:"
echo "1. Start Docker containers: ./docker-start.sh"
echo "2. Install dependencies: docker-compose exec workspace composer install"
echo "3. Generate app key: docker-compose exec workspace php artisan key:generate"
echo "4. Run migrations: docker-compose exec workspace php artisan migrate"
echo ""
echo "🌐 Access your app at: http://localhost"
