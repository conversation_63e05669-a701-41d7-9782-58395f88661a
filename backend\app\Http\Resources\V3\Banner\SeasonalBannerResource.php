<?php

namespace App\Http\Resources\V3\Banner;

use Illuminate\Http\Resources\Json\JsonResource;

class SeasonalBannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'description' => $this->description,
            'season_type' => $this->season_type,
            'image_url' => $this->image_url,
            'mobile_image_url' => $this->mobile_image_url,
            'background_color' => $this->background_color,
            'text_color' => $this->text_color,
            'button_text' => $this->button_text,
            'button_url' => $this->button_url,
            'overlay_opacity' => $this->overlay_opacity,
            'start_date' => isset($this->start_date) ? $this->start_date->toIso8601String() : null,
            'end_date' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'is_active' => (bool) $this->is_active,
            'display_order' => $this->display_order,
            'section_position' => $this->section_position,
            'show_countdown' => (bool) $this->show_countdown,
            'countdown_type' => $this->countdown_type,
            'countdown_background' => $this->countdown_background,
            'countdown_text_color' => $this->countdown_text_color,
            'time_remaining' => $this->when($this->show_countdown && $this->isActive() && $this->end_date, function() {
                return $this->getTimeRemaining();
            }),
            'is_upcoming' => $this->isUpcoming(),
            'campaign' => $this->when($this->relationLoaded('campaign'), function() {
                return [
                    'id' => $this->campaign->id,
                    'title' => $this->campaign->title,
                    'type' => $this->campaign->type,
                ];
            }),
            'metadata' => $this->metadata,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 