{"__meta": {"id": "01K1147TZVDPSH7KKFPSNCZX0R", "datetime": "2025-07-25 08:46:44", "utime": **********.348204, "method": "GET", "uri": "/buzfi-new-backend/api/v3/product-by-category-slug?category_slug=grocery-essentials&page=1&limit=6", "ip": "::1"}, "messages": {"count": 20, "messages": [{"message": "[08:46:43] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 32002", "message_html": null, "is_string": false, "label": "info", "time": **********.939415, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:43] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 32002", "message_html": null, "is_string": false, "label": "info", "time": **********.939532, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31985", "message_html": null, "is_string": false, "label": "info", "time": **********.024058, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31985", "message_html": null, "is_string": false, "label": "info", "time": **********.024149, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31982", "message_html": null, "is_string": false, "label": "info", "time": **********.063213, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31982", "message_html": null, "is_string": false, "label": "info", "time": **********.063284, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31981", "message_html": null, "is_string": false, "label": "info", "time": **********.099655, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31981", "message_html": null, "is_string": false, "label": "info", "time": **********.099727, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31964", "message_html": null, "is_string": false, "label": "info", "time": **********.135633, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31964", "message_html": null, "is_string": false, "label": "info", "time": **********.135702, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31957", "message_html": null, "is_string": false, "label": "info", "time": **********.172245, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31957", "message_html": null, "is_string": false, "label": "info", "time": **********.172319, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31954", "message_html": null, "is_string": false, "label": "info", "time": **********.213618, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31954", "message_html": null, "is_string": false, "label": "info", "time": **********.21369, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31950", "message_html": null, "is_string": false, "label": "info", "time": **********.247847, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31950", "message_html": null, "is_string": false, "label": "info", "time": **********.247937, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.280199, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.280286, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.312077, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:44] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.312164, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.658882, "end": **********.34823, "duration": 0.6893479824066162, "duration_str": "689ms", "measures": [{"label": "Booting", "start": **********.658882, "relative_start": 0, "end": **********.80436, "relative_end": **********.80436, "duration": 0.****************, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.804365, "relative_start": 0.*****************, "end": **********.348231, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "544ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.812042, "relative_start": 0.*****************, "end": **********.814602, "relative_end": **********.814602, "duration": 0.0025599002838134766, "duration_str": "2.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.345599, "relative_start": 0.****************, "end": **********.345847, "relative_end": **********.345847, "duration": 0.000247955322265625, "duration_str": "248μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.347124, "relative_start": 0.****************, "end": **********.347157, "relative_end": **********.347157, "duration": 3.2901763916015625e-05, "duration_str": "33μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 157, "nb_statements": 157, "nb_visible_statements": 157, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.36185000000000006, "accumulated_duration_str": "362ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 57 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select count(*) as aggregate from `categories` where `slug` = 'grocery-essentials'", "type": "query", "params": [], "bindings": ["grocery-essentials"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 903}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 874}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.831001, "duration": 0.02283, "duration_str": "22.83ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 6.309}, {"sql": "select * from `categories` where `slug` = 'grocery-essentials' limit 1", "type": "query", "params": [], "bindings": ["grocery-essentials"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 336}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.855137, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:336", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 336}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=336", "ajax": false, "filename": "ApiHomePageController.php", "line": "336"}, "connection": "buzfi", "explain": null, "start_percent": 6.309, "width_percent": 0.077}, {"sql": "select count(*) as aggregate from `products` where `category_id` = 10 and `published` = 1 and `digital` = 0 and `published` = '1' and `auction_product` = 0 and `approved` = '1' and `wholesale_product` = 0 and (`added_by` = 'admin' or (`user_id` in (3, 13, 86, 66, 136, 23, 144, 809, 919)))", "type": "query", "params": [], "bindings": [10, 1, 0, "1", 0, "1", 0, "admin", 3, 13, 86, 66, 136, 23, 144, 809, 919], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 349}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.887417, "duration": 0.03126, "duration_str": "31.26ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:349", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=349", "ajax": false, "filename": "ApiHomePageController.php", "line": "349"}, "connection": "buzfi", "explain": null, "start_percent": 6.387, "width_percent": 8.639}, {"sql": "select * from `products` where `category_id` = 10 and `published` = 1 and `digital` = 0 and `published` = '1' and `auction_product` = 0 and `approved` = '1' and `wholesale_product` = 0 and (`added_by` = 'admin' or (`user_id` in (3, 13, 86, 66, 136, 23, 144, 809, 919))) order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [10, 1, 0, "1", 0, "1", 0, "admin", 3, 13, 86, 66, 136, 23, 144, 809, 919], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9194891, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:355", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=355", "ajax": false, "filename": "ApiHomePageController.php", "line": "355"}, "connection": "buzfi", "explain": null, "start_percent": 15.026, "width_percent": 1.34}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (31937, 31938, 31950, 31954, 31957, 31964, 31981, 31982, 31985, 32002)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9264772, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:355", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=355", "ajax": false, "filename": "ApiHomePageController.php", "line": "355"}, "connection": "buzfi", "explain": null, "start_percent": 16.366, "width_percent": 0.608}, {"sql": "select * from `product_taxes` where `product_taxes`.`product_id` in (31937, 31938, 31950, 31954, 31957, 31964, 31981, 31982, 31985, 32002)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9297152, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:355", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=355", "ajax": false, "filename": "ApiHomePageController.php", "line": "355"}, "connection": "buzfi", "explain": null, "start_percent": 16.974, "width_percent": 0.608}, {"sql": "select * from `uploads` where `uploads`.`id` in (********, ********, ********, ********, ********, ********, ********, ********, ********) and `uploads`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9332922, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:355", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=355", "ajax": false, "filename": "ApiHomePageController.php", "line": "355"}, "connection": "buzfi", "explain": null, "start_percent": 17.582, "width_percent": 0.122}, {"sql": "select * from `suppliers` where `suppliers`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 335}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9344919, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:355", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=355", "ajax": false, "filename": "ApiHomePageController.php", "line": "355"}, "connection": "buzfi", "explain": null, "start_percent": 17.703, "width_percent": 0.064}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.937675, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 17.767, "width_percent": 0.061}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 32002 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [32002, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.940474, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 17.828, "width_percent": 0.102}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 32002 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [32002], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9414139, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 17.93, "width_percent": 1.559}, {"sql": "select * from `brands` where `brands`.`id` = 240 limit 1", "type": "query", "params": [], "bindings": [240], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.947598, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 19.489, "width_percent": 0.061}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 240 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [240], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.94837, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 19.55, "width_percent": 0.144}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9495609, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 19.693, "width_percent": 0.055}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026216' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026216"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.991889, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 19.749, "width_percent": 0.166}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026215' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026215"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9933019, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 19.914, "width_percent": 0.058}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 32002 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 32002], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.99472, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 19.972, "width_percent": 0.102}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 32002 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [32002], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.995724, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 20.075, "width_percent": 1.525}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 32002 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [32002], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.001689, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 21.6, "width_percent": 1.528}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 32002 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [32002], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.007726, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 23.128, "width_percent": 1.542}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 32002 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [32002, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.013769, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 24.67, "width_percent": 0.069}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 32002 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [32002], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.014503, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 24.74, "width_percent": 1.498}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.020618, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 26.237, "width_percent": 0.069}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.021301, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 26.306, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0219748, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 26.356, "width_percent": 0.069}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.022831, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 26.425, "width_percent": 0.097}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31985 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31985, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0243838, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 26.522, "width_percent": 0.069}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31985 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31985], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0251322, "duration": 0.00554, "duration_str": "5.54ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 26.591, "width_percent": 1.531}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026104' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026104"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.031497, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 28.122, "width_percent": 0.053}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026105' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026105"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.032239, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 28.175, "width_percent": 0.044}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026108' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026108"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.032917, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 28.219, "width_percent": 0.044}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026106' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026106"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.033639, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 28.263, "width_percent": 0.047}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31985 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31985], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.0342948, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 28.31, "width_percent": 0.061}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31985 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31985], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.034996, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 28.371, "width_percent": 1.542}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31985 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31985], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.041077, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 29.913, "width_percent": 1.567}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31985 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31985], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.047326, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 31.48, "width_percent": 1.501}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31985 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31985, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.053314, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 32.981, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31985 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31985], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0539532, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 33.039, "width_percent": 1.581}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0604649, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 34.619, "width_percent": 0.064}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.061131, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 34.683, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0616992, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 34.733, "width_percent": 0.039}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.0623379, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 34.771, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31982 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31982, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0635, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 34.821, "width_percent": 0.053}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31982 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31982], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.064091, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 34.874, "width_percent": 1.49}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026080' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026080"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.070682, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 36.363, "width_percent": 0.058}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31982 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31982], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.071452, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 36.421, "width_percent": 0.066}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31982 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31982], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0722349, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 36.487, "width_percent": 1.512}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31982 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31982], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0781388, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 37.999, "width_percent": 1.52}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31982 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31982], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.084061, "duration": 0.0056500000000000005, "duration_str": "5.65ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 39.519, "width_percent": 1.561}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31982 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31982, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.090245, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 41.081, "width_percent": 0.075}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31982 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31982], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.091024, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 41.155, "width_percent": 1.509}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.096979, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 42.664, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.09758, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 42.714, "width_percent": 0.041}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.098119, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 42.755, "width_percent": 0.041}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.098777, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 42.797, "width_percent": 0.055}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31981 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31981, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.099942, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 42.852, "width_percent": 0.061}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31981 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31981], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.100625, "duration": 0.005809999999999999, "duration_str": "5.81ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 42.913, "width_percent": 1.606}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31981 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31981], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.107668, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 44.518, "width_percent": 0.099}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31981 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31981], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.108643, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 44.618, "width_percent": 1.495}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31981 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31981], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.114462, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 46.113, "width_percent": 1.525}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31981 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31981], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1204028, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 47.639, "width_percent": 1.525}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31981 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31981, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.12652, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 49.164, "width_percent": 0.053}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31981 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31981], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.127126, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 49.217, "width_percent": 1.495}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.133046, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 50.712, "width_percent": 0.047}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.133615, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 50.759, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.134126, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 50.797, "width_percent": 0.044}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.134785, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 50.842, "width_percent": 0.053}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31964 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31964, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1359, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 50.894, "width_percent": 0.055}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31964 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31964], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.136497, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 50.949, "width_percent": 1.503}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026004' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026004"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.142754, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 52.453, "width_percent": 0.047}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31964 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31964], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.1434479, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 52.5, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31964 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31964], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.14414, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 52.558, "width_percent": 1.509}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31964 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31964], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.150188, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 54.067, "width_percent": 1.553}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31964 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31964], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.156266, "duration": 0.005679999999999999, "duration_str": "5.68ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 55.62, "width_percent": 1.57}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31964 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31964, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1625721, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 57.189, "width_percent": 0.091}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31964 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31964], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.163369, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 57.281, "width_percent": 1.512}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.169504, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 58.792, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.170139, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 58.842, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.170661, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 58.881, "width_percent": 0.039}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.171297, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 58.919, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31957 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31957, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.17253, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 58.969, "width_percent": 0.061}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31957 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31957], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.173133, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 59.03, "width_percent": 1.525}, {"sql": "select * from `brands` where `brands`.`id` = 227 limit 1", "type": "query", "params": [], "bindings": [227], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.179055, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 60.555, "width_percent": 0.053}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 227 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [227], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.179667, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 60.608, "width_percent": 0.133}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.180765, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.741, "width_percent": 0.053}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025965' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025965"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.181534, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.793, "width_percent": 0.044}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025966' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025966"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.182215, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.837, "width_percent": 0.047}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025962' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025962"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.182903, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.884, "width_percent": 0.041}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025963' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025963"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.183568, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.926, "width_percent": 0.047}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025964' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025964"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.184249, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 60.973, "width_percent": 0.041}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025961' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025961"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1849601, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 61.014, "width_percent": 0.044}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31957 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31957], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.185596, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 61.058, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31957 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31957], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1863089, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 61.116, "width_percent": 1.523}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31957 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31957], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.192395, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 62.639, "width_percent": 1.542}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31957 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31957], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.198507, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 64.181, "width_percent": 1.492}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31957 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31957, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2044642, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 65.674, "width_percent": 0.08}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31957 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31957], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2052448, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 65.754, "width_percent": 1.528}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.211309, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 67.282, "width_percent": 0.047}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2118819, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 67.329, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.212431, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 67.379, "width_percent": 0.041}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213081, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 67.42, "width_percent": 0.05}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.213892, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 67.47, "width_percent": 0.05}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.214158, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 67.52, "width_percent": 1.495}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.219653, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.015, "width_percent": 0.102}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.220185, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.117, "width_percent": 0.16}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.221114, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.277, "width_percent": 0.05}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.22165, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.327, "width_percent": 0.061}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2221718, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.388, "width_percent": 0.072}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.222727, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.46, "width_percent": 0.047}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223183, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.507, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.223582, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.565, "width_percent": 1.512}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.229161, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 71.076, "width_percent": 1.492}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.234684, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.569, "width_percent": 1.528}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240333, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.097, "width_percent": 0.055}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.240675, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.152, "width_percent": 1.506}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246367, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.658, "width_percent": 0.058}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2467132, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.716, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.246957, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.755, "width_percent": 0.039}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.247325, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.794, "width_percent": 0.047}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.248131, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.841, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.248429, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 75.899, "width_percent": 1.492}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.253912, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.391, "width_percent": 0.044}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2542162, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.435, "width_percent": 0.127}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2552578, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.563, "width_percent": 0.113}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255948, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.676, "width_percent": 1.492}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2614748, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 79.168, "width_percent": 1.523}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2671, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 80.691, "width_percent": 1.539}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.272777, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.23, "width_percent": 0.05}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.27307, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.28, "width_percent": 1.49}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2786689, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.77, "width_percent": 0.05}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.27902, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.819, "width_percent": 0.044}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279317, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.863, "width_percent": 0.044}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.279697, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.908, "width_percent": 0.044}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.28049, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.952, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.280754, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.999, "width_percent": 1.492}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.286242, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.491, "width_percent": 0.041}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.286552, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.533, "width_percent": 0.141}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287519, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.674, "width_percent": 0.064}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.287939, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.737, "width_percent": 1.503}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.293472, "duration": 0.00556, "duration_str": "5.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.241, "width_percent": 1.537}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2991228, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.777, "width_percent": 1.523}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.304743, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 90.3, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.305037, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 90.347, "width_percent": 1.512}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.310666, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.859, "width_percent": 0.044}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.310954, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.903, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3112051, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.941, "width_percent": 0.036}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.311564, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.977, "width_percent": 0.047}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.312347, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 92.024, "width_percent": 0.05}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3126218, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 92.074, "width_percent": 1.528}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.318248, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.602, "width_percent": 0.044}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3185432, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.647, "width_percent": 0.124}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.319583, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.771, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.319987, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.829, "width_percent": 1.553}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.325744, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.382, "width_percent": 1.52}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.331372, "duration": 0.00554, "duration_str": "5.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 96.902, "width_percent": 1.531}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.33706, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.433, "width_percent": 0.053}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.337409, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.486, "width_percent": 1.514}]}, "models": {"data": {"App\\Models\\Upload": {"retrieved": 29, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\ProductTax": {"retrieved": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTax.php&line=1", "ajax": false, "filename": "ProductTax.php", "line": "?"}}, "App\\Models\\Tax": {"retrieved": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 11, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductTranslation": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTranslation.php&line=1", "ajax": false, "filename": "ProductTranslation.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\BrandTranslation": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrandTranslation.php&line=1", "ajax": false, "filename": "BrandTranslation.php", "line": "?"}}, "App\\Models\\Supplier": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FSupplier.php&line=1", "ajax": false, "filename": "Supplier.php", "line": "?"}}}, "count": 126, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 126}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/product-by-category-slug?category_slug=grocery-essentials&...", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@product_by_category_slug", "uri": "GET api/v3/product-by-category-slug", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@product_by_category_slug<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=317\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=317\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:317-368</a>", "middleware": "api", "duration": "695ms", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-567332031 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>category_slug</span>\" => \"<span class=sf-dump-str title=\"18 characters\">grocery-essentials</span>\"\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567332031\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1111989206 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1111989206\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1084070767 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=300</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6InFQMXdwZGhrdW9ERndBRjhmNUtWVHc9PSIsInZhbHVlIjoid3YrQktTSGpnakpKc0J1Y1BQa2tXdElPTG9BS1RKbDVtUUVjbjFSeERFbEtqQzd2UDhwdTMyTmJNdmZYb1QzUU54dlc0SjdldWlUTnpVS0pjUlVTYmZ6dk5nbWIvR3ZQdkxWQXZ5Um1jM1o5c1d0K0VGVXlNVklHMHMrbDRXTjUiLCJtYWMiOiI0YjdhYjlkODg5NDAxYmI3ZGQ0YjI0MWFlYzk0NzBlNjVjMWNiNDRhMDliZWQwMWU5Y2FmNTBjMDlmODA0MWU0IiwidGFnIjoiIn0%3D; buzficom_session=EpNyr5oxLRvQm1n5jZbCvtP1g6xRzXGwGlhMy8uQ</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084070767\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-716710952 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716710952\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-991921524 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">577</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhpVlJjUHEzcVdSdjVEVDdsTVlQUEE9PSIsInZhbHVlIjoiWDArUWJ1V1IwVXBSdUg1emtQVmJrNG53L1gxQUtkNFlCVStYZ0NKR2hpRUJkTTZzQ2FvV0E5TDJBRFVnd3N5ejErR29TYVJNSmxYOGZ3blBRVFpLR1dJMVZ4K0hiMlZtSFAvcEpSdWZsTzlKTTd4T0R5Njh3M3FJYjhJMll1WE0iLCJtYWMiOiIzYjYxODJiMmY2ZmExYzkwMThiOTZmOTY1ZWRkYmMzNTVkMWI1NDFhZTBmMDhlOGQxODkyZDYyYWU0NDYzMDMyIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6ImpWN2YzT2pxRWFZY2oxdldQMS9qVlE9PSIsInZhbHVlIjoiNHNZbVFhSzNTTlczUDlVTmF2NHl6SjR3R05MZHlWdnA2NlNmUFBpaktjcjZPTnRmTytONTZrcUoxZFd0aStqTEl5SFozK1NzSlJZNXYvcWJUN3pEVDBCZlorTmlBMUkvb0lXWVdXZlhrSkNSWGIxTGRKZXFhYmVoZC9ORGNIcUgiLCJtYWMiOiJmZjc2ZWEyZGNhOGUwZWI5Y2E5NzhkODEyNTUwYzczMzdmYmI3ZjNhMzkzNzNmNGQ1OGVhMjM4NWNkZDE2YjM3IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhpVlJjUHEzcVdSdjVEVDdsTVlQUEE9PSIsInZhbHVlIjoiWDArUWJ1V1IwVXBSdUg1emtQVmJrNG53L1gxQUtkNFlCVStYZ0NKR2hpRUJkTTZzQ2FvV0E5TDJBRFVnd3N5ejErR29TYVJNSmxYOGZ3blBRVFpLR1dJMVZ4K0hiMlZtSFAvcEpSdWZsTzlKTTd4T0R5Njh3M3FJYjhJMll1WE0iLCJtYWMiOiIzYjYxODJiMmY2ZmExYzkwMThiOTZmOTY1ZWRkYmMzNTVkMWI1NDFhZTBmMDhlOGQxODkyZDYyYWU0NDYzMDMyIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6ImpWN2YzT2pxRWFZY2oxdldQMS9qVlE9PSIsInZhbHVlIjoiNHNZbVFhSzNTTlczUDlVTmF2NHl6SjR3R05MZHlWdnA2NlNmUFBpaktjcjZPTnRmTytONTZrcUoxZFd0aStqTEl5SFozK1NzSlJZNXYvcWJUN3pEVDBCZlorTmlBMUkvb0lXWVdXZlhrSkNSWGIxTGRKZXFhYmVoZC9ORGNIcUgiLCJtYWMiOiJmZjc2ZWEyZGNhOGUwZWI5Y2E5NzhkODEyNTUwYzczMzdmYmI3ZjNhMzkzNzNmNGQ1OGVhMjM4NWNkZDE2YjM3IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-991921524\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1923687003 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"114 characters\">http://localhost/buzfi-new-backend/api/v3/product-by-category-slug?category_slug=grocery-essentials&amp;limit=6&amp;page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923687003\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/product-by-category-slug?category_slug=grocery-essentials&...", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@product_by_category_slug"}, "badge": null}}