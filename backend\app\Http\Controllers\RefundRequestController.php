<?php

namespace App\Http\Controllers;

use App\Exports\PaidPaymentOrderDetailsExport;
use App\Exports\PaidRefundOrderDetailsExport;
use App\Mail\OrderCanceledEmail;
use App\Models\CancelReason;
use App\Models\Order;
use App\Notifications\order\OnTheWayEmailNotification;
use App\Notifications\order\OrderCanceledEmailNotification;
use App\Notifications\order\OrderRefundProcessedNotification;
use App\Notifications\order\refund\OrderRefundRequestNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\BusinessSetting;
use App\Models\ClubPoint;
use App\Models\RefundRequest;
use App\Models\OrderDetail;
use App\Models\Shop;
use App\Models\Wallet;
use App\Models\User;
use Artisan;
use Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class RefundRequestController extends Controller
{
    public function __construct()
    {

        // Staff Permission Check
        $this->middleware(['permission:view_refund_requests'])->only('admin_index');
        $this->middleware(['permission:view_approved_refund_requests'])->only('paid_index');
        $this->middleware(['permission:view_rejected_refund_requests'])->only('rejected_index');
        $this->middleware(['permission:refund_request_configuration'])->only('refund_config');
    }

    /**
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */

    //Store Customer Refund Request
    public function request_store(Request $request, $id)
    {
        $order_detail = OrderDetail::where('id', $id)->first();
        $refund = new RefundRequest;
        $refund->user_id = Auth::user()->id;
        $refund->order_id = $order_detail->order_id;
        $refund->order_detail_id = $order_detail->id;
        $refund->seller_id = $order_detail->seller_id;
        $refund->seller_approval = 0;
        $refund->reason = $request->reason;
        $refund->admin_approval = 0;
        $refund->admin_seen = 0;
        $refund->refund_amount = $order_detail->price + $order_detail->tax;
        $refund->refund_status = 0;

        if ($order_detail->product == null ||  $order_detail->payment_status != 'paid') {
            if ($request->ajax()) {
                $message = translate("Order Cancel successfully.");
                if ($request->reason === 'Cancelled by Admin') {
                    $order_detail->cancel_reason = $request->reason_id;
                    $order_detail->save();
                }


                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'refund_id' => null,
                ]);
            } else {
                $array = array();
                $array['order'] = $order = Order::where('id', $order_detail->order_id)->first();
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Update on Your Order ') . ' - ' . $order->code . ' ' . ' Refund Request';
                $array['cancel_by'] = 'User';
                $array['cancel_reason'] = '';
                try {
                    //$order->user->notify(new OnTheWayEmailNotification($array));
                    //Mail::to($order->user->email)->queue(new OrderCanceledEmail($array));
                    $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                    exec($command);
                } catch (\Exception $e) {
                    Log::channel('email_logs')->error('Error occurred while sending Has Been Order Cancelled email in OrderController : ' . $e->getMessage());
                }
                flash($message)->success();
                return redirect()->route('purchase_history.index');
            }
        } else {
            if ($refund->save()) {
                $array = array();
                $order = Order::where('id', $order_detail->order_id)->first();
                $array['order_code'] = $order->ocode;
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Update on Your Refund Request ') ;
                $array['refundProduct'] = $order_detail?->product?->name;

                try {
                    $order->user->notify(new OrderRefundRequestNotification($array));
                    $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                    exec($command);
                } catch (\Exception $e) {
                    Log::channel('email_logs')->error('Error occurred while sending  Order Refund request email in RefundRequestController : ' . $e->getMessage());
                }
                $message = translate("Refund Request has been sent successfully");

                if ($request->ajax()) {

                    if ($request->reason === 'Cancelled by Admin') {
                        $order_detail->cancel_reason = $request->reason_id;
                        $order_detail->save();
                    }

                    return response()->json([
                        'success' => true,
                        'message' => $message,
                        'refund_id' => $refund->id,
                    ]);
                } else {
                    flash($message)->success();
                    return redirect()->route('purchase_history.index');
                }
            } else {
                $errorMessage = translate("Something went wrong");

                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage,
                    ]);
                } else {
                    flash($errorMessage)->error();
                    return back();
                }
            }
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function vendor_index()
    {
        $refunds = RefundRequest::where('seller_id', Auth::user()->id)->latest()->paginate(10);
        return view('refund_request.frontend.recieved_refund_request.index', compact('refunds'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function customer_index()
    {
        $refunds = RefundRequest::where('user_id', Auth::user()->id)->latest()->paginate(10);
        return view('refund_request.frontend.refund_request.index', compact('refunds'));
    }

    //Set the Refund configuration
    public function refund_config()
    {
        return view('refund_request.config');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function refund_time_update(Request $request)
    {
        $business_settings = BusinessSetting::where('type', $request->type)->first();
        if ($business_settings != null) {
            $business_settings->value = $request->value;
            $business_settings->save();
        } else {
            $business_settings = new BusinessSetting;
            $business_settings->type = $request->type;
            $business_settings->value = $request->value;
            $business_settings->save();
        }
        Artisan::call('cache:clear');
        flash(translate("Refund Request sending time has been updated successfully"))->success();
        return back();
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function refund_sticker_update(Request $request)
    {
        $business_settings = BusinessSetting::where('type', $request->type)->first();
        if ($business_settings != null) {
            $business_settings->value = $request->logo;
            $business_settings->save();
        } else {
            $business_settings = new BusinessSetting;
            $business_settings->type = $request->type;
            $business_settings->value = $request->logo;
            $business_settings->save();
        }
        Artisan::call('cache:clear');
        flash(translate("Refund Sticker has been updated successfully"))->success();
        return back();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function admin_index()
    {
        $refunds = RefundRequest::where('refund_status', 0)->latest()->paginate(15);
        return view('refund_request.index', compact('refunds'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function paid_index()
    {
        $refunds = RefundRequest::where('refund_status', 1)->latest()->paginate(15);
        return view('refund_request.paid_refund', compact('refunds'));
    }
    public function downloadPaidRefundDetails()
    {
        return Excel::download(new PaidRefundOrderDetailsExport, 'refunfd_order_details.xlsx');
    }

    public function rejected_index()
    {
        $refunds = RefundRequest::where('refund_status', 2)->latest()->paginate(15);
        return view('refund_request.rejected_refund', compact('refunds'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function request_approval_vendor(Request $request)
    {
        $refund = RefundRequest::findOrFail($request->el);
        if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            $refund->seller_approval = 1;
            $refund->admin_approval = 1;
        } else {
            $refund->seller_approval = 1;
        }

        if ($refund->save()) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function refund_pay(Request $request)
    {
        try {
            DB::beginTransaction();

            $refund = RefundRequest::findOrFail($request->refund_id);
            if ($refund->seller_approval == 1) {
                $seller = Shop::where('user_id', $refund->seller_id)->first();
                if ($seller != null) {
                    $seller->admin_to_pay -= $refund->refund_amount;
                }
                $seller->save();
            }

            $refund_amount = $refund->refund_amount;

            // Club Point conversion check
            if (addon_is_activated('club_point')) {
                $club_point = ClubPoint::where('order_id', $refund->order_id)->first();
                if ($club_point != null) {
                    $club_point_details = $club_point->club_point_details->where('product_id', $refund->orderDetail->product->id)->first();

                    if ($club_point->convert_status == 1) {
                        $refund_amount -= $club_point_details->converted_amount;
                    } else {
                        $club_point_details->refunded = 1;
                        $club_point_details->save();
                    }
                }
            }

            $getPaymentChargeIdFromOrders =   Order::where('id',  $refund->order_id)->value('payment_charge_id');
            $stripe = new \Stripe\StripeClient(env('STRIPE_SECRET'));
            try {
                $refunded = $stripe->refunds->create([
                    'charge' => $getPaymentChargeIdFromOrders, // Replace with your actual charge ID
                    'amount' => $refund_amount * 100 // Amount in cents (e.g., $74.36 is represented as 7436)
                ]);
            } catch (\Stripe\Exception\ApiErrorException $e) {
                flash(translate($e->getError()->message))->error();
            }

            $wallet = new Wallet;
            $wallet->user_id = $refund->user_id;
            $wallet->amount = $refund_amount;
            $wallet->payment_method = 'Refund';
            $wallet->payment_details = 'Product Money Refund';
            $wallet->save();
            $user = User::findOrFail($refund->user_id);
            $user->balance += $refund_amount;
            $user->save();
            if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
                $refund->admin_approval = 1;
                $refund->refund_status = 1;
            }
            $refund->save();

            if($refund->orderDetail){
                $refundProduct=$refund->orderDetail?->product?->name ;
            }else{
                $refundProduct="";
            }




            $array = array();
            $order = Order::findOrFail($refund->order_id);
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['refund_amount'] = $refund->refund_amount;
            $array['refund_method'] = "Stripe";
            $array['refundProduct'] = $refundProduct;
            $array['refund_processed_date'] = Carbon::now()->format('Y-M-d ');;
            $array['subject'] = translate('Your Refund for Order ') . ' - ' . $order->code . ' ' . ' Has Been Processed ';
            try {
                $user->notify(new OrderRefundProcessedNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending Ready to Be Shipped email in OrderController : ' . $e->getMessage());
            }


            DB::commit();
            flash(translate('Refund has been sent successfully.'))->success();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Something went wrong.'))->error();
        }
        return back();
    }

    public function reject_refund_request(Request $request)
    {
        $refund = RefundRequest::findOrFail($request->refund_id);
        if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            $refund->admin_approval = 2;
            $refund->refund_status  = 2;
            $refund->reject_reason  = $request->reject_reason;
        } else {
            $refund->seller_approval = 2;
            $refund->reject_reason  = $request->reject_reason;
        }

        if ($refund->save()) {
            flash(translate('Refund request rejected successfully.'))->success();
            return back();
        } else {
            return back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function refund_request_send_page($id)
    {
        $order_detail = OrderDetail::findOrFail($id);
        if ($order_detail->product != null && $order_detail->product->refundable == 1) {
            return view('refund_request.frontend.refund_request.create', compact('order_detail'));
        } else {
            return back();
        }
    }

    /**
     * Show the form for view the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    //Shows the refund reason
    public function reason_view($id)
    {
        $refund = RefundRequest::findOrFail($id);
        if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            //if ($refund->orderDetail != null) {
                $refund->admin_seen = 1;
                $refund->save();
                return view('refund_request.reason', compact('refund'));
           // }
        } else {
            return view('refund_request.frontend.refund_request.reason', compact('refund'));
        }
    }

    public function reject_reason_view($id)
    {
        $refund = RefundRequest::findOrFail($id);
        return $refund->reject_reason;
    }
}
