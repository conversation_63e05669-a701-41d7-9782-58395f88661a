[2025-07-25 04:52:06] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 04:52:06] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 04:52:06] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:06] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:06] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:06] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:07] local.INFO: Created new cart info {"cart_info_id":"189bb44e-62af-4653-a24c-7a6727d84ebf","user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:07] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0,"items":[]} 
[2025-07-25 04:52:07] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:07] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0} 
[2025-07-25 04:52:07] local.INFO: Cart retrieved successfully {"cart_info_id":"189bb44e-62af-4653-a24c-7a6727d84ebf","user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0,"saved_items_count":0} 
[2025-07-25 04:52:08] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 04:52:08] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 04:52:08] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:08] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:08] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0} 
[2025-07-25 04:52:09] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 04:52:09] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 04:52:09] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:09] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:09] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:09] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:09] local.INFO: Found existing cart info {"cart_info_id":"189bb44e-62af-4653-a24c-7a6727d84ebf","user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:09] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0,"items":[]} 
[2025-07-25 04:52:09] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:09] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0} 
[2025-07-25 04:52:09] local.INFO: Cart retrieved successfully {"cart_info_id":"189bb44e-62af-4653-a24c-7a6727d84ebf","user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0,"saved_items_count":0} 
[2025-07-25 04:52:10] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 04:52:10] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 04:52:10] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753444149392_dvtv36i17"} 
[2025-07-25 04:52:10] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","is_authenticated":false} 
[2025-07-25 04:52:10] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753444149392_dvtv36i17","items_count":0} 
[2025-07-25 05:43:32] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sizes' already exists (Connection: mysql, SQL: create table `sizes` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(191) not null, `code` varchar(191) not null, `display_order` int not null default '0', `dimension` varchar(191) null, `description` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sizes' already exists (Connection: mysql, SQL: create table `sizes` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(191) not null, `code` varchar(191) not null, `display_order` int not null default '0', `dimension` varchar(191) null, `description` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#1 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#2 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `s...')
#3 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('sizes', Object(Closure))
#6 D:\\Development\\laragon\\www\\buzfi-new-backend\\database\\migrations\\2023_08_30_0000001_create_sizes_table.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2023_08_30_0000...', Object(Closure))
#13 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2023_08_30_0000...', Object(Closure))
#14 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Development\\\\...', 45, false)
#15 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Development\\laragon\\www\\buzfi-new-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'sizes' already exists at D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `s...', Array)
#2 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('create table `s...', Array, Object(Closure))
#3 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('create table `s...', Array, Object(Closure))
#4 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('create table `s...')
#5 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(456): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->create('sizes', Object(Closure))
#8 D:\\Development\\laragon\\www\\buzfi-new-backend\\database\\migrations\\2023_08_30_0000001_create_sizes_table.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(427): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2023_08_30_0000...', Object(Closure))
#15 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(224): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2023_08_30_0000...', Object(Closure))
#16 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(189): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\Development\\\\...', 45, false)
#17 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(132): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\Development\\laragon\\www\\buzfi-new-backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}
"} 
[2025-07-25 08:39:52] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/products/featured"} 
[2025-07-25 08:39:52] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31936  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31936  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31935  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31935  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31934  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31934  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31933  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31933  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31932  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31932  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31931  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31931  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31930  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31930  
[2025-07-25 08:39:52] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31929  
[2025-07-25 08:39:52] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31929  
[2025-07-25 08:39:54] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/products/on-sale"} 
[2025-07-25 08:39:54] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1169  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1169  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33037  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33037  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33016  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33016  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33015  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33015  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33014  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33014  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33009  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33009  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33006  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33006  
[2025-07-25 08:39:55] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31991  
[2025-07-25 08:39:55] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31991  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31935  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31935  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1169  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1169  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31981  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31981  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31946  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31946  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1631  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1631  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2702  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2702  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 3624  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 3624  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 3684  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 3684  
[2025-07-25 08:39:59] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31933  
[2025-07-25 08:39:59] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31933  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33037  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33037  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33016  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33016  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33015  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33015  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33014  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33014  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33009  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33009  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33006  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33006  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33001  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33001  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32992  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32992  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31940  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31940  
[2025-07-25 08:40:00] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:40:00] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32002  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32002  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31985  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31985  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31982  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31982  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31981  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31981  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31964  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31964  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31957  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31957  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31954  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31954  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31950  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31950  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:40:01] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:40:01] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31971  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31971  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31963  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31963  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31948  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31948  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31946  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31946  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31945  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31945  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31915  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31915  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31897  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31897  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31893  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31893  
[2025-07-25 08:40:02] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31892  
[2025-07-25 08:40:02] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31892  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 4011  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 4011  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31857  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31857  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31856  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31856  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31855  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31855  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31854  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31854  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31853  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31853  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31852  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31852  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31851  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31851  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31850  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31850  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31849  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31849  
[2025-07-25 08:40:03] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31848  
[2025-07-25 08:40:03] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31848  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32001  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32001  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31997  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31997  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31995  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31995  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31949  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31949  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31917  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31917  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31908  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31908  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31894  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31894  
[2025-07-25 08:40:04] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2580  
[2025-07-25 08:40:04] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2580  
[2025-07-25 08:40:05] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2579  
[2025-07-25 08:40:05] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2579  
[2025-07-25 08:40:05] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2577  
[2025-07-25 08:40:05] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2577  
[2025-07-25 08:40:06] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:40:06] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:06] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:06] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:06] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:06] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:06] local.INFO: Created new cart info {"cart_info_id":"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786","user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:06] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0,"items":[]} 
[2025-07-25 08:40:06] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:06] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0} 
[2025-07-25 08:40:06] local.INFO: Cart retrieved successfully {"cart_info_id":"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786","user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0,"saved_items_count":0} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:07] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:07] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:07] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:07] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:07] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:07] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:07] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:07] local.INFO: Found existing cart info {"cart_info_id":"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786","user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:07] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0,"items":[]} 
[2025-07-25 08:40:07] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:07] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0} 
[2025-07-25 08:40:07] local.INFO: Cart retrieved successfully {"cart_info_id":"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786","user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0,"saved_items_count":0} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:40:07] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:07] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457987293_cr35y75mk"} 
[2025-07-25 08:40:07] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","is_authenticated":false} 
[2025-07-25 08:40:07] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457987293_cr35y75mk","items_count":0} 
[2025-07-25 08:40:36] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:40:36] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:36] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:36] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:36] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:36] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:36] local.INFO: Created new cart info {"cart_info_id":"483aa506-5b78-43ac-88e9-f05f61567fa1","user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:36] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0,"items":[]} 
[2025-07-25 08:40:36] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:36] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0} 
[2025-07-25 08:40:36] local.INFO: Cart retrieved successfully {"cart_info_id":"483aa506-5b78-43ac-88e9-f05f61567fa1","user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0,"saved_items_count":0} 
[2025-07-25 08:40:36] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:40:36] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:36] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:36] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:36] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0} 
[2025-07-25 08:40:37] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:40:37] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:37] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:37] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:37] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:37] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:37] local.INFO: Found existing cart info {"cart_info_id":"483aa506-5b78-43ac-88e9-f05f61567fa1","user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:37] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0,"items":[]} 
[2025-07-25 08:40:37] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:37] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0} 
[2025-07-25 08:40:37] local.INFO: Cart retrieved successfully {"cart_info_id":"483aa506-5b78-43ac-88e9-f05f61567fa1","user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0,"saved_items_count":0} 
[2025-07-25 08:40:37] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:40:37] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:40:37] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753457812364_c9d80y7ne"} 
[2025-07-25 08:40:37] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","is_authenticated":false} 
[2025-07-25 08:40:37] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753457812364_c9d80y7ne","items_count":0} 
[2025-07-25 08:46:28] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/products/featured"} 
[2025-07-25 08:46:28] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:28] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:46:28] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31936  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31936  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31935  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31935  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31934  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31934  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31933  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31933  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31932  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31932  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31931  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31931  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31930  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31930  
[2025-07-25 08:46:29] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31929  
[2025-07-25 08:46:29] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31929  
[2025-07-25 08:46:30] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/products/on-sale"} 
[2025-07-25 08:46:30] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1169  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1169  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33037  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33037  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33016  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33016  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33015  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33015  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33014  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33014  
[2025-07-25 08:46:30] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33009  
[2025-07-25 08:46:30] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33009  
[2025-07-25 08:46:31] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33006  
[2025-07-25 08:46:31] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33006  
[2025-07-25 08:46:31] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31991  
[2025-07-25 08:46:31] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31991  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31935  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31935  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1169  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1169  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31981  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31981  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31946  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31946  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 1631  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 1631  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2702  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2702  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 3624  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 3624  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 3684  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 3684  
[2025-07-25 08:46:42] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31933  
[2025-07-25 08:46:42] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31933  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33037  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33037  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33016  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33016  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33015  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33015  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33014  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33014  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33009  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33009  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33006  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33006  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 33001  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 33001  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32992  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32992  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31940  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31940  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:46:43] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32002  
[2025-07-25 08:46:43] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32002  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31985  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31985  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31982  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31982  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31981  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31981  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31964  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31964  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31957  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31957  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31954  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31954  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31950  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31950  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31938  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31938  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31937  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31937  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31971  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31971  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31963  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31963  
[2025-07-25 08:46:44] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31948  
[2025-07-25 08:46:44] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31948  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31946  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31946  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31945  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31945  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31915  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31915  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31897  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31897  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31893  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31893  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31892  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31892  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 4011  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 4011  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31857  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31857  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31856  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31856  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31855  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31855  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31854  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31854  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31853  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31853  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31852  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31852  
[2025-07-25 08:46:45] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31851  
[2025-07-25 08:46:45] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31851  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31850  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31850  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31849  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31849  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31848  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31848  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 32001  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 32001  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31997  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31997  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31995  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31995  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31949  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31949  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31917  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31917  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31908  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31908  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 31894  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 31894  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2580  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2580  
[2025-07-25 08:46:46] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2579  
[2025-07-25 08:46:46] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2579  
[2025-07-25 08:46:47] local.INFO: ProductResource.getWishlistStatus: No authenticated user for product 2577  
[2025-07-25 08:46:47] local.INFO: ProductResource.getCompareStatus: No authenticated user for product 2577  
[2025-07-25 08:46:47] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:46:47] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:47] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:47] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:47] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:47] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:48] local.INFO: Created new cart info {"cart_info_id":"2f3a3784-6f65-4bad-bbdc-024f675107a1","user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:48] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0,"items":[]} 
[2025-07-25 08:46:48] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:48] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0} 
[2025-07-25 08:46:48] local.INFO: Cart retrieved successfully {"cart_info_id":"2f3a3784-6f65-4bad-bbdc-024f675107a1","user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0,"saved_items_count":0} 
[2025-07-25 08:46:48] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:46:48] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:48] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:48] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:48] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0} 
[2025-07-25 08:46:49] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:46:49] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:49] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:49] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:49] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:49] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:49] local.INFO: Found existing cart info {"cart_info_id":"2f3a3784-6f65-4bad-bbdc-024f675107a1","user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:49] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0,"items":[]} 
[2025-07-25 08:46:49] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:49] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0} 
[2025-07-25 08:46:49] local.INFO: Cart retrieved successfully {"cart_info_id":"2f3a3784-6f65-4bad-bbdc-024f675107a1","user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0,"saved_items_count":0} 
[2025-07-25 08:46:49] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:46:49] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:46:49] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458370059_7j5bpf577"} 
[2025-07-25 08:46:49] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","is_authenticated":false} 
[2025-07-25 08:46:49] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458370059_7j5bpf577","items_count":0} 
[2025-07-25 08:47:26] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:47:26] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:47:26] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:26] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:26] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:26] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:26] local.INFO: Created new cart info {"cart_info_id":"c5f735dd-3106-44d0-a1ca-382d37fec8f5","user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:26] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0,"items":[]} 
[2025-07-25 08:47:26] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:26] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0} 
[2025-07-25 08:47:26] local.INFO: Cart retrieved successfully {"cart_info_id":"c5f735dd-3106-44d0-a1ca-382d37fec8f5","user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0,"saved_items_count":0} 
[2025-07-25 08:47:27] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:47:27] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:47:27] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:27] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:27] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0} 
[2025-07-25 08:47:27] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart"} 
[2025-07-25 08:47:27] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:47:27] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:27] local.INFO: Cart index - User identification {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:27] local.INFO: Getting cart {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:27] local.INFO: Getting or creating cart info {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:27] local.INFO: Found existing cart info {"cart_info_id":"c5f735dd-3106-44d0-a1ca-382d37fec8f5","user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:27] local.INFO: Found guest user cart items {"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0,"items":[]} 
[2025-07-25 08:47:27] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:27] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0} 
[2025-07-25 08:47:27] local.INFO: Cart retrieved successfully {"cart_info_id":"c5f735dd-3106-44d0-a1ca-382d37fec8f5","user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0,"saved_items_count":0} 
[2025-07-25 08:47:28] local.INFO: OptionalAuth middleware - Start {"has_token":false,"token_preview":null,"token_source":"none","path":"api/v3/cart/saved-items"} 
[2025-07-25 08:47:28] local.INFO: OptionalAuth middleware - Final state {"auth_check":false,"auth_id":null,"guard_check":false,"guard_id":null} 
[2025-07-25 08:47:28] local.DEBUG: getTempUserId: Found temp user ID in header {"temp_user_id":"temp_1753458426942_rqsaj2prd"} 
[2025-07-25 08:47:28] local.INFO: Getting saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","is_authenticated":false} 
[2025-07-25 08:47:28] local.INFO: Found saved items {"user_id":null,"temp_user_id":"temp_1753458426942_rqsaj2prd","items_count":0} 
[2025-07-25 11:11:55] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'default.business_settings' doesn't exist (Connection: mysql, SQL: select * from `business_settings`) {"view":{"view":"/var/www/backend/resources/views/auth/login.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1254297050 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3675</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1254297050\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'default.business_settings' doesn't exist (Connection: mysql, SQL: select * from `business_settings`) at /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(675): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 /var/www/backend/app/Utility/BusinessSettingUtility.php(27): Illuminate\\Database\\Eloquent\\Model::all()
#10 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(396): App\\Utility\\BusinessSettingUtility->App\\Utility\\{closure}()
#11 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(429): Illuminate\\Cache\\Repository->remember('business_settin...', Object(Illuminate\\Support\\Carbon), Object(Closure))
#12 /var/www/backend/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 /var/www/backend/app/Utility/BusinessSettingUtility.php(26): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 /var/www/backend/app/Http/Helpers.php(1359): App\\Utility\\BusinessSettingUtility->business_settings('system_logo_bla...')
#15 /var/www/backend/resources/views/auth/login.blade.php(21): get_setting('system_logo_bla...')
#16 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('/var/www/backen...')
#17 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/var/www/backen...', Array)
#19 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/var/www/backen...', Array)
#20 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('/var/www/backen...', Array)
#21 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#22 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#23 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#24 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 /var/www/backend/app/Http/Middleware/RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /var/www/backend/app/Http/Middleware/PreventBackHistory.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/backend/app/Http/Middleware/HandleStripeCookies.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HandleStripeCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/backend/app/Http/Middleware/CheckForMaintenanceMode.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/backend/app/Http/Middleware/HttpsProtocol.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HttpsProtocol->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/backend/app/Http/Middleware/Language.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /var/www/backend/app/Http/Middleware/LogCartRequests.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\LogCartRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#64 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 /var/www/backend/app/Http/Middleware/Cors.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 /var/www/backend/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'default.business_settings' doesn't exist (Connection: mysql, SQL: select * from `business_settings`) at /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(675): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 /var/www/backend/app/Utility/BusinessSettingUtility.php(27): Illuminate\\Database\\Eloquent\\Model::all()
#10 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(396): App\\Utility\\BusinessSettingUtility->App\\Utility\\{closure}()
#11 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(429): Illuminate\\Cache\\Repository->remember('business_settin...', Object(Illuminate\\Support\\Carbon), Object(Closure))
#12 /var/www/backend/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#13 /var/www/backend/app/Utility/BusinessSettingUtility.php(26): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#14 /var/www/backend/app/Http/Helpers.php(1359): App\\Utility\\BusinessSettingUtility->business_settings('system_logo_bla...')
#15 /var/www/backend/storage/framework/views/46a8c5d223943f9e0c8af222a84cd48c.php(19): get_setting('system_logo_bla...')
#16 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('/var/www/backen...')
#17 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#18 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/var/www/backen...', Array)
#19 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/var/www/backen...', Array)
#20 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('/var/www/backen...', Array)
#21 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#22 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#23 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#24 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#25 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#26 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#27 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#28 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 /var/www/backend/app/Http/Middleware/RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /var/www/backend/app/Http/Middleware/PreventBackHistory.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/backend/app/Http/Middleware/HandleStripeCookies.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HandleStripeCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/backend/app/Http/Middleware/CheckForMaintenanceMode.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/backend/app/Http/Middleware/HttpsProtocol.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HttpsProtocol->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/backend/app/Http/Middleware/Language.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#52 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#53 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#54 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#55 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#56 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /var/www/backend/app/Http/Middleware/LogCartRequests.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\LogCartRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#64 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 /var/www/backend/app/Http/Middleware/Cors.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#79 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#80 /var/www/backend/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#81 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'default.business_settings' doesn't exist at /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php:423)
[stacktrace]
#0 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): PDO->prepare('select * from `...')
#1 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 /var/www/backend/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(675): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 /var/www/backend/app/Utility/BusinessSettingUtility.php(27): Illuminate\\Database\\Eloquent\\Model::all()
#12 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(396): App\\Utility\\BusinessSettingUtility->App\\Utility\\{closure}()
#13 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php(429): Illuminate\\Cache\\Repository->remember('business_settin...', Object(Illuminate\\Support\\Carbon), Object(Closure))
#14 /var/www/backend/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#15 /var/www/backend/app/Utility/BusinessSettingUtility.php(26): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#16 /var/www/backend/app/Http/Helpers.php(1359): App\\Utility\\BusinessSettingUtility->business_settings('system_logo_bla...')
#17 /var/www/backend/storage/framework/views/46a8c5d223943f9e0c8af222a84cd48c.php(19): get_setting('system_logo_bla...')
#18 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(123): require('/var/www/backen...')
#19 /var/www/backend/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#20 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('/var/www/backen...', Array)
#21 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('/var/www/backen...', Array)
#22 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('/var/www/backen...', Array)
#23 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(190): Illuminate\\View\\View->getContents()
#24 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/View.php(159): Illuminate\\View\\View->renderContents()
#25 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#26 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#27 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#28 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#29 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#30 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 /var/www/backend/app/Http/Middleware/RedirectIfAuthenticated.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /var/www/backend/app/Http/Middleware/PreventBackHistory.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /var/www/backend/app/Http/Middleware/HandleStripeCookies.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HandleStripeCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /var/www/backend/app/Http/Middleware/CheckForMaintenanceMode.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /var/www/backend/app/Http/Middleware/HttpsProtocol.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\HttpsProtocol->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /var/www/backend/app/Http/Middleware/Language.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Language->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /var/www/backend/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 /var/www/backend/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 /var/www/backend/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /var/www/backend/app/Http/Middleware/LogCartRequests.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\LogCartRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /var/www/backend/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 /var/www/backend/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#66 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /var/www/backend/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 /var/www/backend/app/Http/Middleware/Cors.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\Cors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 /var/www/backend/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#81 /var/www/backend/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#82 /var/www/backend/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#83 {main}
"} 
