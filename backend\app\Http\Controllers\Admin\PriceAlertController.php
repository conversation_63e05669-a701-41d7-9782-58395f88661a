<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PriceAlertController extends Controller
{
    /**
     * Display a listing of price alerts.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Product::join('users', 'products.user_id', '=', 'users.id')
            ->whereNotNull('products.price_alert_data')
            ->select('products.*', 'products.name as product_name', 'users.name as user_name', 'users.email');

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->whereRaw("JSON_EXTRACT(products.price_alert_data, '$.status') = ?", [$request->status]);
        }

        // Filter by alert type
        if ($request->has('alert_type') && $request->alert_type) {
            $query->whereRaw("JSON_EXTRACT(products.price_alert_data, '$.alertType') = ?", [$request->alert_type]);
        }

        // Get paginated results
        $alerts = $query->paginate(20);

        // Process data for display
        $processedAlerts = [];
        foreach ($alerts as $alert) {
            $alertData = json_decode($alert->price_alert_data, true);

            $processedAlerts[] = [
                'id' => $alertData['id'] ?? '',
                'user_name' => $alert->user_name,
                'user_email' => $alert->email,
                'product_name' => $alert->product_name,
                'product_id' => $alert->id,
                'alert_type' => $alertData['alertType'] ?? '',
                'threshold' => isset($alertData['percentageThreshold'])
                    ? $alertData['percentageThreshold'] . '%'
                    : (isset($alertData['targetPrice']) ? '$' . number_format($alertData['targetPrice'], 2) : 'Any drop'),
                'original_price' => isset($alertData['currentPrice']) ? '$' . number_format($alertData['currentPrice'], 2) : '',
                'status' => $alertData['status'] ?? '',
                'created_at' => isset($alertData['createdAt']) ? Carbon::parse($alertData['createdAt'])->format('Y-m-d H:i:s') : '',
                'record_id' => $alert->id
            ];
        }

        // Statistics
        $stats = [
            'total' => Product::whereNotNull('price_alert_data')->count(),
            'active' => Product::whereNotNull('price_alert_data')
                ->whereRaw("JSON_EXTRACT(price_alert_data, '$.status') = 'active'")->count(),
            'triggered' => Product::whereNotNull('price_alert_data')
                ->whereRaw("JSON_EXTRACT(price_alert_data, '$.status') = 'triggered'")->count(),
            'users_with_alerts' => Product::whereNotNull('price_alert_data')
                ->distinct('user_id')->count('user_id'),
            'products_with_alerts' => Product::whereNotNull('price_alert_data')->count()
        ];

        return view('backend.price_alerts.index', compact('processedAlerts', 'alerts', 'stats'));
    }

    /**
     * Show the form for creating a new price alert.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('backend.price_alerts.create');
    }

    /**
     * Store a newly created price alert in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'product_id' => 'required|exists:products,id',
            'alert_type' => 'required|in:any,percentage,specific',
            'percentage_threshold' => 'required_if:alert_type,percentage|numeric|min:1|max:99',

        ]);

        $product = Product::findOrFail($request->product_id);
        $user = User::findOrFail($request->user_id);

        // Create alert data
        $alertData = [
            'id' => md5($user->id . '_' . $product->id . '_' . time()),
            'productId' => $product->id,
            'productName' => $product->name,
            'productImage' => $product->thumbnail ? asset($product->thumbnail) : null,
            'currentPrice' => $product->unit_price,
            'alertType' => $request->alert_type,
            'createdAt' => Carbon::now()->toISOString(),
            'status' => 'active'
        ];

        if ($request->alert_type === 'percentage') {
            $alertData['percentageThreshold'] = $request->percentage_threshold;
        } elseif ($request->alert_type === 'specific') {
            $alertData['targetPrice'] = $request->target_price;
        }

        // Update the product with price alert data
        $product->price_alert_data = json_encode($alertData);
        $product->user_id = $user->id; // Associate with user requesting the alert
        $product->save();

        flash(translate('Price alert has been created successfully'))->success();
        return redirect()->route('admin.price-alerts.index');
    }

    /**
     * Remove the specified price alert from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);

        if ($product->price_alert_data) {
            $product->price_alert_data = null;
            $product->save();
            flash(translate('Price alert has been deleted successfully'))->success();
        } else {
            flash(translate('Invalid price alert'))->error();
        }

        return redirect()->route('admin.price-alerts.index');
    }

    /**
     * Run the price alert check manually.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function check()
    {
        // Call the artisan command
        \Artisan::call('price-alerts:check');

        $output = \Artisan::output();
        flash(translate('Price alert check completed: ' . $output))->success();

        return redirect()->route('admin.price-alerts.index');
    }
}
