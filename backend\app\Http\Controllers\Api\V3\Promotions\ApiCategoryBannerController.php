<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Banner\CategoryBannerResource;
use App\Models\Promotions\CategoryBanner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ApiCategoryBannerController extends ApiResponse
{
    /**
     * Get active category banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveCategoryBanners(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 3);
            
            $banners = CategoryBanner::active()
                ->orderBy('display_order', 'asc')
                ->limit($limit)
                ->get();
            
            // Load category data if available
            $banners->load('category');
            
            return $this->success(CategoryBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get category banners by category ID
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getCategoryBannersByCategory(Request $request, int $categoryId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 3);
            
            $banners = CategoryBanner::active()
                ->where('category_id', $categoryId)
                ->orderBy('display_order', 'asc')
                ->limit($limit)
                ->get();
                
            // Load category data
            $banners->load('category');
            
            return $this->success(CategoryBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get category banner by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getCategoryBanner(int $id): JsonResponse
    {
        try {
            $banner = CategoryBanner::find($id);
            
            if (!$banner) {
                return $this->error('Category banner not found', 'The requested category banner does not exist', 404);
            }
            
            // Load category if exists
            if ($banner->category_id) {
                $banner->load('category');
            }
            
            return $this->success(new CategoryBannerResource($banner));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Get all category banners for admin management
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function adminGetAllCategoryBanners(Request $request): JsonResponse
    {
        try {
            $query = CategoryBanner::query()->with('category');
            
            // Apply filters
            if ($request->has('is_active')) {
                $query->where('is_active', $request->input('is_active'));
            }
            
            if ($request->has('category_id')) {
                $query->where('category_id', $request->input('category_id'));
            }
            
            // Apply search
            if ($request->has('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('subtitle', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }
            
            // Apply sorting
            $sortField = $request->input('sort_field', 'display_order');
            $sortDirection = $request->input('sort_direction', 'asc');
            $query->orderBy($sortField, $sortDirection);
            
            // Pagination
            $perPage = $request->input('per_page', 15);
            $banners = $query->paginate($perPage);
            
            return $this->success([
                'data' => CategoryBannerResource::collection($banners->items()),
                'pagination' => [
                    'total' => $banners->total(),
                    'per_page' => $banners->perPage(),
                    'current_page' => $banners->currentPage(),
                    'last_page' => $banners->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Create a new category banner
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function adminCreateCategoryBanner(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'bg_color' => 'nullable|string|max:50',
                'image_src' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
                'image_alt' => 'nullable|string|max:255',
                'link_url' => 'required|string|max:255',
                'btn_text' => 'nullable|string|max:50',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'category_id' => 'nullable|exists:categories,id',
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Handle image upload
            $imagePath = null;
            if ($request->hasFile('image_src')) {
                $image = $request->file('image_src');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $imagePath = $image->storeAs('category_banners', $imageName, 'public');
            }

            // Create banner
            $banner = CategoryBanner::create([
                'title' => $request->input('title'),
                'subtitle' => $request->input('subtitle'),
                'description' => $request->input('description'),
                'bg_color' => $request->input('bg_color', 'bg-gray-100'),
                'image_src' => asset('storage/' . $imagePath),
                'image_alt' => $request->input('image_alt', $request->input('title')),
                'link_url' => $request->input('link_url'),
                'btn_text' => $request->input('btn_text', 'View More'),
                'is_active' => $request->input('is_active', true),
                'display_order' => $request->input('display_order', 0),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'category_id' => $request->input('category_id'),
            ]);

            // Load category if exists
            if ($banner->category_id) {
                $banner->load('category');
            }

            return $this->success(new CategoryBannerResource($banner), 'Category banner created successfully', 201);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Update an existing category banner
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function adminUpdateCategoryBanner(Request $request, int $id): JsonResponse
    {
        try {
            $banner = CategoryBanner::find($id);
            
            if (!$banner) {
                return $this->error('Category banner not found', 'The requested category banner does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'bg_color' => 'nullable|string|max:50',
                'image_src' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:2048',
                'image_alt' => 'nullable|string|max:255',
                'link_url' => 'sometimes|string|max:255',
                'btn_text' => 'nullable|string|max:50',
                'is_active' => 'sometimes|boolean',
                'display_order' => 'nullable|integer',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'category_id' => 'nullable|exists:categories,id',
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Handle image upload if new image is provided
            if ($request->hasFile('image_src')) {
                // Delete old image if it exists
                $oldImagePath = str_replace(asset('storage/'), '', $banner->image_src);
                if (Storage::disk('public')->exists($oldImagePath)) {
                    Storage::disk('public')->delete($oldImagePath);
                }
                
                // Store new image
                $image = $request->file('image_src');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $imagePath = $image->storeAs('category_banners', $imageName, 'public');
                $banner->image_src = asset('storage/' . $imagePath);
            }

            // Update banner fields
            if ($request->has('title')) $banner->title = $request->input('title');
            if ($request->has('subtitle')) $banner->subtitle = $request->input('subtitle');
            if ($request->has('description')) $banner->description = $request->input('description');
            if ($request->has('bg_color')) $banner->bg_color = $request->input('bg_color');
            if ($request->has('image_alt')) $banner->image_alt = $request->input('image_alt');
            if ($request->has('link_url')) $banner->link_url = $request->input('link_url');
            if ($request->has('btn_text')) $banner->btn_text = $request->input('btn_text');
            if ($request->has('is_active')) $banner->is_active = $request->input('is_active');
            if ($request->has('display_order')) $banner->display_order = $request->input('display_order');
            if ($request->has('start_date')) $banner->start_date = $request->input('start_date');
            if ($request->has('end_date')) $banner->end_date = $request->input('end_date');
            if ($request->has('category_id')) $banner->category_id = $request->input('category_id');

            $banner->save();

            // Load category if exists
            if ($banner->category_id) {
                $banner->load('category');
            }

            return $this->success(new CategoryBannerResource($banner), 'Category banner updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Delete a category banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function adminDeleteCategoryBanner(int $id): JsonResponse
    {
        try {
            $banner = CategoryBanner::find($id);
            
            if (!$banner) {
                return $this->error('Category banner not found', 'The requested category banner does not exist', 404);
            }

            // Delete the image file if it exists
            $imagePath = str_replace(asset('storage/'), '', $banner->image_src);
            if (Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
            }

            // Delete the banner
            $banner->delete();

            return $this->success(null, 'Category banner deleted successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Update banner status (active/inactive)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function adminUpdateBannerStatus(Request $request, int $id): JsonResponse
    {
        try {
            $banner = CategoryBanner::find($id);
            
            if (!$banner) {
                return $this->error('Category banner not found', 'The requested category banner does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $banner->is_active = $request->input('is_active');
            $banner->save();

            return $this->success(new CategoryBannerResource($banner), 'Banner status updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Admin: Update banner display order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function adminUpdateBannersOrder(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'banners' => 'required|array',
                'banners.*.id' => 'required|exists:category_banners,id',
                'banners.*.display_order' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            foreach ($request->input('banners') as $bannerData) {
                CategoryBanner::where('id', $bannerData['id'])
                    ->update(['display_order' => $bannerData['display_order']]);
            }

            return $this->success(null, 'Banner order updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 