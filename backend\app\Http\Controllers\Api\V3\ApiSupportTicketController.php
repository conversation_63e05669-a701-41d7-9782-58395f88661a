<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\TicketCategoryCollection;
use App\Http\Resources\V3\TicketResource;
use App\Http\Resources\V3\TicketCollection;
use App\Http\Resources\V3\TicketStatisticsResource;
use App\Models\SupportTicket;
use App\Models\TicketMessage;
use App\Services\SupportTicketService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\TicketCategory;
use App\Models\TicketAttachment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Helpers\NotificationHelper;
use App\Utility\ApiAizUploadUtility;

class ApiSupportTicketController extends ApiResponse
{
    protected $supportTicketService;

    /**
     * Create a new controller instance.
     *
     * @param SupportTicketService $supportTicketService
     * @return void
     */
    public function __construct(SupportTicketService $supportTicketService)
    {
        parent::__construct();
        $this->supportTicketService = $supportTicketService;
    }

    /**
     * Get all active ticket categories
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        try {
            $categories = $this->supportTicketService->getActiveCategories();
            return response()->json([
                'success' => true,
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new support ticket
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createTicket(Request $request)
    {
        try {
            // Log initial request
            \Log::info('Support ticket creation request started', [
                'user' => Auth::id(),
                'hasAttachments' => $request->hasFile('attachments')
            ]);
            
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:ticket_categories,id',
                'subject' => 'required|string|max:255',
                'description' => 'required|string',
                'priority' => 'required|in:low,medium,high',
                'attachments' => 'nullable|array',
                'attachments.*' => 'file|max:10240' // 10MB max
            ]);

            if ($validator->fails()) {
                \Log::error('Support ticket creation validation failed', [
                    'errors' => $validator->errors()->messages()
                ]);
                return response()->json([
                    'success' => false, 
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()->messages()
                ], 422);
            }

            // Start transaction
            DB::beginTransaction();

            $ticket = SupportTicket::create([
                'user_id' => Auth::id(),
                'category_id' => $request->category_id,
                'subject' => $request->subject,
                'description' => $request->description,
                'priority' => $request->priority,
                'status' => 'open',
                'last_reply_at' => now()
            ]);

            \Log::info('Support ticket created', [
                'ticketId' => $ticket->id
            ]);

            // Create initial message
            $message = TicketMessage::create([
                'ticket_id' => $ticket->id,
                'user_id' => Auth::id(),
                'message' => $request->description,
                'is_read' => true,
                'is_staff_reply' => false
            ]);

            \Log::info('Initial message created', [
                'messageId' => $message->id
            ]);

            // Handle attachments using ApiAizUploadUtility
            if ($request->hasFile('attachments')) {
                \Log::info('Processing attachments for new ticket', [
                    'count' => count($request->file('attachments'))
                ]);
                
                $uploadUtility = new ApiAizUploadUtility();
                $result = $uploadUtility->multipleFileUpload($request->file('attachments'), Auth::id());
                
                if ($result['success']) {
                    $uploadIds = explode(',', $result['files']);
                    
                    foreach ($uploadIds as $uploadId) {
                        $upload = \App\Models\Upload::find($uploadId);
                        if ($upload) {
                            \Log::info('Creating attachment record', [
                                'uploadId' => $uploadId,
                                'fileName' => $upload->file_original_name
                            ]);
                            
                            // Create the attachment record with actual file path stored
                            $attachment = TicketAttachment::create([
                                'ticket_id' => $ticket->id,
                                'message_id' => $message->id,
                                'upload_id' => $uploadId, // Store upload ID for reference
                                'file_name' => $upload->file_original_name ?: 'Unknown',
                                'file_path' => $upload->file_name, // Store actual file path
                                'file_size' => $upload->file_size ?: 0,
                                'file_type' => $upload->type ?: 'unknown',
                                'uploaded_by' => Auth::id()
                            ]);
                            
                            \Log::info('Attachment record created', [
                                'attachmentId' => $attachment->id,
                                'uploadId' => $uploadId
                            ]);
                        }
                    }
                } else {
                    \Log::error('File upload failed using ApiAizUploadUtility');
                    throw new \Exception('Failed to upload attachments');
                }
            }

            // Notify the user
            try {
                NotificationHelper::sendTicketCreatedNotification($ticket);
            } catch (\Exception $e) {
                \Log::warning('Failed to send notification', [
                    'error' => $e->getMessage()
                ]);
                // Don't throw this exception, just log it
            }

            // Commit transaction
            DB::commit();
            
            \Log::info('Support ticket creation complete', [
                'ticketId' => $ticket->id,
                'hasAttachments' => $request->hasFile('attachments')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Ticket created successfully',
                'data' => $ticket->load(['category', 'messages', 'attachments'])
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            \Log::error('Support ticket creation error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create ticket: ' . $e->getMessage(),
                'error_detail' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    /**
     * Get tickets with filtering, sorting and pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTickets(Request $request)
    {
        $query = SupportTicket::with([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ])
        ->where('user_id', Auth::id());

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        $tickets = $query->latest()->paginate(10);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $tickets->getCollection()->each(function($ticket) {
            $ticket->messages->each(function($message) {
                $message->attachments = $message->attachments->map(function($attachment) {
                    // Transform file_path to complete URL using uploaded_asset function
                    $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                    return $attachment;
                });
            });
        });
        
        return response()->json(['success' => true, 'data' => $tickets]);
    }

    /**
     * Get a specific ticket by ID
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicket($id)
    {
        $ticket = SupportTicket::with([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ])
        ->where('user_id', Auth::id())
        ->findOrFail($id);

        // Transform attachments to include file_url attribute
        $ticket->messages->each(function($message) {
            $message->attachments->each(function($attachment) {
                $attachment->append('file_url');
            });
        });
        
        // Also add file_url to ticket-level attachments
        $ticket->attachments->each(function($attachment) {
            $attachment->append('file_url');
        });

        return response()->json(['success' => true, 'data' => $ticket]);
    }

    /**
     * Add a reply to a ticket
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addReply(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ticketId' => 'required|exists:tickets,id',
            'message' => 'required|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx,mp4,mov,avi,mkv,webm|max:10240',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();
            $attachments = [];
            if ($request->hasFile('attachments')) {
                $upload = new \App\Utility\ApiAizUploadUtility();
                $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                if ($result['success'] == true) {
                    $attachments = $result['files'];
                }
            }
            
            $ticket = SupportTicket::findOrFail($request->ticketId);
            $ticketReply = $this->supportTicketService->addReply($request->all(), $ticket->id, $attachments);
            $ticketUpdate = SupportTicket::find($request->ticketId);
            
            DB::commit();
            return $this->success(new TicketResource($ticketUpdate));

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while adding the reply',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Close a ticket
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function close(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ticketId' => 'required|exists:tickets,id',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();
            $ticket = SupportTicket::findOrFail($request->ticketId);
            
            if ($ticket->status === 'closed') {
                return $this->error(
                    'ALREADY_CLOSED',
                    'Ticket is already closed',
                    '',
                    400
                );
            }

            $this->supportTicketService->addReply($request->all(), $ticket->id, null, 'closed');
            $ticketUpdate = SupportTicket::find($request->ticketId);
            
            DB::commit();
            return $this->success(new TicketResource($ticketUpdate));

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while closing the ticket',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Reopen a ticket
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reopen(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ticketId' => 'required|exists:tickets,id',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();
            $ticket = SupportTicket::findOrFail($request->ticketId);
            
            if ($ticket->status !== 'closed') {
                return $this->error(
                    'INVALID_STATUS',
                    'Only closed tickets can be reopened',
                    '',
                    400
                );
            }

            $this->supportTicketService->addReply($request->all(), $ticket->id, null, 'open');
            $ticketUpdate = SupportTicket::find($request->ticketId);
            
            DB::commit();
            return $this->success(new TicketResource($ticketUpdate));

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while reopening the ticket',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get ticket statistics
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTicketStats(Request $request)
    {
        try {
            // Check if user is admin or staff
            $user = auth()->user();
           /* if (!in_array($user->user_type, ['admin', 'staff'])) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You do not have permission to access ticket statistics',
                    '',
                    403
                );
            }*/

            $validator = Validator::make($request->all(), [
                'dateFrom' => 'nullable|date_format:Y-m-d',
                'dateTo' => 'nullable|date_format:Y-m-d',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }

            $filters = $request->only(['dateFrom', 'dateTo']);
            $statistics = $this->supportTicketService->getTicketStatistics($filters);

            return $this->success(new TicketStatisticsResource($statistics));
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving ticket statistics',
                $e->getMessage(),
                500
            );
        }
    }

    // public function getTicketByCode($code)
    // {
    //     $ticket = SupportTicket::where('code', $code)->first();
    //     return $this->success(new TicketResource($ticket));
    // }

    public function getMessages($ticketId)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        
        $messages = TicketMessage::with(['user', 'attachments'])
            ->where('ticket_id', $ticketId)
            ->latest()
            ->paginate(20);

        return response()->json(['success' => true, 'data' => $messages]);
    }

    /**
     * Add a reply to a ticket
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addMessage(Request $request, $ticketId)
    {
        try {
            // Log initial request
            \Log::info('Support ticket message request started', [
                'ticketId' => $ticketId,
                'user' => Auth::id(),
                'hasAttachments' => $request->hasFile('attachments')
            ]);
            
            $validator = Validator::make($request->all(), [
                'message' => 'required|string',
                'attachments' => 'nullable|array',
                'attachments.*' => 'file|max:10240' // 10MB max
            ]);

            if ($validator->fails()) {
                \Log::error('Support ticket message validation failed', [
                    'errors' => $validator->errors()->messages()
                ]);
                return response()->json([
                    'success' => false, 
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()->messages()
                ], 422);
            }

            $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
            
            if ($ticket->status === 'closed') {
                return response()->json(['success' => false, 'message' => 'Cannot add message to a closed ticket'], 422);
            }

            // Start transaction
            DB::beginTransaction();
            
            $message = TicketMessage::create([
                'ticket_id' => $ticketId,
                'user_id' => Auth::id(),
                'message' => $request->message,
                'is_read' => true,
                'is_staff_reply' => false
            ]);

            \Log::info('Support ticket message created', [
                'ticketId' => $ticketId,
                'messageId' => $message->id
            ]);

            // Handle attachments using ApiAizUploadUtility
            if ($request->hasFile('attachments')) {
                \Log::info('Processing attachments', [
                    'count' => count($request->file('attachments'))
                ]);
                
                $uploadUtility = new ApiAizUploadUtility();
                $result = $uploadUtility->multipleFileUpload($request->file('attachments'), Auth::id());
                
                if ($result['success']) {
                    $uploadIds = explode(',', $result['files']);
                    
                    foreach ($uploadIds as $uploadId) {
                        $upload = \App\Models\Upload::find($uploadId);
                        if ($upload) {
                            \Log::info('Creating attachment record', [
                                'uploadId' => $uploadId,
                                'fileName' => $upload->file_original_name
                            ]);
                            
                            // Create the attachment record with actual file path stored
                            $attachment = TicketAttachment::create([
                                'ticket_id' => $ticketId,
                                'message_id' => $message->id,
                                'upload_id' => $uploadId, // Store upload ID for reference
                                'file_name' => $upload->file_original_name ?: 'Unknown',
                                'file_path' => $upload->file_name, // Store actual file path
                                'file_size' => $upload->file_size ?: 0,
                                'file_type' => $upload->type ?: 'unknown',
                                'uploaded_by' => Auth::id()
                            ]);
                            
                            \Log::info('Attachment record created', [
                                'attachmentId' => $attachment->id,
                                'uploadId' => $uploadId
                            ]);
                        }
                    }
                } else {
                    \Log::error('File upload failed using ApiAizUploadUtility');
                    throw new \Exception('Failed to upload attachments');
                }
            }

            // Update ticket's last reply timestamp
            $ticket->update(['last_reply_at' => now()]);
            
            // Commit transaction
            DB::commit();
            
            \Log::info('Support ticket message complete', [
                'ticketId' => $ticketId,
                'messageId' => $message->id,
                'hasAttachments' => $request->hasFile('attachments')
            ]);

            // Load the message with attachments for the response
            $messageWithAttachments = $message->load(['user', 'attachments']);
            
            // Transform attachments to have complete URLs using uploaded_asset function
            $messageWithAttachments->attachments = $messageWithAttachments->attachments->map(function($attachment) {
                $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                return $attachment;
            });
            
            return response()->json([
                'success' => true,
                'message' => 'Message added successfully',
                'data' => $messageWithAttachments
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            \Log::error('Support ticket message error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add message: ' . $e->getMessage(),
                'error_detail' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function getOpenTickets()
    {
        $tickets = SupportTicket::with([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ])
        ->where('user_id', Auth::id())
        ->where('status', '!=', 'closed')
        ->latest()
        ->paginate(10);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $tickets->getCollection()->each(function($ticket) {
            $ticket->messages->each(function($message) {
                $message->attachments = $message->attachments->map(function($attachment) {
                    // Transform file_path to complete URL using uploaded_asset function
                    $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                    return $attachment;
                });
            });
        });
        
        return response()->json(['success' => true, 'data' => $tickets]);
    }

    public function getOpenTicketsCount()
    {
        $count = SupportTicket::where('user_id', Auth::id())
            ->where('status', '!=', 'closed')
            ->count();
        return response()->json(['success' => true, 'data' => ['count' => $count]]);
    }

    public function searchTickets(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $tickets = SupportTicket::with([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ])
        ->where('user_id', Auth::id())
        ->where(function ($query) use ($request) {
            $query->where('subject', 'like', '%' . $request->query . '%')
                ->orWhere('description', 'like', '%' . $request->query . '%');
        })
        ->latest()
        ->paginate(10);

        // Transform attachments to have complete URLs using uploaded_asset function
        $tickets->getCollection()->each(function($ticket) {
            $ticket->messages->each(function($message) {
                $message->attachments = $message->attachments->map(function($attachment) {
                    // Transform file_path to complete URL using uploaded_asset function
                    $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                    return $attachment;
                });
            });
        });

        return response()->json(['success' => true, 'data' => $tickets]);
    }

    public function getTicketStatistics()
    {
        $userId = Auth::id();
        
        $statistics = [
            'total' => SupportTicket::where('user_id', $userId)->count(),
            'open' => SupportTicket::where('user_id', $userId)->where('status', 'open')->count(),
            'in_progress' => SupportTicket::where('user_id', $userId)->where('status', 'in_progress')->count(),
            'closed' => SupportTicket::where('user_id', $userId)->where('status', 'closed')->count(),
            'recent_tickets' => SupportTicket::with([
                'category', 
                'messages' => function($query) {
                    $query->with('attachments');
                }
            ])
            ->where('user_id', $userId)
            ->latest()
            ->take(5)
            ->get()
            ->each(function($ticket) {
                $ticket->messages->each(function($message) {
                    $message->attachments = $message->attachments->map(function($attachment) {
                        // Transform file_path to complete URL using uploaded_asset function
                        $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                        return $attachment;
                    });
                });
            })
        ];

        return response()->json(['success' => true, 'data' => $statistics]);
    }

    public function updateTicket(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'priority' => 'required|in:low,medium,high'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($id);
        
        if ($ticket->status === 'closed') {
            return response()->json(['success' => false, 'message' => 'Cannot update a closed ticket'], 422);
        }

        $ticket->update([
            'subject' => $request->subject,
            'priority' => $request->priority
        ]);

        $ticketWithAttachments = $ticket->load([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ]);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $ticketWithAttachments->messages->each(function($message) {
            $message->attachments = $message->attachments->map(function($attachment) {
                // Transform file_path to complete URL using uploaded_asset function
                $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                return $attachment;
            });
        });

        return response()->json([
            'success' => true,
            'message' => 'Ticket updated successfully',
            'data' => $ticketWithAttachments
        ]);
    }

    /**
     * Update ticket status
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:open,in_progress,closed,resolved'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($id);

        $ticket->update(['status' => $request->status]);

        $ticketWithAttachments = $ticket->load([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ]);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $ticketWithAttachments->messages->each(function($message) {
            $message->attachments = $message->attachments->map(function($attachment) {
                // Transform file_path to complete URL using uploaded_asset function
                $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                return $attachment;
            });
        });

        return response()->json([
            'success' => true,
            'message' => 'Ticket status updated successfully',
            'data' => $ticketWithAttachments
        ]);
    }

    public function closeTicket($id)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($id);
        
        if ($ticket->status === 'closed') {
            return response()->json(['success' => false, 'message' => 'Ticket is already closed'], 422);
        }

        $ticket->update(['status' => 'closed']);

        $ticketWithAttachments = $ticket->load([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ]);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $ticketWithAttachments->messages->each(function($message) {
            $message->attachments = $message->attachments->map(function($attachment) {
                // Transform file_path to complete URL using uploaded_asset function
                $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                return $attachment;
            });
        });

        return response()->json([
            'success' => true,
            'message' => 'Ticket closed successfully',
            'data' => $ticketWithAttachments
        ]);
    }

    public function reopenTicket($id)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($id);
        
        if ($ticket->status !== 'closed') {
            return response()->json(['success' => false, 'message' => 'Ticket is not closed'], 422);
        }

        $ticket->update(['status' => 'open']);

        $ticketWithAttachments = $ticket->load([
            'category', 
            'messages' => function($query) {
                $query->with('attachments');
            }, 
            'attachments'
        ]);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $ticketWithAttachments->messages->each(function($message) {
            $message->attachments = $message->attachments->map(function($attachment) {
                // Transform file_path to complete URL using uploaded_asset function
                $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
                return $attachment;
            });
        });

        return response()->json([
            'success' => true,
            'message' => 'Ticket reopened successfully',
            'data' => $ticketWithAttachments
        ]);
    }

    public function updateMessage(Request $request, $ticketId, $messageId)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        
        if ($ticket->status === 'closed') {
            return response()->json(['success' => false, 'message' => 'Cannot update message in a closed ticket'], 422);
        }

        $message = TicketMessage::where('ticket_id', $ticketId)
            ->where('user_id', Auth::id())
            ->findOrFail($messageId);

        $message->update(['message' => $request->message]);

        $messageWithAttachments = $message->load(['user', 'attachments']);
        
        // Transform attachments to have complete URLs using uploaded_asset function
        $messageWithAttachments->attachments = $messageWithAttachments->attachments->map(function($attachment) {
            $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
            return $attachment;
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Message updated successfully',
            'data' => $messageWithAttachments
        ]);
    }

    public function deleteMessage($ticketId, $messageId)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        
        if ($ticket->status === 'closed') {
            return response()->json(['success' => false, 'message' => 'Cannot delete message in a closed ticket'], 422);
        }

        $message = TicketMessage::where('ticket_id', $ticketId)
            ->where('user_id', Auth::id())
            ->findOrFail($messageId);

        // Delete associated attachments
        foreach ($message->attachments as $attachment) {
            Storage::delete($attachment->file_path);
            $attachment->delete();
        }

        $message->delete();

        return response()->json([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    }

    public function markMessageAsRead($ticketId, $messageId)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        $message = TicketMessage::where('ticket_id', $ticketId)->findOrFail($messageId);

        $message->update(['is_read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Message marked as read'
        ]);
    }

    public function getAttachments($ticketId)
    {
        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        
        $attachments = TicketAttachment::with('uploader')
            ->where('ticket_id', $ticketId)
            ->latest()
            ->paginate(20);

        // Transform file_paths to complete URLs using uploaded_asset function
        $attachments->getCollection()->transform(function($attachment) {
            $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
            return $attachment;
        });

        return response()->json(['success' => true, 'data' => $attachments]);
    }

    public function uploadAttachment(Request $request, $ticketId)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240' // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()], 422);
        }

        $ticket = SupportTicket::where('user_id', Auth::id())->findOrFail($ticketId);
        
        if ($ticket->status === 'closed') {
            return response()->json(['success' => false, 'message' => 'Cannot upload attachment to a closed ticket'], 422);
        }

        // Use ApiAizUploadUtility for consistent file handling
        $uploadUtility = new ApiAizUploadUtility();
        
        // Create a temporary request object for the upload utility
        $tempRequest = new \Illuminate\Http\Request();
        $tempRequest->files->set('aiz_file', $request->file('file'));
        
        $result = $uploadUtility->upload($tempRequest, Auth::id());
        
        if (!$result || !$result['success']) {
            return response()->json([
                'success' => false, 
                'message' => 'Failed to upload file'
            ], 500);
        }
        
        $uploadId = $result['file_path']; // This is actually the upload ID
        $upload = \App\Models\Upload::find($uploadId);
        
        if (!$upload) {
            return response()->json([
                'success' => false, 
                'message' => 'Upload record not found'
            ], 500);
        }

        $attachment = TicketAttachment::create([
            'ticket_id' => $ticketId,
            'upload_id' => $uploadId, // Store upload ID for reference
            'file_name' => $upload->file_original_name ?: 'Unknown',
            'file_path' => $upload->file_name, // Store actual file path
            'file_size' => $upload->file_size ?: 0,
            'file_type' => $upload->type ?: 'unknown',
            'uploaded_by' => Auth::id()
        ]);

        // Transform file_path to complete URL using uploaded_asset function
        $attachment->file_path = $attachment->file_url; // Uses uploaded_asset() via getFileUrlAttribute
        
        return response()->json([
            'success' => true,
            'message' => 'Attachment uploaded successfully',
            'data' => $attachment->load('uploader')
        ]);
    }

    /**
     * Download attachment
     *
     * @param int $attachmentId
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function downloadAttachment($attachmentId)
    {
        try {
            // Find the attachment and verify ownership
            $attachment = TicketAttachment::whereHas('ticket', function ($query) {
                $query->where('user_id', Auth::id());
            })->findOrFail($attachmentId);

            // If we have an upload_id, use the uploaded_asset function
            if ($attachment->upload_id) {
                $upload = \App\Models\Upload::find($attachment->upload_id);
                if ($upload) {
                    // For DigitalOcean Spaces or S3, redirect to the direct URL
                    $fileUrl = uploaded_asset($attachment->upload_id);
                    if ($fileUrl) {
                        return response()->json([
                            'success' => true,
                            'download_url' => $fileUrl,
                            'file_name' => $attachment->file_name
                        ]);
                    }
                }
            }

            // Fallback: try to serve from local storage if file_path exists
            if ($attachment->file_path) {
                $filePath = str_replace('/storage/', '', $attachment->file_path);
                
                if (Storage::disk('public')->exists($filePath)) {
                    return Storage::disk('public')->download($filePath, $attachment->file_name);
                }
            }

            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Attachment download error: ' . $e->getMessage(), [
                'attachmentId' => $attachmentId,
                'userId' => Auth::id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error downloading attachment'
            ], 500);
        }
    }
}
