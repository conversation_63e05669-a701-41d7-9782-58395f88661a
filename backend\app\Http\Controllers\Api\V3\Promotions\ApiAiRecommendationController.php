<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\AiRecommendation\AiRecommendationResource;
use App\Models\Promotions\AiRecommendation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiAiRecommendationController extends ApiResponse
{
    /**
     * Get recommendations for the current user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserRecommendations(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'nullable|string|in:personalized,trending,similar,popular,recently_viewed',
                'limit' => 'integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $userId = Auth::id();
            $type = $request->input('type');
            $limit = $request->input('limit', 10);
            
            if (!$userId) {
                return $this->error('Unauthorized', 'Authentication required for personalized recommendations', 401);
            }
            
            $query = AiRecommendation::where('user_id', $userId)
                ->where('status', 'active')
                ->with('products');
                
            if ($type) {
                $query->where('recommendation_type', $type);
            }
            
            $recommendations = $query->orderBy('generated_date', 'desc')
                ->limit($limit)
                ->get();
                
            // Record that recommendations were shown to the user
            foreach ($recommendations as $recommendation) {
                $recommendation->recordShown();
            }
            
            return $this->success(AiRecommendationResource::collection($recommendations));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get recommendations by type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendationsByType(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:trending,popular,new_arrivals,best_sellers,flash_sale',
                'limit' => 'integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $type = $request->input('type');
            $limit = $request->input('limit', 10);
            $userId = Auth::id();
            
            // For non-personalized recommendation types, we can return them to any user
            $query = AiRecommendation::where('recommendation_type', $type)
                ->where('status', 'active')
                ->where('is_personalized', false)
                ->with('products');
                
            $recommendations = $query->orderBy('generated_date', 'desc')
                ->limit($limit)
                ->get();
                
            // Record that recommendations were shown to the user if authenticated
            if ($userId) {
                foreach ($recommendations as $recommendation) {
                    $recommendation->recordShown();
                }
            }
            
            return $this->success(AiRecommendationResource::collection($recommendations));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get similar product recommendations
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSimilarProductRecommendations(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $productId = $request->input('product_id');
            $limit = $request->input('limit', 8);
            $userId = Auth::id();
            
            // Look for a non-personalized similar products recommendation
            $query = AiRecommendation::where('recommendation_type', 'similar')
                ->where('status', 'active')
                ->whereJsonContains('product_ids', $productId)
                ->with('products');
                
            $recommendation = $query->first();
            
            if (!$recommendation) {
                return $this->error('Not found', 'No similar product recommendations available', 404);
            }
            
            // Record that recommendation was shown to the user if authenticated
            if ($userId) {
                $recommendation->recordShown();
            }
            
            return $this->success(new AiRecommendationResource($recommendation));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Record a click on a recommendation
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recordClick(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'recommendation_id' => 'required|integer|exists:ai_recommendations,id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $recommendationId = $request->input('recommendation_id');
            
            $recommendation = AiRecommendation::find($recommendationId);
            $recommendation->recordClicked();
            
            return $this->success(['message' => 'Click recorded successfully']);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Record a conversion on a recommendation
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recordConversion(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'recommendation_id' => 'required|integer|exists:ai_recommendations,id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $recommendationId = $request->input('recommendation_id');
            
            $recommendation = AiRecommendation::find($recommendationId);
            $recommendation->recordConversion();
            
            return $this->success(['message' => 'Conversion recorded successfully']);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a specific recommendation by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getRecommendation(int $id): JsonResponse
    {
        try {
            $recommendation = AiRecommendation::with('products')->find($id);
            
            if (!$recommendation) {
                return $this->error('Not found', 'Recommendation not found', 404);
            }
            
            // If this is a personalized recommendation, check if it belongs to the current user
            if ($recommendation->is_personalized) {
                $userId = Auth::id();
                
                if (!$userId || $recommendation->user_id !== $userId) {
                    return $this->error('Forbidden', 'You do not have permission to view this recommendation', 403);
                }
            }
            
            // Record that recommendation was shown
            $recommendation->recordShown();
            
            return $this->success(new AiRecommendationResource($recommendation));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 