<?php

namespace App\Http\Resources\V3\Coupon;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Category;
use App\Models\Product;

class PersonalizedCouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Parse details JSON
        $details = json_decode($this->details, true) ?? [];
        
        // Parse applicable_to JSON
        $applicableTo = $this->applicable_to ? json_decode($this->applicable_to, true) : null;
        
        return [
            'id' => (string) $this->id,
            'code' => $this->code,
            'type' => $this->discount_type == 'percent' ? 'percentage' : 'fixed',
            'value' => (float) $this->discount,
            'minPurchase' => isset($details['min_buy']) ? (float) $details['min_buy'] : null,
            'description' => $this->getDescription(),
            'startDate' => date('Y-m-d\TH:i:s\Z', $this->start_date),
            'endDate' => date('Y-m-d\TH:i:s\Z', $this->end_date),
            'status' => $this->isActive() ? 'active' : 'inactive',
            'applicableTo' => $this->formatApplicableTo($applicableTo),
            'isPersonalized' => $this->is_personalized,
            'image' => $this->image ? asset($this->image) : null
        ];
    }
    
    /**
     * Format the applicable_to field
     *
     * @param array|null $applicableTo
     * @return array|null
     */
    protected function formatApplicableTo($applicableTo)
    {
        if (!$applicableTo || !isset($applicableTo['type']) || !isset($applicableTo['ids'])) {
            return null;
        }
        
        $result = [
            'type' => $applicableTo['type'],
            'ids' => $applicableTo['ids'],
            'names' => []
        ];
        
        // Get names based on type
        if ($applicableTo['type'] === 'category') {
            $categories = Category::whereIn('id', $applicableTo['ids'])->get();
            $result['names'] = $categories->pluck('name')->toArray();
        } elseif ($applicableTo['type'] === 'product') {
            $products = Product::whereIn('id', $applicableTo['ids'])->get();
            $result['names'] = $products->pluck('name')->toArray();
        }
        
        return $result;
    }
}
