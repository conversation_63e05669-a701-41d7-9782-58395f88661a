<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Savings;
use App\Models\SavingsGoal;

class SavingsController extends Controller
{
    public function getSummary(Request $request)
    {
        $period = $request->get('period', 'month');
        
        $summary = Savings::getSummary($period);
        
        return response()->json($summary);
    }

    public function getHistory(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 5);
        $sort = $request->get('sort', 'date_desc');
        
        $history = Savings::getHistory($page, $limit, $sort);
        
        return response()->json($history);
    }

    public function getPriceComparison()
    {
        $comparison = Savings::getPriceComparison();
        
        return response()->json($comparison);
    }

    public function setSavingsGoal(Request $request)
    {
        $validated = $request->validate([
            'goal' => 'required|numeric|min:0',
            'target_date' => 'required|date'
        ]);

        $goal = SavingsGoal::updateOrCreate(
            ['user_id' => auth()->id()],
            [
                'amount' => $validated['goal'],
                'target_date' => $validated['target_date']
            ]
        );

        return response()->json($goal);
    }
} 