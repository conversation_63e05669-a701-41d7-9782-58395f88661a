<?php

namespace App\Http\Resources\V3\Notifications;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'title' => $this->title,
            'message' => $this->message,
            'date' => $this->created_at->toIso8601String(),
            'read' => (bool) $this->read,
            'priority' => $this->priority,
            'link' => $this->link,
            'linkText' => $this->link_text,
        ];
    }
}
