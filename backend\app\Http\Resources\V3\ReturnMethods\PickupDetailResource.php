<?php

namespace App\Http\Resources\V3\ReturnMethods;

use Illuminate\Http\Resources\Json\JsonResource;

class PickupDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'date' => $this->formatted_date,
            'timeSlot' => $this->formatted_time_slot,
            'address' => $this->address,
            'contactName' => $this->contact_name,
            'contactPhone' => $this->contact_phone,
            'instructions' => $this->instructions,
            'status' => $this->status
        ];
    }
}
