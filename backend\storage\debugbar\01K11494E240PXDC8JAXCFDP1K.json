{"__meta": {"id": "01K11494E240PXDC8JAXCFDP1K", "datetime": "2025-07-25 08:47:26", "utime": **********.786448, "method": "GET", "uri": "/buzfi-new-backend/api/v3/cart", "ip": "::1"}, "messages": {"count": 11, "messages": [{"message": "[08:47:26] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/cart\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.738041, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.738559, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.debug: getTempUserId: Found temp user ID in header {\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.73868, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Cart index - User identification {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.738728, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Getting cart {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.738773, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Getting or creating cart info {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.738814, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Created new cart info {\n    \"cart_info_id\": \"c5f735dd-3106-44d0-a1ca-382d37fec8f5\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.777492, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Found guest user cart items {\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"items_count\": 0,\n    \"items\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.778956, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Getting saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.780353, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Found saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.782107, "xdebug_link": null, "collector": "log"}, {"message": "[08:47:26] LOG.info: Cart retrieved successfully {\n    \"cart_info_id\": \"c5f735dd-3106-44d0-a1ca-382d37fec8f5\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753458426942_rqsaj2prd\",\n    \"items_count\": 0,\n    \"saved_items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.78217, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.549763, "end": **********.786464, "duration": 0.23670101165771484, "duration_str": "237ms", "measures": [{"label": "Booting", "start": **********.549763, "relative_start": 0, "end": **********.697378, "relative_end": **********.697378, "duration": 0.****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.697384, "relative_start": 0.*****************, "end": **********.786466, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "89.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.703014, "relative_start": 0.*****************, "end": **********.705283, "relative_end": **********.705283, "duration": 0.0022690296173095703, "duration_str": "2.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.784431, "relative_start": 0.*****************, "end": **********.784608, "relative_end": **********.784608, "duration": 0.00017690658569335938, "duration_str": "177μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.785492, "relative_start": 0.*****************, "end": **********.785525, "relative_end": **********.785525, "duration": 3.314018249511719e-05, "duration_str": "33μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.031079999999999997, "accumulated_duration_str": "31.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `cart_info` where `temp_user_id` = 'temp_1753458426942_rqsaj2prd' and `status` = 'active' and `user_id` is null limit 1", "type": "query", "params": [], "bindings": ["temp_1753458426942_rqsaj2prd", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, {"index": 17, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.740959, "duration": 0.026359999999999998, "duration_str": "26.36ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:125", "source": {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=125", "ajax": false, "filename": "EnhancedCartService.php", "line": "125"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 84.813}, {"sql": "insert into `cart_info` (`user_id`, `temp_user_id`, `status`, `currency`, `last_activity_at`, `session_id`, `expires_at`, `id`, `updated_at`, `created_at`) values (null, 'temp_1753458426942_rqsaj2prd', 'active', 'USD', '2025-07-25 08:47:26', 'NThUWO0x13230166whQlQpvHJzh8RxvJW1IiYNej', '2025-08-24 08:47:26', 'c5f735dd-3106-44d0-a1ca-382d37fec8f5', '2025-07-25 08:47:26', '2025-07-25 08:47:26')", "type": "query", "params": [], "bindings": [null, "temp_1753458426942_rqsaj2prd", "active", "USD", "2025-07-25 08:47:26", "NThUWO0x13230166whQlQpvHJzh8RxvJW1IiYNej", "2025-08-24 08:47:26", "c5f735dd-3106-44d0-a1ca-382d37fec8f5", "2025-07-25 08:47:26", "2025-07-25 08:47:26"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 143}, {"index": 14, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7744439, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:143", "source": {"index": 13, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=143", "ajax": false, "filename": "EnhancedCartService.php", "line": "143"}, "connection": "buzfi", "explain": null, "start_percent": 84.813, "width_percent": 8.044}, {"sql": "select * from `carts` where `temp_user_id` = 'temp_1753458426942_rqsaj2prd' and `status` = 'active' and `user_id` is null", "type": "query", "params": [], "bindings": ["temp_1753458426942_rqsaj2prd", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.77787, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:207", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=207", "ajax": false, "filename": "EnhancedCartService.php", "line": "207"}, "connection": "buzfi", "explain": null, "start_percent": 92.857, "width_percent": 1.963}, {"sql": "update `cart_info` set `item_count` = null, `total_quantity` = null, `subtotal` = 0, `tax_total` = 0, `shipping_total` = 0, `total` = 0, `cart_info`.`updated_at` = '2025-07-25 08:47:26' where `id` = 'c5f735dd-3106-44d0-a1ca-382d37fec8f5'", "type": "query", "params": [], "bindings": [null, null, 0, 0, 0, 0, "2025-07-25 08:47:26", "c5f735dd-3106-44d0-a1ca-382d37fec8f5"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 1341}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 220}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.779226, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:1341", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 1341}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=1341", "ajax": false, "filename": "EnhancedCartService.php", "line": "1341"}, "connection": "buzfi", "explain": null, "start_percent": 94.82, "width_percent": 1.995}, {"sql": "select * from `saved_items` where `temp_user_id` = 'temp_1753458426942_rqsaj2prd' and `user_id` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["temp_1753458426942_rqsaj2prd"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 223}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.780661, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:864", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=864", "ajax": false, "filename": "EnhancedCartService.php", "line": "864"}, "connection": "buzfi", "explain": null, "start_percent": 96.815, "width_percent": 3.185}]}, "models": {"data": {"App\\Models\\CartInfo": {"created": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCartInfo.php&line=1", "ajax": false, "filename": "CartInfo.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index", "uri": "GET api/v3/cart", "controller": "App\\Http\\Controllers\\Api\\V3\\CartController@index<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/CartController.php:28-68</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "236ms", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-851771955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-851771955\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-531806716 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-531806716\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-366980597 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-temp-user-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">temp_1753458426942_rqsaj2prd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"526 characters\">__stripe_mid=3f11140f-3aca-4245-a359-07ebb97886dc95fd6a; __stripe_sid=c48f7936-688b-4324-9bf6-24cfc967d06a540212; XSRF-TOKEN=eyJpdiI6Im83R2lDb0t1c1JFZ0c2c2tya1lBcWc9PSIsInZhbHVlIjoiWEt1emVQMHZ1SEdRaWRremV0WG4weVBsTHBLSUk4d3M4N0x6M3plYXBwVFNpWGVKT29TdGVJN1ZpbkhZSHRNb0VQeUd1aGtBcUZmY2pORk1ZNFBPeXZIZURrOVZlL2E0YzVNMVRlNDBsYzVtOHpjM3FrYmdJSGg3TmFRc2labnkiLCJtYWMiOiJjMDQzODY3YTlkNzFmMGE4ODUyNDg1YzJiOTRiNmZiOWJkZDM2M2QxYzQxN2YwZDFhM2E3ZTk5NjY1YTRiYTk5IiwidGFnIjoiIn0%3D; buzficom_session=0J3SYXeaTtsUGab9zLhVpLdKWIj8t0HaxSjrNec4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366980597\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1009957936 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21iBAlC5OhIcawDUh13CgSpJIksBP4SeW0t45BxF</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009957936\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-137216245 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:47:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">587</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InQ0eG11eTBuV2pML2ZvZEk2NnpySkE9PSIsInZhbHVlIjoiK2wvd2tWSTZPRCt4bmdTZ2Z0VWxQTmxRMnRmSHBGZGNxUW5oZlB0L25pRkk5VHlKOEtvc2lFWGNpM3VTUFJRN1RZSThkclZTVld1ZGZYb3pXd2FIbnAzcy9iRUlRdGV0Y1ZLazVsNEhMU05yVnAyMFFuUks5cEZMLzZsd0NMOG0iLCJtYWMiOiJjMTA0ZGE0ODgzYWM4YjE4N2ViZDIwOWQ1OTIwMGQ2Nzg5YjkzNDE4YzE0MjBmZWM0M2E4ZWUwOThhNzdiYWUxIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:47:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IkZiZ0FKRjlJT2VRR0hWMTYrdVcrWEE9PSIsInZhbHVlIjoiWk1HTjNVakMwME5IZkg5VUVCM2VNbFdnaWxmRUlFMjkzdXdGRm1od0JaRU15cmVzTUlvMlZVZmRkeVNjbC9uY3FxeTQxUHFHMEFYREF5bVQwTGo0a21DQ05OUlJxeGtaQWw0TTNrV3VPNWhUbTNjdzBsU1djZVpWS1MrcXJTTGEiLCJtYWMiOiI0MzJjMmU2MDYzNzU5YmU2YTU4MjI5YTczZTE4NTFkYjQxNzE5MTBhYTRkNzc2ZDg2ZjAxZDNkZjk2NjYwYmY0IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:47:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InQ0eG11eTBuV2pML2ZvZEk2NnpySkE9PSIsInZhbHVlIjoiK2wvd2tWSTZPRCt4bmdTZ2Z0VWxQTmxRMnRmSHBGZGNxUW5oZlB0L25pRkk5VHlKOEtvc2lFWGNpM3VTUFJRN1RZSThkclZTVld1ZGZYb3pXd2FIbnAzcy9iRUlRdGV0Y1ZLazVsNEhMU05yVnAyMFFuUks5cEZMLzZsd0NMOG0iLCJtYWMiOiJjMTA0ZGE0ODgzYWM4YjE4N2ViZDIwOWQ1OTIwMGQ2Nzg5YjkzNDE4YzE0MjBmZWM0M2E4ZWUwOThhNzdiYWUxIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:47:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IkZiZ0FKRjlJT2VRR0hWMTYrdVcrWEE9PSIsInZhbHVlIjoiWk1HTjNVakMwME5IZkg5VUVCM2VNbFdnaWxmRUlFMjkzdXdGRm1od0JaRU15cmVzTUlvMlZVZmRkeVNjbC9uY3FxeTQxUHFHMEFYREF5bVQwTGo0a21DQ05OUlJxeGtaQWw0TTNrV3VPNWhUbTNjdzBsU1djZVpWS1MrcXJTTGEiLCJtYWMiOiI0MzJjMmU2MDYzNzU5YmU2YTU4MjI5YTczZTE4NTFkYjQxNzE5MTBhYTRkNzc2ZDg2ZjAxZDNkZjk2NjYwYmY0IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:47:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137216245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-424664049 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">21iBAlC5OhIcawDUh13CgSpJIksBP4SeW0t45BxF</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/buzfi-new-backend/api/v3/cart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>temp_user_id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">temp_1753458426942_rqsaj2prd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424664049\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index"}, "badge": null}}