<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class TicketStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'total' => $this['total'],
            'open' => $this['open'],
            'inProgress' => $this['inProgress'],
            'waitingForCustomer' => $this['waitingForCustomer'],
            'waitingForSupport' => $this['waitingForSupport'],
            'resolved' => $this['resolved'],
            'closed' => $this['closed'],
            'averageResolutionTime' => $this['averageResolutionTime'],
            'byCategoryBreakdown' => $this['byCategoryBreakdown'],
            'byPriorityBreakdown' => $this['byPriorityBreakdown'],
        ];
    }
}
