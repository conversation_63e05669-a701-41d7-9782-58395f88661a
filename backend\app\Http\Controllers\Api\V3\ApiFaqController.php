<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\FaqCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class ApiFaqController extends ApiResponse
{
    public function index(Request $request)
    {
        $per_page = min((int)$request->input('per_page', 10), 50);
        $page = max((int)$request->input('page', 1), 1);
        $category_id = $request->input('category_id');

        $query = Faq::with('category')->where('is_active', true);

        if ($category_id) {
            $query->where('category_id', $category_id);
        }

        $faqs = $query->orderBy('position')
            ->paginate($per_page, ['*'], 'page', $page);

        return $this->success($faqs);
    }

    public function show($id)
    {
        $faq = Faq::with('category')
            ->where('is_active', true)
            ->findOrFail($id);

        $faq->incrementViewCount();

        return $this->success($faq);
    }

    public function search(Request $request)
    {
        $query = $request->input('query');
        $per_page = min((int)$request->input('per_page', 10), 50);
        $page = max((int)$request->input('page', 1), 1);

        $faqs = Faq::with('category')
            ->where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('question', 'like', "%{$query}%")
                    ->orWhere('answer', 'like', "%{$query}%");
            })
            ->orderBy('position')
            ->paginate($per_page, ['*'], 'page', $page);

        return $this->success($faqs);
    }

    public function popular()
    {
        $faqs = Cache::remember('popular_faqs', 3600, function () {
            return Faq::with('category')
                ->where('is_active', true)
                ->orderBy('view_count', 'desc')
                ->take(10)
                ->get();
        });

        return $this->success($faqs);
    }

    public function categories()
    {
        $categories = Cache::remember('faq_categories', 3600, function () {
            return FaqCategory::where('is_active', true)
                ->orderBy('position')
                ->get();
        });

        return $this->success($categories);
    }

    public function markHelpful($id)
    {
        $faq = Faq::findOrFail($id);
        $faq->incrementHelpfulCount();

        return $this->success(null, 'FAQ marked as helpful');
    }

    public function markNotHelpful($id)
    {
        $faq = Faq::findOrFail($id);
        $faq->incrementNotHelpfulCount();

        return $this->success(null, 'FAQ marked as not helpful');
    }
} 