<?php

namespace App\Http\Controllers\Api\V3\Messaging;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Messaging\Conversation;
use App\Models\Messaging\ConversationMessage;
use App\Models\Messaging\ConversationAttachment;
use App\Models\User;
use App\Events\Messaging\ConversationMessageSent;
use App\Events\Messaging\ConversationUpdated;
use App\Events\Messaging\UnreadCountUpdated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ConversationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Get all conversations for the authenticated user
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $search = $request->input('search', '');
        $limit = $request->input('limit', 20);
        
        $conversations = Conversation::where(function($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })
        ->with(['messages' => function($query) {
            $query->latest('created_at');
        }])
        ->withCount(['messages as unread_count' => function($query) use ($user) {
            $query->where('user_id', '!=', $user->id)
                  ->where('is_read', false);
        }])
        ->orderBy('updated_at', 'desc')
        ->paginate($limit);
        
        $formattedConversations = [];
        
        foreach ($conversations as $conversation) {
            // Get the other user in the conversation
            $participantId = $conversation->sender_id == $user->id 
                ? $conversation->receiver_id 
                : $conversation->sender_id;
                
            $participant = User::find($participantId);
            
            if (!$participant) {
                continue; // Skip if user not found
            }
            
            // Filter by search term if provided
            if ($search && stripos($participant->name, $search) === false) {
                continue;
            }
            
            // Get the last message
            $lastMessage = $conversation->messages->first();
            
            $formattedConversations[] = [
                'id' => $conversation->id,
                'participant' => [
                    'id' => $participant->id,
                    'name' => $participant->name,
                    'avatar' => uploaded_asset($participant->avatar_original),
                    'user_type' => $participant->user_type
                ],
                'last_message' => $lastMessage ? [
                    'id' => $lastMessage->id,
                    'content' => $lastMessage->content,
                    'created_at' => $lastMessage->created_at,
                    'is_from_me' => $lastMessage->user_id == $user->id
                ] : null,
                'unread_count' => $conversation->unread_count,
                'created_at' => $conversation->created_at,
                'updated_at' => $conversation->updated_at
            ];
        }
        
        return response()->json([
            'success' => true,
            'conversations' => $formattedConversations,
            'meta' => [
                'total' => $conversations->total(),
                'current_page' => $conversations->currentPage(),
                'last_page' => $conversations->lastPage(),
                'per_page' => $conversations->perPage()
            ]
        ]);
    }

    /**
     * Get a specific conversation with its messages
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Find conversation and check if user is a participant
        $conversation = Conversation::where(function($q) use ($user) {
            $q->where('user1_id', $user->id)
              ->orWhere('user2_id', $user->id);
        })->find($id);
        
        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found or you do not have access',
            ], 404);
        }
        
        // Get all messages in the conversation
        $messages = $conversation->messages()
            ->with(['sender', 'attachments'])
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function($message) use ($user) {
                return [
                    'id' => $message->id,
                    'content' => $message->content,
                    'user_id' => $message->sender_id,
                    'created_at' => $message->created_at,
                    'attachments' => $message->attachments->map(function($attachment) {
                        return [
                            'id' => $attachment->id,
                            'name' => $attachment->name,
                            'url' => $attachment->url,
                            'type' => $attachment->type,
                            'size' => $attachment->size,
                        ];
                    }),
                    'is_read' => $message->isReadBy($user->id),
                ];
            });
        
        // Determine the other participant
        $participant = ($conversation->user1_id == $user->id) 
            ? $conversation->user2 
            : $conversation->user1;
        
        // Get participant's avatar and user type
        $avatar = null;
        $userType = 'User';
        
        if ($participant->seller) {
            $userType = 'Seller';
            $avatar = uploaded_asset($participant->seller->logo);
        } elseif ($participant->customer) {
            $userType = 'Customer';
            $avatar = uploaded_asset($participant->avatar_original);
        } elseif ($participant->dropshipper) {
            $userType = 'Dropshipper';
            $avatar = uploaded_asset($participant->dropshipper->logo);
        }
        
        // Mark all unread messages as read
        $this->markMessagesAsRead($conversation, $user->id);
        
        // Format the response
        $formattedConversation = [
            'id' => $conversation->id,
            'participant' => [
                'id' => $participant->id,
                'name' => $participant->name,
                'avatar' => $avatar,
                'user_type' => $userType
            ],
            'created_at' => $conversation->created_at,
            'updated_at' => $conversation->updated_at
        ];
        
        return response()->json([
            'conversation' => $formattedConversation,
            'messages' => $messages
        ]);
    }

    /**
     * Create a new conversation or send a message to an existing one
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'content' => 'required_without:attachments|string|max:5000',
            'recipient_id' => 'required_without:conversation_id|exists:users,id',
            'conversation_id' => 'required_without:recipient_id|exists:conversations,id',
            'attachments.*' => 'nullable|file|max:10240', // 10MB max file size
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Get or create conversation
        $conversation = null;
        
        if ($request->filled('conversation_id')) {
            // Find existing conversation and check if user is a participant
            $conversation = Conversation::where(function($q) use ($user) {
                $q->where('user1_id', $user->id)
                  ->orWhere('user2_id', $user->id);
            })->find($request->conversation_id);
            
            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or you do not have access',
                ], 404);
            }
        } else {
            // Check if user can't message themselves
            if ($user->id == $request->recipient_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot create a conversation with yourself',
                ], 422);
            }
            
            // Check if conversation already exists
            $conversation = Conversation::where(function($q) use ($user, $request) {
                $q->where(function($q) use ($user, $request) {
                    $q->where('user1_id', $user->id)
                      ->where('user2_id', $request->recipient_id);
                })->orWhere(function($q) use ($user, $request) {
                    $q->where('user1_id', $request->recipient_id)
                      ->where('user2_id', $user->id);
                });
            })->first();
            
            if (!$conversation) {
                // Create new conversation
                $conversation = Conversation::create([
                    'user1_id' => $user->id,
                    'user2_id' => $request->recipient_id,
                ]);
            }
        }
        
        // Create message
        DB::beginTransaction();
        
        try {
            // Create message
            $message = $conversation->messages()->create([
                'sender_id' => $user->id,
                'content' => $request->content ?? '',
                'is_read' => false,
            ]);
            
            // Handle attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('conversation-attachments', 'public');
                    $url = Storage::disk('public')->url($path);
                    
                    $message->attachments()->create([
                        'name' => $file->getClientOriginalName(),
                        'url' => $url,
                        'type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                    ]);
                }
            }
            
            // Mark as read by sender
            $message->markAsReadBy($user->id);
            
            // Update conversation updated_at timestamp
            $conversation->touch();
            
            DB::commit();
            
            // Broadcast events
            broadcast(new ConversationMessageSent($message, $conversation->id))->toOthers();
            broadcast(new ConversationUpdated($conversation))->toOthers();
            
            // Broadcast unread count update for the other user
            $otherUserId = ($conversation->user1_id == $user->id) ? $conversation->user2_id : $conversation->user1_id;
            $this->broadcastUnreadCountUpdate($otherUserId);
            
            // Format the response
            $formattedMessage = [
                'id' => $message->id,
                'content' => $message->content,
                'user_id' => $message->sender_id,
                'created_at' => $message->created_at,
                'attachments' => $message->attachments->map(function($attachment) {
                    return [
                        'id' => $attachment->id,
                        'name' => $attachment->name,
                        'url' => $attachment->url,
                        'type' => $attachment->type,
                        'size' => $attachment->size,
                    ];
                })
            ];
            
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'conversation' => [
                    'id' => $conversation->id
                ],
                'message' => $formattedMessage
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark messages as read in bulk
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find conversation and check if user is a participant
        $conversation = Conversation::where(function($q) use ($user) {
            $q->where('user1_id', $user->id)
              ->orWhere('user2_id', $user->id);
        })->find($request->conversation_id);
        
        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found or you do not have access',
            ], 404);
        }
        
        // Mark messages as read
        $markedCount = $this->markMessagesAsRead($conversation, $user->id);
        
        return response()->json([
            'success' => true,
            'message' => 'Messages marked as read',
            'count' => $markedCount,
        ]);
    }

    /**
     * Helper method to mark messages as read in a conversation
     *
     * @param  \App\Models\Messaging\Conversation  $conversation
     * @param  int  $userId
     * @return int Number of messages marked as read
     */
    private function markMessagesAsRead($conversation, $userId)
    {
        // Find all unread messages sent by the other user
        $unreadMessages = $conversation->messages()
            ->where('sender_id', '!=', $userId)
            ->whereDoesntHave('readStatus', function($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->get();
        
        // Mark each message as read
        foreach ($unreadMessages as $message) {
            $message->markAsReadBy($userId);
        }
        
        // Broadcast unread count update
        $this->broadcastUnreadCountUpdate($userId);
        
        return $unreadMessages->count();
    }

    /**
     * Helper method to broadcast unread count update
     *
     * @param  int  $userId
     * @return void
     */
    private function broadcastUnreadCountUpdate($userId)
    {
        // Broadcast unread count update for user
        broadcast(new UnreadCountUpdated($userId))->toOthers();
    }

    /**
     * Get messages for a conversation with pagination
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMessages(Request $request, $id)
    {
        $user = Auth::user();
        
        // Find conversation and check if user is a participant
        $conversation = Conversation::where(function($q) use ($user) {
            $q->where('user1_id', $user->id)
              ->orWhere('user2_id', $user->id);
        })->find($id);
        
        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found or you do not have access',
            ], 404);
        }
        
        // Query parameters
        $perPage = $request->input('perPage', 50);
        $page = $request->input('page', 1);
        $sortDir = $request->input('sortDir', 'desc');
        
        // Get messages with pagination
        $messages = $conversation->messages()
            ->with(['sender', 'attachments', 'readStatus'])
            ->orderBy('created_at', $sortDir)
            ->paginate($perPage, ['*'], 'page', $page);
        
        return response()->json([
            'success' => true,
            'data' => $messages,
        ]);
    }

    /**
     * Send a message to a conversation
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMessage(Request $request, $id)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find conversation and check if user is a participant
        $conversation = Conversation::where(function($q) use ($user) {
            $q->where('user1_id', $user->id)
              ->orWhere('user2_id', $user->id);
        })->find($id);
        
        if (!$conversation) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found or you do not have access',
            ], 404);
        }
        
        DB::beginTransaction();
        
        try {
            // Create message
            $message = $conversation->messages()->create([
                'sender_id' => $user->id,
                'content' => $request->content,
            ]);
            
            // Handle attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('conversation-attachments', 'public');
                    $url = Storage::url($path);
                    
                    $message->attachments()->create([
                        'name' => $file->getClientOriginalName(),
                        'url' => $url,
                        'type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                    ]);
                }
            }
            
            // Mark as read by sender
            $message->markAsReadBy($user->id);
            
            // Update conversation updated_at timestamp
            $conversation->touch();
            
            DB::commit();
            
            // Load relationships for response
            $message->load(['sender', 'attachments']);
            
            // Broadcast events
            broadcast(new ConversationMessageSent($message, $conversation->id))->toOthers();
            broadcast(new ConversationUpdated($conversation))->toOthers();
            
            // Broadcast unread count update for the other user
            $otherUserId = ($conversation->user1_id == $user->id) ? $conversation->user2_id : $conversation->user1_id;
            $this->broadcastUnreadCountUpdate($otherUserId);
            
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message,
            ], 201);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get unread message counts for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCounts()
    {
        $user = Auth::user();
        
        $counts = [];
        
        // Count of unread messages for each conversation
        $conversations = Conversation::where(function($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })->withCount(['messages' => function($query) use ($user) {
            $query->where('sender_id', '!=', $user->id)
                  ->where('is_read', false);
        }])->get();
        
        $total = 0;
        
        foreach ($conversations as $conversation) {
            if ($conversation->messages_count > 0) {
                $counts[$conversation->id] = $conversation->messages_count;
                $total += $conversation->messages_count;
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'counts' => $counts,
                'total' => $total
            ]
        ]);
    }
    
    /**
     * Get contacts that the user can message
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContacts(Request $request)
    {
        $user = Auth::user();
        $search = $request->input('search', '');
        $userType = $request->input('user_type', 'all');
        $limit = $request->input('limit', 20);

        // Admin can message all users
        if ($user->user_type == 'admin') {
            $query = User::where('id', '!=', $user->id)
                ->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            
            // Filter by user type if requested
            if ($userType != 'all') {
                $query->where('user_type', $userType);
            }
            
            $contacts = $query->take($limit)->get();
            
            return response()->json([
                'success' => true,
                'data' => $contacts->map(function($contact) {
                    $type = 'User';
                    
                    if ($contact->hasRole('seller')) {
                        $type = 'Seller';
                    } elseif ($contact->hasRole('customer')) {
                        $type = 'Customer';
                    } elseif ($contact->hasRole('dropshipper')) {
                        $type = 'Dropshipper';
                    }
                    
                    return [
                        'id' => $contact->id,
                        'name' => $contact->name,
                        'email' => $contact->email,
                        'avatar' => uploaded_asset($contact->avatar_original),
                        'type' => $type,
                        'user_type' => $contact->user_type
                    ];
                })
            ]);
        }
        
        // Non-admin users should be more restricted in who they can message
        return response()->json([
            'success' => false,
            'message' => 'Permission denied'
        ], 403);
    }
} 