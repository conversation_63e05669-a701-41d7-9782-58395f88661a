<?php

namespace App\Http\Controllers\Api\V3\Pusher;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Pusher\Pusher;

class ApiPusherController extends Controller
{
    /**
     * Authenticate a user for Pusher private channels
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function authenticate(Request $request)
    {
        $socketId = $request->socket_id;
        $channelName = $request->channel_name;
        
        // Get Pusher credentials from config
        $appId = config('broadcasting.connections.pusher.app_id');
        $appKey = config('broadcasting.connections.pusher.key');
        $appSecret = config('broadcasting.connections.pusher.secret');
        $options = [
            'cluster' => config('broadcasting.connections.pusher.options.cluster'),
            'useTLS' => true
        ];
        
        // Create Pusher instance
        $pusher = new Pusher($appKey, $appSecret, $appId, $options);
        
        // For private channels
        if (strpos($channelName, 'private-') === 0) {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthenticated'
                ], 401);
            }
            
            // Extract the channel user id from the channel name
            // Format: private-user-{user_id}
            $channelParts = explode('-', $channelName);
            $channelUserId = end($channelParts);
            
            // Ensure user is only accessing their own channels
            if ($channelParts[1] === 'user' && $channelUserId != $user->id) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }
            
            // Generate authentication response
            $auth = $pusher->socket_auth($channelName, $socketId);
            
            return response()->json(json_decode($auth, true));
        }
        
        // For presence channels
        if (strpos($channelName, 'presence-') === 0) {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => 'Unauthenticated'
                ], 401);
            }
            
            // User data to be sent with the presence channel
            $userData = [
                'user_id' => $user->id,
                'user_info' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'avatar' => $user->avatar,
                    'type' => $user->user_type // customer, dropshipper, etc.
                ]
            ];
            
            // Generate authentication response for presence channel
            $auth = $pusher->presence_auth($channelName, $socketId, $user->id, $userData);
            
            return response()->json(json_decode($auth, true));
        }
        
        return response()->json([
            'status' => false,
            'message' => 'Invalid channel type'
        ], 400);
    }
    
    /**
     * Send a test notification to a specific channel
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendTestNotification(Request $request)
    {
        $request->validate([
            'channel' => 'required|string',
            'event' => 'required|string',
            'data' => 'required'
        ]);
        
        try {
            // Get Pusher credentials from config
            $appId = config('broadcasting.connections.pusher.app_id');
            $appKey = config('broadcasting.connections.pusher.key');
            $appSecret = config('broadcasting.connections.pusher.secret');
            $options = [
                'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                'useTLS' => true
            ];
            
            // Create Pusher instance
            $pusher = new Pusher($appKey, $appSecret, $appId, $options);
            
            // Trigger event on channel
            $result = $pusher->trigger(
                $request->channel,
                $request->event,
                $request->data
            );
            
            return response()->json([
                'status' => true,
                'message' => 'Notification sent successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ], 500);
        }
    }
} 