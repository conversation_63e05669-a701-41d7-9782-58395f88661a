<?php

namespace App\Http\Resources\V3\Orders;

use App\Enums\OrderStatus;
use App\Http\Resources\V3\User\SellerProfileResource;
use App\Models\Coupon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class OrderInvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Get coupon details if applicable
        $discount = null;
        if ($this->coupon_discount > 0 && $this->coupon_id) {
            $coupon = Coupon::find($this->coupon_id);
            if ($coupon) {
                $discount = [
                    'code' => $coupon->code,
                    'amount' => (float) $this->coupon_discount
                ];
            }
        }

        // Format items
        $items = $this->orderDetails->map(function ($detail) {
            return [
                'id' => optional($detail->product)->slug,
                'productId' => optional($detail->product)->slug,
                'name' => $detail->product ? $detail->product->name : 'Product',
                'price' => (float) $detail->price,
                'quantity' => (int) $detail->quantity,
                'image' => $detail->product->thumbnail_img ? uploaded_asset($detail->product->thumbnail_img) : null,
                'sku' => $detail->product ? product_sku_by_product_id($detail->product, $detail->variation) : null
            ];
        });

        // Format payment method
        $paymentMethod = [
            'type' => $this->payment_type ?? 'unknown',
            'last4' => null,
            'brand' => null
        ];

        // If payment details are available, parse them
        if ($this->payment_details) {
            $paymentDetails = json_decode($this->payment_details, true);
            if (isset($paymentDetails['card_details'])) {
                $card = json_decode($paymentDetails['card_details'], true);
                if (isset($card['brand']) && isset($card['last4'])) {
                    $paymentMethod['last4'] = $card['last4'];
                    $paymentMethod['brand'] = ucfirst($card['brand']);
                }
            }
        }

        $seller = $this->getSellerInfo();

        // Format dates
        $orderDate = date('Y-m-d', strtotime($this->created_at));
        $formattedDate = date('F j, Y', strtotime($this->created_at));


        return [
            'id' => 'inv_' . $this->code ,
            'invoiceNumber' => $this->code ,
            'orderId' => $this->code ,
            'orderNumber' => $this->code,
            'date' => $orderDate,
            'dueDate' => $orderDate, // Due date is same as order date for e-commerce
            'orderStatus' => OrderStatus::getLabel($this->delivery_status),
            'paymentStatus' => $this->payment_status,
            'items' => $items,
            'subtotal' => (float) $this->orderDetails->sum('price'),
            'shipping' => (float) $this->orderDetails->sum('shipping_cost'),
            'discount' => $discount,
            'tax' => (float) $this->orderDetails->sum('tax'),
            'total' => (float) $this->grand_total,
            'paymentMethod' => $this->getReadablePaymentMethodDetails($this->payment_details, $this->payment_status),
            'shippingAddress' => $this->getShippingAddress($this->shipping_address),
            'billingAddress' => $this->getBillingAddress($this->payment_details,$this->shipping_address),
            'seller' => new SellerProfileResource($this->seller),
            'invoiceDate' => $orderDate,
            'formattedDate' => $formattedDate,
            'customerNotes' => $this->customer_note ?? '',
            'downloadUrl' => route('api.customer.orders.invoice.download', ['orderId' => $this->id])
        ];
    }

    /**
     * Get billing address from order
     *
     * @return array
     */


    /**
     * Get seller information from business settings
     *
     * @return array
     */
    private function getSellerInfo(): array
    {
        $businessName = get_setting('site_name') ?? 'Buzfi, Inc';
        $businessAddress = get_setting('address') ?? '123 E-Commerce St, New York, NY 10001';
        $businessTaxId = get_setting('tax_id') ?? 'TAX-12345-US';
        $businessPhone = get_setting('phone') ?? '+1987654321';
        $businessEmail = get_setting('email') ?? '<EMAIL>';

        return [
            'name' => $businessName,
            'address' => $businessAddress,
            'taxId' => $businessTaxId,
            'phone' => $businessPhone,
            'email' => $businessEmail
        ];
    }

    /**
     * Map order payment status to invoice status
     *
     * @param string|null $paymentStatus
     * @return string
     */
    private function mapOrderStatusToInvoiceStatus(?string $paymentStatus): string
    {
        switch ($paymentStatus) {
            case 'paid':
                return 'paid';
            case 'unpaid':
                return 'unpaid';
            case 'refunded':
                return 'refunded';
            case 'partially_paid':
                return 'partially_paid';
            default:
                return 'pending';
        }
    }
}
