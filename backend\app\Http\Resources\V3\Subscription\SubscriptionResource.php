<?php

namespace App\Http\Resources\V3\Subscription;

use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'email' => $this->email,
            'name' => $this->name,
            'subscription_type' => $this->subscription_type,
            'status' => $this->status,
            'preferences' => $this->preferences,
            'source' => $this->source,
            'frequency' => $this->frequency,
            'last_email_sent' => isset($this->last_email_sent) ? $this->last_email_sent->toIso8601String() : null,
            'subscription_date' => isset($this->subscription_date) ? $this->subscription_date->toIso8601String() : null,
            'unsubscribed_date' => isset($this->unsubscribed_date) ? $this->unsubscribed_date->toIso8601String() : null,
            'is_confirmed' => (bool) $this->is_confirmed,
            'user' => $this->when($this->relationLoaded('user') && $this->user, function() {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 