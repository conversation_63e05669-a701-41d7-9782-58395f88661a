<?php

namespace App\Http\Controllers\Api\V3\Products;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\ProductReviewRating\ProductReviewResource;
use App\Models\Product;
use App\Models\Review;
use App\Services\ProductReviewRatingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiRatingReviewController extends ApiResponse
{
    protected ProductReviewRatingService $productReviewRatingService;
    public function __construct(ProductReviewRatingService $productReviewRatingService)
    {
        parent::__construct();
        $this->productReviewRatingService = $productReviewRatingService;
    }
    public function write_review_rating(Request $request):JsonResponse{
        $validator = Validator::make($request->all(), [
            'reviewTitle' => 'required|string',
            'review' => 'required|string',
            'rating' => 'required|numeric',
            'product_slug' => 'required|exists:products,slug',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = auth()->user();
        $product= Product::where('slug',$request->product_slug)->first();

        $upload = new \App\Utility\ApiAizUploadUtility();
        $reuquest->hasFile('aiz_files')?$request->file('aiz_files'):'';
        $result = $upload->multipleFileUpload($request->file('aiz_files'), $user->id);
        $resultVideo = $upload->multipleFileUpload($request->file('aiz_video_files'), $user->id);
        $review = new Review();
        $review->product_id = $product->id;
        $review->user_id = $user->id;
        $review->review_title = $request->reviewTitle;
        $review->photos = $result?$result['files']:'';
        $review->review_videos = $resultVideo?$resultVideo['files']:'';
        $review->rating = $request->rating;
        $review->comment = $request->review;
        $review->status = 0;
        $review->save();
        $per_page = 3;
        $page = 1;
        return $this->productReviewRatingService->get_product_reviews($per_page,$page,$status=1,'',$user->id,);
    }
    public function update_review_rating(Request $request):JsonResponse{
        $validator = Validator::make($request->all(), [
            'reviewTitle' => 'required|string',
            'review' => 'required|string',
            'rating' => 'required|numeric',
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = auth()->user();

        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->user_id !== $user->id || $review->status !== 0) {
            return $this->error(
                'not_authorized',
                'You are not authorized to update this review',
                '',
                403
            );
        }

        $upload = new \App\Utility\ApiAizUploadUtility();
        $result = $upload->multipleFileUpload($request->file('aiz_files'), $user->id);
        $resultVideo = $upload->multipleFileUpload($request->file('aiz_video_files'), $user->id);


        $review->review_title = $request->reviewTitle;
        $review->photos = $result?$result['files']:'';
        $review->review_videos = $resultVideo?$resultVideo['files']:'';
        $review->rating = $request->rating;
        $review->comment = $request->review;
        $review->status = 0;
        $review->save();

        $per_page = 3;
        $page = 1;
        return $this->productReviewRatingService->get_product_reviews($per_page, $page, $status=1, '', $user->id);
    }

    public function delete_review(Request $request): JsonResponse {
        $validator = Validator::make($request->all(), [
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->user_id !== $user->id || $review->status !== 0) {
            return $this->error_response('You are not authorized to delete this review', [], 403);
        }

        try {
            $review->delete();
            return $this->success_response('Review deleted successfully');
        } catch (\Exception $e) {
            return $this->error_response('Failed to delete review', [], 500);
        }
    }


    public function get_user_reviews(Request $request): JsonResponse {
        $user = auth()->user();
        $filter_by_rating = in_array($request->input('filter_by_rating'), ['1', '2', '3', '4', '5']) ? $request->input('filter_by_rating') : '';
        $sort = in_array($request->input('sort'), ['newest', 'lowest_rating', 'highest_rating', 'most_helpful']) ? $request->input('sort') : 'newest';
        $per_page = min((int)$request->input('per_page', 3), 50);
        $page = max((int)$request->input('page', 1), 1);
        return $this->productReviewRatingService->get_product_reviews($per_page,$page,'','',$user->id,$filter_by_rating,$sort);
    }
    public function get_user_review_details(Request $request): JsonResponse {
        $validator = Validator::make($request->all(), [
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();
        if (!$review || $review->user_id !== $user->id ) {
            return $this->error(
                'not_authorized',
                'You are not authorized to update this review',
                '',
                403
            );
        }
        return $this->success(new ProductReviewResource($review));
    }
    public function get_user_rating(Request $request){
        $user = auth()->user();

        $average =Review::where('user_id', $user->id)
            ->where('status', 1)
            ->avg('rating');

        $ratings = Review::where('user_id', $user->id)->where('status', 1)
            ->selectRaw('rating, COUNT(*) as total')
            ->groupBy('rating')
            ->pluck('total', 'rating');

        $totalReviews = $ratings->sum();

        // Ensure all 5 star ratings are represented in percentage
        $percentageBreakdown = collect(range(1, 5))->mapWithKeys(function ($star) use ($ratings, $totalReviews) {
            $count = $ratings->get($star, 0);
            $percentage = $totalReviews > 0 ? round(($count / $totalReviews) * 100, 2) : 0;
            return [$star => $percentage];
        });
        $data['averageRating'] =  round($average ?? 0, 2);
        $data['ratingPercentageBreakdown'] = $percentageBreakdown;

        return $this->success($data);
    }
}
