<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\CombinedOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class ToyyibpayController extends Controller
{
    /**
     * ToyyibPay configuration
     */
    protected function getToyyibpayConfig()
    {
        return [
            'key' => config('toyyibpay.key'),
            'category' => config('toyyibpay.category'),
            'sandbox_url' => 'https://dev.toyyibpay.com/',
            'production_url' => 'https://toyyibpay.com/',
        ];
    }

    /**
     * Get ToyyibPay API URL
     */
    protected function getApiUrl()
    {
        $config = $this->getToyyibpayConfig();
        return env('TOYYIBPAY_SANDBOX', true) ? $config['sandbox_url'] : $config['production_url'];
    }

    /**
     * Initiate ToyyibPay payment
     */
    public function pay(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_type' => 'required|string|in:cart_payment,wallet_payment,seller_package_payment',
                'combined_order_id' => 'required|integer',
                'amount' => 'required|numeric|min:0.01',
                'user_id' => 'required|integer|exists:users,id',
                'package_id' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $payment_type = $request->payment_type;
            $combined_order_id = $request->combined_order_id;
            $amount = $request->amount;
            $user_id = $request->user_id;
            $package_id = $request->package_id ?? 0;

            $user = User::find($user_id);
            if (!$user) {
                return response()->json([
                    'result' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Calculate amount based on payment type
            if ($payment_type == 'cart_payment') {
                $combined_order = CombinedOrder::find($combined_order_id);
                if (!$combined_order) {
                    return response()->json([
                        'result' => false,
                        'message' => 'Order not found'
                    ], 404);
                }
                $amount = floatval($combined_order->grand_total);
            }

            $config = $this->getToyyibpayConfig();
            $order_id = 'TOYYIBPAY_' . $payment_type . '_' . time() . '_' . rand(1000, 9999);

            // Prepare ToyyibPay payment request
            $payment_data = [
                'userSecretKey' => $config['key'],
                'categoryCode' => $config['category'],
                'billName' => 'Payment for Order #' . $combined_order_id,
                'billDescription' => 'Payment via ToyyibPay for ' . $payment_type,
                'billPriceSetting' => 1,
                'billPayorInfo' => 1,
                'billAmount' => number_format($amount * 100, 0, '', ''), // Convert to cents
                'billReturnUrl' => route('toyyibpay-status', [
                    'payment_type' => $payment_type,
                    'combined_order_id' => $combined_order_id,
                    'amount' => $amount,
                    'user_id' => $user_id,
                    'package_id' => $package_id,
                ]),
                'billCallbackUrl' => route('toyyibpay-callback'),
                'billExternalReferenceNo' => $order_id,
                'billTo' => $user->name,
                'billEmail' => $user->email,
                'billPhone' => $user->phone ?? '',
                'billSplitPayment' => 0,
                'billSplitPaymentArgs' => '',
                'billPaymentChannel' => '0',
                'billDisplayMerchant' => 1,
                'billContentEmail' => 'Thank you for your payment!',
            ];

            // Create bill via ToyyibPay API
            $response = Http::post($this->getApiUrl() . 'index.php/api/createBill', $payment_data);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result[0]['BillCode'])) {
                    $bill_code = $result[0]['BillCode'];
                    $payment_url = $this->getApiUrl() . $bill_code;

                    Log::info('ToyyibPay payment initiated', [
                        'user_id' => $user_id,
                        'payment_type' => $payment_type,
                        'amount' => $amount,
                        'bill_code' => $bill_code,
                        'order_id' => $order_id
                    ]);

                    return response()->json([
                        'result' => true,
                        'payment_url' => $payment_url,
                        'bill_code' => $bill_code,
                        'message' => 'Payment initiated successfully'
                    ]);
                } else {
                    throw new \Exception('Invalid response from ToyyibPay API: ' . json_encode($result));
                }
            } else {
                throw new \Exception('ToyyibPay API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('ToyyibPay payment initiation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment initiation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle ToyyibPay payment status check
     */
    public function paymentstatus(Request $request)
    {
        try {
            Log::info('ToyyibPay payment status check', $request->all());

            $status_id = $request->status_id;
            $billcode = $request->billcode;
            $order_id = $request->order_id;

            // Status mapping
            $status_messages = [
                '1' => 'successful',
                '2' => 'pending',
                '3' => 'failed'
            ];

            $status = $status_messages[$status_id] ?? 'unknown';

            if ($status_id == '1') { // Successful payment
                $payment_type = $request->payment_type;
                $user_id = $request->user_id;
                $amount = $request->amount;
                $combined_order_id = $request->combined_order_id;
                $package_id = $request->package_id;

                Log::info('ToyyibPay payment successful', [
                    'payment_type' => $payment_type,
                    'user_id' => $user_id,
                    'amount' => $amount,
                    'billcode' => $billcode,
                    'status_id' => $status_id
                ]);

                // Process payment based on type
                switch ($payment_type) {
                    case 'cart_payment':
                        if (function_exists('checkout_done')) {
                            checkout_done($combined_order_id, json_encode([
                                'status_id' => $status_id,
                                'billcode' => $billcode,
                                'order_id' => $order_id,
                                'payment_method' => 'ToyyibPay'
                            ]));
                        }
                        break;

                    case 'wallet_payment':
                        if (function_exists('wallet_payment_done')) {
                            wallet_payment_done($user_id, $amount, 'ToyyibPay', json_encode([
                                'status_id' => $status_id,
                                'billcode' => $billcode,
                                'order_id' => $order_id
                            ]));
                        }
                        break;

                    case 'seller_package_payment':
                        if (function_exists('seller_purchase_payment_done')) {
                            seller_purchase_payment_done($user_id, $package_id, $amount, 'ToyyibPay', json_encode([
                                'status_id' => $status_id,
                                'billcode' => $billcode,
                                'order_id' => $order_id
                            ]));
                        }
                        break;
                }

                return response()->json([
                    'result' => true,
                    'message' => translate('Payment is successful'),
                    'status' => $status,
                    'billcode' => $billcode,
                    'payment_type' => $payment_type
                ]);

            } else {
                Log::warning('ToyyibPay payment failed or pending', [
                    'status_id' => $status_id,
                    'billcode' => $billcode,
                    'status' => $status,
                    'request_data' => $request->all()
                ]);

                return response()->json([
                    'result' => false,
                    'message' => translate('Payment ' . $status),
                    'status' => $status,
                    'billcode' => $billcode
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('ToyyibPay payment status check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment status check failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle ToyyibPay callback
     */
    public function callback(Request $request)
    {
        try {
            Log::info('ToyyibPay callback received', $request->all());

            $refno = $request->refno;
            $status = $request->status;
            $reason = $request->reason;
            $billcode = $request->billcode;
            $order_id = $request->order_id;
            $amount = $request->amount;

            // Verify callback authenticity if needed
            // You might want to implement signature verification here

            if ($status == '1') { // Successful payment
                Log::info('ToyyibPay callback - payment successful', [
                    'refno' => $refno,
                    'billcode' => $billcode,
                    'amount' => $amount,
                    'order_id' => $order_id
                ]);

                return response()->json([
                    'result' => true,
                    'message' => 'Payment callback processed successfully'
                ]);

            } else {
                Log::warning('ToyyibPay callback - payment failed', [
                    'refno' => $refno,
                    'status' => $status,
                    'reason' => $reason,
                    'billcode' => $billcode
                ]);

                return response()->json([
                    'result' => false,
                    'message' => 'Payment failed: ' . $reason
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('ToyyibPay callback processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Callback processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bill transactions from ToyyibPay
     */
    public function getBillTransactions(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'billcode' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $config = $this->getToyyibpayConfig();
            
            $response = Http::post($this->getApiUrl() . 'index.php/api/getBillTransactions', [
                'billCode' => $request->billcode,
                'userSecretKey' => $config['key']
            ]);

            if ($response->successful()) {
                return response()->json([
                    'result' => true,
                    'data' => $response->json()
                ]);
            } else {
                throw new \Exception('ToyyibPay API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('ToyyibPay get bill transactions failed', [
                'error' => $e->getMessage(),
                'billcode' => $request->billcode ?? 'N/A'
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Failed to get bill transactions: ' . $e->getMessage()
            ], 500);
        }
    }
} 