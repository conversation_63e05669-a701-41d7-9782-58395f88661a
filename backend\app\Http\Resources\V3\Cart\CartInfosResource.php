<?php

namespace App\Http\Resources\V3\Cart;

use App\Http\Resources\V3\CartProductsResource;
use Illuminate\Http\Resources\Json\JsonResource;

class CartInfosResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // Format cart items
        $items = $this->when($this->relationLoaded('carts'), function () {
            return CartItemResource::collection($this->carts);
        }, []);
        
        // Calculate all totals dynamically (with fallback to model attributes)
        $subtotal = $this->calculateSubtotal();
        $discount = $this->discount ?? 0;
        $taxTotal = $this->tax_total();
        $shippingTotal = $this->shipping_total ?? 0;
        $total = $subtotal + $taxTotal + $shippingTotal - $discount;

        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'items' => $items,
            'summary' => [
                'item_count' => $this->item_count(),
                'total_quantity' => $this->total_quantity(),
                'subtotal' => round($subtotal, 2),
                'discount' => round($discount, 2),
                'coupon_code' => $this->coupon_code,
                'coupon_applied' => (bool)$this->coupon_applied,
                'shipping_total' => round($shippingTotal, 2),
                'tax_total' => round($taxTotal, 2),
                'total' => round($total, 2),
                'currency' => $this->currency ?? 'USD'
            ],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Calculate shipping total with fallback to model attribute
     * 
     * @param mixed $data CartInfo instance
     * @return float|int
     */
    protected function shipping_total($data): float|int
    {
        if (isset($data->shipping_total)) {
            return $data->shipping_total;
        }
        
        $shippingTotal = 0;
        if ($data->relationLoaded('carts') && $data->carts->isNotEmpty()) {
            $shippingTotal = $data->carts->sum('shipping_cost');
        }
        
        return $shippingTotal;
    }
}
