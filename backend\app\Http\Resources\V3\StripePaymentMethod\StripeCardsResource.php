<?php

namespace App\Http\Resources\V3\StripePaymentMethod;

use App\Http\Resources\V3\StripePaymentMethod\StripeCardResource;
use Illuminate\Http\Resources\Json\JsonResource;

class StripeCardsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($item) {
            return new StripeCardResource($item);

        });
    }
}
