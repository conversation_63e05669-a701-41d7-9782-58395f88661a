<?php

namespace App\Http\Controllers\Api\V3\Stripe;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\StripePaymentMethod\PaymentMethodsResource;
use App\Http\Resources\V3\StripePaymentMethod\StripeCardResource;
use App\Http\Resources\V3\StripePaymentMethod\StripeCardsResource;
use App\Services\StripeService;
use App\Models\StripeCard;
use App\Models\StripeCustomer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;

class ApiPaymentMethodController extends ApiResponse
{
    protected StripeService $stripeService;
    
    public function __construct(StripeService $stripeService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
    }

    /**
     * Store a new card
     */
    public function store_card(Request $request)
    {
        $messages = [
            'payment_method.required' => translate('Payment method is required'),
            'cardholder_name.required' => translate('Cardholder name is required'),
            'cardholder_name.string' => translate('Cardholder name must be a string'),
            'cardholder_name.max' => translate('Cardholder name cannot exceed 255 characters'),
            'is_default.required' => translate('Default status is required'),
            'is_default.boolean' => translate('Default status must be true or false'),
        ];

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string',
            'cardholder_name' => 'required|string|max:255',
            'is_default' => 'required|boolean',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error', 
                'Please provide valid credentials', 
                $validator->errors()->messages(), 
                400
            );
        }

        DB::beginTransaction();
        try {
            $user = auth()->user();
            
            // Get or create Stripe customer
            $stripeCustomer = StripeCustomer::getOrCreateForUser($user, $this->stripeService);
            
            // Retrieve payment method from Stripe
            $paymentMethod = $this->stripeService->retrieveCard($request->payment_method);
            
            // Validate card
            $this->stripeService->validateCard($request->payment_method);

            // Check if this card already exists for the user
            $existingCard = $user->stripeCards()
                ->where('last4', $paymentMethod->card->last4)
                ->where('exp_month', $paymentMethod->card->exp_month)
                ->where('exp_year', $paymentMethod->card->exp_year)
                ->where('brand', $paymentMethod->card->brand)
                ->first();

            if ($existingCard) {
                DB::rollBack();
                return $this->error(
                    400,
                    'Card already exists',
                    'A card with the same details already exists for this user',
                    400
                );
            }

            // Add the card to Stripe customer
            $attachedPaymentMethod = $this->stripeService->addCard(
                $stripeCustomer->stripe_customer_id, 
                $request->payment_method
            );

            // Handle default card logic
            if ($request->is_default) {
                // Set all existing cards to non-default
                $user->stripeCards()->update(['isDefault' => false]);
                
                // Set this card as default in Stripe
                $this->stripeService->setDefaultPaymentMethod(
                    $stripeCustomer->stripe_customer_id, 
                    $request->payment_method
                );
            }

            // Prepare billing details
            $billingDetails = $paymentMethod->billing_details ? (array) $paymentMethod->billing_details : [];
            if ($request->cardholder_name) {
                $billingDetails['name'] = $request->cardholder_name;
            }

            // Update payment method with cardholder name if provided
            if ($request->cardholder_name && $request->cardholder_name !== ($billingDetails['name'] ?? '')) {
                $this->stripeService->updateCard($request->payment_method, [
                    'billing_details' => array_merge($billingDetails, [
                        'name' => $request->cardholder_name
                    ])
                ]);
                $billingDetails['name'] = $request->cardholder_name;
            }

            // Save the card details in the database
            $card = $user->stripeCards()->create([
                'stripe_card_id' => $attachedPaymentMethod->id,
                'type' => $attachedPaymentMethod->type,
                'brand' => $attachedPaymentMethod->card->brand,
                'last4' => $attachedPaymentMethod->card->last4,
                'exp_month' => $attachedPaymentMethod->card->exp_month,
                'exp_year' => $attachedPaymentMethod->card->exp_year,
                'isDefault' => $request->is_default,
                'billing_details' => $billingDetails,
            ]);

            DB::commit();

            return $this->success(
                new StripeCardResource($card),
                'Card added successfully',
                201
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to store card', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                400,
                'Card not added',
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Get user's saved cards
     */
    public function get_user_cards(Request $request)
    {
        try {
            $user = auth()->user();
            $stripeCustomer = $user->stripeCustomer;

            if (!$stripeCustomer) {
                return $this->success(new StripeCardsResource(collect([])), 'No cards found');
            }

            // Get cards from database with pagination
            $perPage = min((int)$request->input('per_page', 10), 50);
            $cards = $user->stripeCards()
                          ->orderBy('isDefault', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);

            return $this->success(
                new StripeCardsResource($cards),
                'Cards retrieved successfully'
            );

        } catch (\Exception $e) {
            Log::error('Failed to get user cards', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return $this->error(
                500,
                'Failed to retrieve cards',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get available payment methods
     */
    public function get_user_available_cards(Request $request)
    {
        $response = [
            [
                "id" => "stripe",
                "name" => "Stripe",
                "description" => "Pay with Stripe",
                "icon" => 'https://img.icons8.com/color/48/000000/stripe.png',
                "fees" => 0,
                "isDefault" => true
            ]
        ];
        
        return $this->success($response, 'Available payment methods retrieved');
    }

    /**
     * Remove a card
     */
    public function remove_card(Request $request, $stripe_card_id)
    {
        DB::beginTransaction();
        try {
            $user = auth()->user();
            $card = $user->stripeCards()->where('stripe_card_id', $stripe_card_id)->first();

            if (!$card) {
                return $this->error(
                    404,
                    'Card not found',
                    'The specified card was not found',
                    404
                );
            }

            // Check if this is the only card and it's default
            $totalCards = $user->stripeCards()->count();
            $wasDefault = $card->isDefault;

            // Remove from Stripe
            $this->stripeService->removeCard($stripe_card_id);

            // Remove from database
            $card->delete();

            // If this was the default card and there are other cards, set another as default
            if ($wasDefault && $totalCards > 1) {
                $nextCard = $user->stripeCards()->first();
                if ($nextCard) {
                    $nextCard->setAsDefault();
                    
                    // Update in Stripe as well
                    $stripeCustomer = $user->stripeCustomer;
                    if ($stripeCustomer) {
                        $this->stripeService->setDefaultPaymentMethod(
                            $stripeCustomer->stripe_customer_id,
                            $nextCard->stripe_card_id
                        );
                    }
                }
            }

            DB::commit();

            return $this->success(
                [],
                'Payment method deleted successfully',
                200
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to remove card', [
                'user_id' => auth()->id(),
                'card_id' => $stripe_card_id,
                'error' => $e->getMessage()
            ]);

            return $this->error(
                400,
                'Card not removed',
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Set a card as default
     */
    public function set_default_card(Request $request, $stripe_card_id)
    {
        DB::beginTransaction();
        try {
            $user = auth()->user();
            $card = $user->stripeCards()->where('stripe_card_id', $stripe_card_id)->first();

            if (!$card) {
                return $this->error(
                    404,
                    'Card not found',
                    'The specified card was not found',
                    404
                );
            }

            // If already default, no need to update
            if ($card->isDefault) {
                return $this->success(
                    new StripeCardResource($card),
                    'Card is already set as default',
                    200
                );
            }

            $stripeCustomer = $user->stripeCustomer;
            if (!$stripeCustomer) {
                DB::rollBack();
                return $this->error(
                    400,
                    'Stripe customer not found',
                    'User does not have a Stripe customer account',
                    400
                );
            }

            // Set as default in Stripe
            $this->stripeService->setDefaultPaymentMethod(
                $stripeCustomer->stripe_customer_id, 
                $stripe_card_id
            );

            // Update in database
            $card->setAsDefault();

            DB::commit();

            return $this->success(
                new StripeCardResource($card->fresh()),
                'Payment method set as default successfully',
                200
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to set default card', [
                'user_id' => auth()->id(),
                'card_id' => $stripe_card_id,
                'error' => $e->getMessage()
            ]);

            return $this->error(
                400,
                'Failed to set default card',
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Update card details (billing information)
     */
    public function update_card(Request $request, $stripe_card_id)
    {
        $messages = [
            'cardholder_name.string' => translate('Cardholder name must be a string'),
            'cardholder_name.max' => translate('Cardholder name cannot exceed 255 characters'),
            'billing_address.array' => translate('Billing address must be an array'),
        ];

        $validator = Validator::make($request->all(), [
            'cardholder_name' => 'sometimes|string|max:255',
            'billing_address' => 'sometimes|array',
            'billing_address.line1' => 'sometimes|string|max:255',
            'billing_address.line2' => 'sometimes|string|max:255',
            'billing_address.city' => 'sometimes|string|max:100',
            'billing_address.state' => 'sometimes|string|max:100',
            'billing_address.postal_code' => 'sometimes|string|max:20',
            'billing_address.country' => 'sometimes|string|size:2',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid data',
                $validator->errors()->messages(),
                400
            );
        }

        DB::beginTransaction();
        try {
            $user = auth()->user();
            $card = $user->stripeCards()->where('stripe_card_id', $stripe_card_id)->first();

            if (!$card) {
                return $this->error(
                    404,
                    'Card not found',
                    'The specified card was not found',
                    404
                );
            }

            $updateData = [];
            $billingDetails = $card->billing_details ?? [];

            // Update cardholder name
            if ($request->has('cardholder_name')) {
                $billingDetails['name'] = $request->cardholder_name;
            }

            // Update billing address
            if ($request->has('billing_address')) {
                $billingAddress = $request->billing_address;
                $billingDetails['address'] = array_filter([
                    'line1' => $billingAddress['line1'] ?? null,
                    'line2' => $billingAddress['line2'] ?? null,
                    'city' => $billingAddress['city'] ?? null,
                    'state' => $billingAddress['state'] ?? null,
                    'postal_code' => $billingAddress['postal_code'] ?? null,
                    'country' => $billingAddress['country'] ?? null,
                ]);
            }

            // Update in Stripe if billing details changed
            if (!empty($billingDetails)) {
                $updateData['billing_details'] = $billingDetails;
                $this->stripeService->updateCard($stripe_card_id, $updateData);
            }

            // Update in database
            $card->update([
                'billing_details' => $billingDetails
            ]);

            DB::commit();

            return $this->success(
                new StripeCardResource($card->fresh()),
                'Card updated successfully',
                200
            );

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update card', [
                'user_id' => auth()->id(),
                'card_id' => $stripe_card_id,
                'error' => $e->getMessage()
            ]);

            return $this->error(
                400,
                'Failed to update card',
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Get specific card details
     */
    public function get_card(Request $request, $stripe_card_id)
    {
        try {
            $user = auth()->user();
            $card = $user->stripeCards()->where('stripe_card_id', $stripe_card_id)->first();

            if (!$card) {
                return $this->error(
                    404,
                    'Card not found',
                    'The specified card was not found',
                    404
                );
            }

            return $this->success(
                new StripeCardResource($card),
                'Card details retrieved successfully'
            );

        } catch (\Exception $e) {
            Log::error('Failed to get card details', [
                'user_id' => auth()->id(),
                'card_id' => $stripe_card_id,
                'error' => $e->getMessage()
            ]);

            return $this->error(
                500,
                'Failed to retrieve card',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Process payment with stored card
     */
    public function process_payment(Request $request, $stripe_card_id)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.50|max:999999.99',
            'currency' => 'sometimes|string|size:3',
            'description' => 'sometimes|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid payment data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $user = auth()->user();
            $card = $user->stripeCards()->where('stripe_card_id', $stripe_card_id)->first();

            if (!$card) {
                return $this->error(
                    404,
                    'Card not found',
                    'The specified card was not found',
                    404
                );
            }

            $stripeCustomer = $user->stripeCustomer;
            if (!$stripeCustomer) {
                return $this->error(
                    400,
                    'Customer not found',
                    'User does not have a Stripe customer account',
                    400
                );
            }

            // Validate card before processing
            $this->stripeService->validateCard($stripe_card_id);

            $successUrl = url('/api/v3/stripe/payment/success');
            $cancelUrl = url('/api/v3/stripe/payment/cancel');

            $paymentIntent = $this->stripeService->payWithCard(
                $stripeCustomer->stripe_customer_id,
                $stripe_card_id,
                $request->amount,
                $successUrl,
                $cancelUrl
            );

            return $this->success([
                'payment_intent_id' => $paymentIntent->id,
                'client_secret' => $paymentIntent->client_secret,
                'status' => $paymentIntent->status,
                'amount' => $paymentIntent->amount / 100,
                'currency' => $paymentIntent->currency,
            ], 'Payment processed successfully');

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'user_id' => auth()->id(),
                'card_id' => $stripe_card_id,
                'amount' => $request->amount,
                'error' => $e->getMessage()
            ]);

            return $this->error(
                400,
                'Payment failed',
                $e->getMessage(),
                400
            );
        }
    }

    /**
     * Create a setup intent for saving payment methods
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function create_setup_intent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'usage' => 'sometimes|string|in:on_session,off_session',
            'payment_method_types' => 'sometimes|array',
            'payment_method_types.*' => 'string|in:card,us_bank_account,sepa_debit',
            'customer_id' => 'sometimes|string|nullable',
            'metadata' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid setup intent data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $user = auth()->user();
            
            // Get or create Stripe customer
            $stripeCustomer = StripeCustomer::getOrCreateForUser($user, $this->stripeService);
            
            // Set default values
            $usage = $request->input('usage', 'off_session');
            $paymentMethodTypes = $request->input('payment_method_types', ['card']);
            $metadata = $request->input('metadata', []);
            
            // Add default metadata
            $metadata = array_merge([
                'user_id' => $user->id,
                'user_email' => $user->email,
                'app_name' => config('app.name', 'Buzfi'),
                'created_at' => now()->toDateTimeString(),
            ], $metadata);

            // Create setup intent with Stripe
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));
            
            $setupIntentData = [
                'customer' => $stripeCustomer->stripe_customer_id,
                'usage' => $usage,
                'payment_method_types' => $paymentMethodTypes,
                'metadata' => $metadata,
            ];

            $setupIntent = \Stripe\SetupIntent::create($setupIntentData);

            Log::info('Setup intent created successfully', [
                'user_id' => $user->id,
                'setup_intent_id' => $setupIntent->id,
                'customer_id' => $stripeCustomer->stripe_customer_id,
                'usage' => $usage,
            ]);

            return $this->success([
                'setup_intent_id' => $setupIntent->id,
                'client_secret' => $setupIntent->client_secret,
                'status' => $setupIntent->status,
                'customer_id' => $setupIntent->customer,
                'usage' => $setupIntent->usage,
                'payment_method_types' => $setupIntent->payment_method_types,
                'created' => $setupIntent->created,
            ], 'Setup intent created successfully');

        } catch (\Stripe\Exception\InvalidRequestException $e) {
            Log::error('Stripe invalid request error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode(),
            ]);

            return $this->error(
                400,
                'Invalid setup intent request',
                'The setup intent request is invalid: ' . $e->getMessage(),
                400
            );

        } catch (\Stripe\Exception\AuthenticationException $e) {
            Log::error('Stripe authentication error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return $this->error(
                401,
                'Authentication failed',
                'Stripe authentication failed. Please contact support.',
                401
            );

        } catch (\Stripe\Exception\ApiConnectionException $e) {
            Log::error('Stripe API connection error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return $this->error(
                503,
                'Service unavailable',
                'Payment service is temporarily unavailable. Please try again later.',
                503
            );

        } catch (\Stripe\Exception\CardException $e) {
            Log::error('Stripe card error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode(),
            ]);

            return $this->error(
                400,
                'Card error',
                $e->getMessage(),
                400
            );

        } catch (\Stripe\Exception\RateLimitException $e) {
            Log::error('Stripe rate limit error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
            ]);

            return $this->error(
                429,
                'Rate limit exceeded',
                'Too many requests to payment service. Please try again later.',
                429
            );

        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe API error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'stripe_error_code' => $e->getStripeCode(),
            ]);

            return $this->error(
                500,
                'Payment service error',
                'Payment service error: ' . $e->getMessage(),
                500
            );

        } catch (\Exception $e) {
            Log::error('General error during setup intent creation', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->error(
                500,
                'Setup intent creation failed',
                'Unable to create setup intent: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Handle payment success return URL
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function payment_success(Request $request): JsonResponse
    {
        try {
            $paymentIntentId = $request->query('payment_intent');
            $paymentIntentClientSecret = $request->query('payment_intent_client_secret');
            $redirectStatus = $request->query('redirect_status');

            // Log the return for debugging
            Log::info('Stripe payment success callback', [
                'payment_intent_id' => $paymentIntentId,
                'redirect_status' => $redirectStatus,
                'query_params' => $request->query()
            ]);

            $response = [
                'success' => true,
                'message' => 'Payment completed successfully',
                'data' => [
                    'payment_intent_id' => $paymentIntentId,
                    'redirect_status' => $redirectStatus,
                    'status' => 'success'
                ]
            ];

            // If this is an API call, return JSON
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->success($response['data'], $response['message']);
            }

            // Otherwise, return a simple HTML page for browser redirects
            return response()->view('stripe.payment_success', $response);

        } catch (\Exception $e) {
            Log::error('Stripe payment success callback error', [
                'error' => $e->getMessage(),
                'query_params' => $request->query()
            ]);

            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error(
                    500,
                    'Payment success handler error',
                    $e->getMessage(),
                    500
                );
            }

            return response()->view('stripe.payment_error', [
                'error' => 'Unable to process payment success callback'
            ]);
        }
    }

    /**
     * Handle payment cancel return URL
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function payment_cancel(Request $request): JsonResponse
    {
        try {
            $paymentIntentId = $request->query('payment_intent');
            $paymentIntentClientSecret = $request->query('payment_intent_client_secret');

            // Log the return for debugging
            Log::info('Stripe payment cancel callback', [
                'payment_intent_id' => $paymentIntentId,
                'query_params' => $request->query()
            ]);

            $response = [
                'success' => false,
                'message' => 'Payment was cancelled by user',
                'data' => [
                    'payment_intent_id' => $paymentIntentId,
                    'status' => 'cancelled'
                ]
            ];

            // If this is an API call, return JSON
            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error(
                    400,
                    'Payment cancelled',
                    'Payment was cancelled by user',
                    400
                );
            }

            // Otherwise, return a simple HTML page for browser redirects
            return response()->view('stripe.payment_cancel', $response);

        } catch (\Exception $e) {
            Log::error('Stripe payment cancel callback error', [
                'error' => $e->getMessage(),
                'query_params' => $request->query()
            ]);

            if ($request->expectsJson() || $request->is('api/*')) {
                return $this->error(
                    500,
                    'Payment cancel handler error',
                    $e->getMessage(),
                    500
                );
            }

            return response()->view('stripe.payment_error', [
                'error' => 'Unable to process payment cancel callback'
            ]);
        }
    }
}
