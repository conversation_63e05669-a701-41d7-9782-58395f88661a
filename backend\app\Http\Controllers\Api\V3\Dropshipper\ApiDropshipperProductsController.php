<?php

namespace App\Http\Controllers\Api\V3\Dropshipper;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\OrderDetail;
use App\Models\Category;
use App\Models\ProductStock;
use App\Models\User;
use App\Models\Order;
use App\Http\Controllers\Api\V3\ApiResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;
use App\Helpers\ProductPriceHelper;
use Illuminate\Support\Facades\Auth;

class ApiDropshipperProductsController extends ApiResponse
{
    /**
     * Get list of products with filtering and pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // Parse request parameters
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $search = $request->input('search');
            $categoryId = $request->input('category');
            $status = $request->input('status');
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $priceMin = $request->input('price_min');
            $priceMax = $request->input('price_max');
            $inStock = $request->input('in_stock');
            $tag = $request->input('tag');

            // Start building the query
            $query = Product::query();

            // Apply filters if provided
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhere('tags', 'like', "%{$search}%");
                });
            }

            if ($categoryId) {
                // Get all child categories
                $childCategories = $this->getAllChildCategories($categoryId);
                $childCategories[] = $categoryId;

                $query->whereIn('category_id', $childCategories);
            }

            if ($status) {
                $query->where('status', $status);
            } else {
                $query->where('status', 1); // Default to published/active products
            }

            if ($priceMin !== null) {
                $query->where('unit_price', '>=', $priceMin);
            }

            if ($priceMax !== null) {
                $query->where('unit_price', '<=', $priceMax);
            }

            if ($inStock !== null) {
                if ($inStock) {
                    $query->where('current_stock', '>', 0);
                } else {
                    $query->where('current_stock', '<=', 0);
                }
            }

            if ($tag) {
                $query->where('tags', 'like', "%{$tag}%");
            }

            // Apply sorting
            if ($sortBy && $sortOrder) {
                $validSortColumns = [
                    'name', 'unit_price', 'current_stock', 'num_of_sale', 'rating', 'created_at'
                ];

                if (in_array($sortBy, $validSortColumns)) {
                    $query->orderBy($sortBy, $sortOrder);
                } else {
                    $query->orderBy('created_at', 'desc');
                }
            }

            // Execute paginated query
            $products = $query->paginate($limit, ['*'], 'page', $page);

            // Transform products
            $transformedProducts = $products->map(function($product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'price' => $prices['displayPrice'],
                    'regularPrice' => $prices['regularPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'discountType' => $product->discount_type,
                    'profitMargin' => (float)$profitMargin,
                    'rating' => (float)$avgRating,
                    'totalReviews' => (int)$product->num_of_reviews,
                    'stock' => (int)$product->current_stock,
                    'discountPrice' => $product->discount > 0 ? (float)($product->unit_price - $product->discount) : null,
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'rating' => (float)$product->rating,
                    'totalReviews' => (int)$product->num_of_reviews,
                    'stock' => (int)$product->current_stock,
                    'category' => [
                        'id' => $product->category_id,
                        'name' => $product->category ? $product->category->name : null,
                    ],
                    'brand' => [
                        'id' => $product->brand_id,
                        'name' => $product->brand ? $product->brand->name : null,
                    ],
                    'totalSales' => (int)$product->num_of_sale,
                    'createdAt' => $product->created_at->toIso8601String(),
                    'updatedAt' => $product->updated_at->toIso8601String(),
                ];
            });

            // Prepare response
            $response = [
                'products' => $transformedProducts,
                'pagination' => [
                    'total' => $products->total(),
                    'count' => $products->count(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'total_pages' => $products->lastPage(),
                ],
                'filters' => [
                    'search' => $search,
                    'category' => $categoryId,
                    'status' => $status,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                    'price_min' => $priceMin,
                    'price_max' => $priceMax,
                    'in_stock' => $inStock,
                    'tag' => $tag,
                ],
            ];

            return $this->success($response);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve products: ' . $e->getMessage());
            return $this->error('FAILED_TO_RETRIEVE_PRODUCTS', 'Failed to retrieve products: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * Get product details by ID
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            $product = Product::with(['category', 'brand', 'stocks'])->find($id);

            if (!$product) {
                return ApiResponse::error('Product not found', 404);
            }

            // Get product images
            $photos = json_decode($product->photos) ?? [];
            $images = [];

            foreach ($photos as $photo) {
                $images[] = uploaded_asset($photo);
            }

            if ($product->thumbnail_img) {
                array_unshift($images, uploaded_asset($product->thumbnail_img));
            }

            // Calculate profit margin
            $costPrice = (float)$product->purchase_price;
            $sellingPrice = (float)$product->unit_price;
            $profitMargin = $sellingPrice > 0 ? (($sellingPrice - $costPrice) / $sellingPrice) * 100 : 0;

            // Calculate average rating
            $avgRating = $product->rating;

            // Get related products
            $relatedProducts = Product::where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->limit(5)
                ->get()
                ->map(function($relatedProduct) {
                    $prices = ProductPriceHelper::getProductPrices($relatedProduct);
                    return [
                        'id' => $relatedProduct->id,
                        'name' => $relatedProduct->name,
                        'slug' => $relatedProduct->slug,
                        'thumbnail' => $relatedProduct->thumbnail_img ? uploaded_asset($relatedProduct->thumbnail_img) : null,
                        'price' => (float)$prices['displayPrice'],
                        'regularPrice' => (float)$prices['regularPrice'],
                        'has_discount' => $relatedProduct->discount > 0,
                        'discount' => $relatedProduct->discount,
                        'discount_type' => $relatedProduct->discount_type,
                        'discount_percentage' => $relatedProduct->discount_type === 'percent' ? $relatedProduct->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                        'discountPercent' => $relatedProduct->discount_type === 'percent' ? (float)$relatedProduct->discount : null,
                        'discountPrice' => $relatedProduct->discount > 0 ? (float)($relatedProduct->unit_price - $relatedProduct->discount) : null,
                        'rating' => (float)$relatedProduct->rating,
                    ];
                });

            // Prepare response
            $prices = ProductPriceHelper::getProductPrices($product);
            $productDetails = [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'description' => $product->description,
                'shortDescription' => $product->short_description,
                'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                'images' => $images,
                'price' => $prices['displayPrice'],
                'regularPrice' => $prices['regularPrice'],
                'b2bPrice' => $product->b2b_price,
                'costPrice' => (float)$product->purchase_price,
                'has_discount' => $product->discount > 0,
                'discount' => $product->discount,
                'discount_type' => $product->discount_type,
                'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                'discountType' => $product->discount_type,
                'profitMargin' => (float)$profitMargin,
                'rating' => (float)$avgRating,
                'totalReviews' => (int)$product->num_of_reviews,
                'stock' => (int)$product->current_stock,
                'soldCount' => (int)$product->num_of_sale,
                'sku' => $product->sku,
                'barcode' => $product->barcode,
                'weight' => (float)$product->weight,
                'dimensions' => [
                    'length' => (float)($product->length ?? 0),
                    'width' => (float)($product->width ?? 0),
                    'height' => (float)($product->height ?? 0),
                ],
                'category' => [
                    'id' => $product->category_id,
                    'name' => $product->category ? $product->category->name : null,
                ],
                'brand' => [
                    'id' => $product->brand_id,
                    'name' => $product->brand ? $product->brand->name : null,
                ],
                'tax' => $product->taxes->map(function($tax) {
                    return [
                        'id' => $tax->id,
                        'tax_id' => $tax->tax_id,
                        'tax_name' => optional(\App\Models\Tax::find($tax->tax_id))->name,
                        'tax' => $tax->tax,
                        'tax_type' => $tax->tax_type
                    ];
                }),
                'variations' => $product->stocks->map(function($stock) {
                    $variant = json_decode($stock->variant, true) ?? [];
                    $variantString = implode(' / ', array_values($variant));

                    return [
                        'id' => $stock->id,
                        'name' => $variantString,
                        'sku' => $stock->sku,
                        'price' => (float)$stock->price,
                        'stock' => (int)$stock->qty,
                        'image' => $stock->image ? uploaded_asset($stock->image) : null,
                    ];
                }),
                'tags' => explode(',', $product->tags),
                'metaTitle' => $product->meta_title,
                'metaDescription' => $product->meta_description,
                'metaKeywords' => $product->meta_keywords,
                'published' => (bool)$product->published,
                'featured' => (bool)$product->featured,
                'createdAt' => $product->created_at->toIso8601String(),
                'updatedAt' => $product->updated_at->toIso8601String(),
                'relatedProducts' => $relatedProducts,
            ];

            return $this->success($productDetails);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product details: ' . $e->getMessage());
            return ApiResponse::error('Failed to retrieve product details: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get product statistics
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductStats(Request $request)
    {
        try {
            $user = auth()->user();

            // Get counts
            $totalProducts = Product::count();
            $inStockProducts = Product::where('current_stock', '>', 0)->count();
            $outOfStockProducts = Product::where('current_stock', '<=', 0)->count();

            // Get user's ordered products
            $orderProductIds = OrderDetail::whereIn('order_id', Order::where('user_id', $user->id)->pluck('id'))
                ->distinct('product_id')
                ->pluck('product_id');

            $orderedProductsCount = $orderProductIds->count();

            // Get top categories
            $topCategories = OrderDetail::join('products', 'order_details.product_id', '=', 'products.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->select('categories.id', 'categories.name', DB::raw('COUNT(*) as count'))
                ->groupBy('categories.id', 'categories.name')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
                ->map(function($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'count' => (int)$category->count,
                    ];
                });

            // Get top brands
            $topBrands = OrderDetail::join('products', 'order_details.product_id', '=', 'products.id')
                ->join('brands', 'products.brand_id', '=', 'brands.id')
                ->select('brands.id', 'brands.name', DB::raw('COUNT(*) as count'))
                ->groupBy('brands.id', 'brands.name')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
                ->map(function($brand) {
                    return [
                        'id' => $brand->id,
                        'name' => $brand->name,
                        'count' => (int)$brand->count,
                    ];
                });

            // Prepare response
            $stats = [
                'totalProducts' => (int)$totalProducts,
                'inStockProducts' => (int)$inStockProducts,
                'outOfStockProducts' => (int)$outOfStockProducts,
                'orderedProducts' => (int)$orderedProductsCount,
                'topCategories' => $topCategories,
                'topBrands' => $topBrands,
            ];

            return $this->success($stats);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve product statistics: ' . $e->getMessage());
            return ApiResponse::error('Failed to retrieve product statistics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get trending products
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrendingProducts(Request $request)
    {
        try {
            $type = $request->input('type', 'most_ordered');
            $limit = $request->input('limit', 10);
            $categoryId = $request->input('category');

            $query = Product::query();

            // Apply category filter if provided
            if ($categoryId) {
                // Get all child categories
                $childCategories = $this->getAllChildCategories($categoryId);
                $childCategories[] = $categoryId;

                $query->whereIn('category_id', $childCategories);
            }

            // Apply trending type filter
            switch ($type) {
                case 'most_ordered':
                    $query->orderBy('num_of_sale', 'desc');
                    break;
                case 'most_viewed':
                    $query->orderBy('views', 'desc');
                    break;
                case 'top_rated':
                    $query->orderBy('rating', 'desc');
                    break;
                case 'most_profitable':
                    $query->selectRaw('*, (unit_price - purchase_price) as profit')
                        ->orderByRaw('(unit_price - purchase_price) desc');
                    break;
                default:
                    $query->orderBy('num_of_sale', 'desc');
            }

            // Get trending products
            $trendingProducts = $query->limit($limit)->get();

            // Transform products
            $transformedProducts = $trendingProducts->map(function($product) use ($type) {
                $prices = ProductPriceHelper::getProductPrices($product);
                $baseData = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'price' => (float)$prices['displayPrice'],
                    'regularPrice' => (float)$prices['regularPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'discountPrice' => $product->discount > 0 ? (float)($product->unit_price - $product->discount) : null,
                    'rating' => (float)$product->rating,
                    'stock' => (int)$product->current_stock,
                    'category' => [
                        'id' => $product->category_id,
                        'name' => $product->category ? $product->category->name : null,
                    ],
                ];

                // Add trending metric based on type
                switch ($type) {
                    case 'most_ordered':
                        $baseData['soldCount'] = (int)$product->num_of_sale;
                        break;
                    case 'most_viewed':
                        $baseData['viewCount'] = (int)$product->views;
                        break;
                    case 'top_rated':
                        $baseData['reviewCount'] = (int)$product->num_of_reviews;
                        break;
                    case 'most_profitable':
                        $baseData['profit'] = (float)($product->unit_price - $product->purchase_price);
                        $baseData['profitMargin'] = $product->unit_price > 0
                            ? (float)(($product->unit_price - $product->purchase_price) / $product->unit_price * 100)
                            : 0;
                        break;
                }

                return $baseData;
            });

            return $this->success($transformedProducts);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve trending products: ' . $e->getMessage());
            return ApiResponse::error('Failed to retrieve trending products: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get products for bulk ordering
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBulkOrderProducts(Request $request)
    {
        try {
            // Get authenticated user
            $user = auth('sanctum')->user();

            // Parse request parameters
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $search = $request->input('search');
            $categoryId = $request->input('category');
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $priceMin = $request->input('price_min');
            $priceMax = $request->input('price_max');
            $inStock = $request->input('in_stock', true);

            // Start building the query
            $query = Product::where('b2b_price', '>', 0);

            // Apply filters if provided
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhere('tags', 'like', "%{$search}%");
                });
            }

            if ($categoryId) {
                // Get all child categories
                $childCategories = $this->getAllChildCategories($categoryId);
                $childCategories[] = $categoryId;

                $query->whereIn('category_id', $childCategories);
            }

            if ($priceMin !== null) {
                $query->where('unit_price', '>=', $priceMin);
            }

            if ($priceMax !== null) {
                $query->where('unit_price', '<=', $priceMax);
            }

            if ($inStock !== null) {
                if ($inStock) {
                    $query->where('current_stock', '>', 0);
                } else {
                    $query->where('current_stock', '<=', 0);
                }
            }

            // Apply sorting
            if ($sortBy && $sortOrder) {
                $validSortColumns = [
                    'name', 'unit_price', 'current_stock', 'num_of_sale', 'rating', 'created_at'
                ];

                if (in_array($sortBy, $validSortColumns)) {
                    $query->orderBy($sortBy, $sortOrder);
                } else {
                    $query->orderBy('created_at', 'desc');
                }
            }

            // Execute paginated query
            $products = $query->paginate($limit, ['*'], 'page', $page);

            // Transform products
            $transformedProducts = $products->map(function($product) use ($user) {
                $prices = ProductPriceHelper::getProductPrices($product);

                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'price' => $prices['displayPrice'],
                    'user_type' => $user->user_type, // Using user_type from authenticated user
                    'regularPrice' => $prices['regularPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'rating' => (float)$product->rating,
                    'stock' => (int)$product->current_stock,
                    'minBulkQuantity' => (int)$product->min_bulk_quantity,
                    'bulkDiscountPercentage' => (float)$product->bulk_discount_percentage,
                    'category' => [
                        'id' => $product->category_id,
                        'name' => optional($product->category)->name
                    ],
                    'brand' => [
                        'id' => $product->brand_id,
                        'name' => optional($product->brand)->name
                    ]
                ];
            });

            return $this->success([
                'products' => $transformedProducts,
                'pagination' => [
                    'total' => $products->total(),
                    'count' => $products->count(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'total_pages' => $products->lastPage()
                ],
                'filters' => [
                    'search' => $search,
                    'category' => $categoryId,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                    'price_min' => $priceMin,
                    'price_max' => $priceMax,
                    'in_stock' => $inStock
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve bulk order products: ' . $e->getMessage());
            return ApiResponse::error('Failed to retrieve bulk order products: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Place a bulk order
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function placeBulkOrder(Request $request)
    {
        try {
            $user = auth()->user();

            // Validate input
            $validator = Validator::make($request->all(), [
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'shipping_address_id' => 'required|exists:addresses,id,user_id,'.$user->id,
                'payment_method' => 'required|string',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error($validator->errors()->first(), 422);
            }

            // Find address
            $address = \App\Models\Address::findOrFail($request->input('shipping_address_id'));

            // Convert bulk order items to cart format
            $orderItems = [];
            foreach ($request->input('items') as $item) {
                $product = Product::findOrFail($item['product_id']);

                // Create an order item in the format expected by OrderProcessingService
                $orderItems[] = [
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'variation' => null,
                    'price' => $product->unit_price,
                    'tax' => 0,
                    'shipping_cost' => 0,
                    'shipping_type' => 'standard',
                    'product_referral_code' => null,
                    'notes' => $item['notes'] ?? null,
                    'discount' => 0
                ];
            }

            // Setup additional info
            $additionalInfo = [
                'additional_info' => $request->input('notes') ?? null,
                'is_gift' => false,
                'order_from' => 'dropshipper_bulk_order',
                'is_bulk_order' => true
            ];

            // Use OrderProcessingService for consistent workflow with process_order_from_cart
            $result = app(\App\Services\OrderProcessingService::class)->processBulkOrder(
                $user,
                $address->id,
                $request->input('payment_method'),
                $orderItems,
                $additionalInfo
            );

            if (!$result['success']) {
                return ApiResponse::error($result['message'], 400);
            }

            return $this->success($result['data'], 'Bulk order placed successfully');

        } catch (\Exception $e) {
            Log::error('Failed to place bulk order: ' . $e->getMessage());
            return ApiResponse::error('Failed to place bulk order: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get bulk pricing tiers for a product
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBulkPricingTiers(Request $request, $id)
    {
        try {
            $product = Product::find($id);

            if (!$product) {
                return ApiResponse::error('Product not found', 404);
            }

            // Get bulk pricing tiers from the database or calculate them
            $bulkPricingTiers = [
                [
                    'min_quantity' => 1,
                    'max_quantity' => 9,
                    'discount_percentage' => 0,
                    'price_per_unit' => (float)$product->unit_price,
                ],
                [
                    'min_quantity' => 10,
                    'max_quantity' => 49,
                    'discount_percentage' => 5,
                    'price_per_unit' => (float)($product->unit_price * 0.95),
                ],
                [
                    'min_quantity' => 50,
                    'max_quantity' => 99,
                    'discount_percentage' => 10,
                    'price_per_unit' => (float)($product->unit_price * 0.9),
                ],
                [
                    'min_quantity' => 100,
                    'max_quantity' => null,
                    'discount_percentage' => 15,
                    'price_per_unit' => (float)($product->unit_price * 0.85),
                ],
            ];

            // Prepare response
            $response = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'base_price' => (float)$product->unit_price,
                'min_bulk_quantity' => (int)$product->min_bulk_quantity,
                'tiers' => $bulkPricingTiers,
            ];

            return $this->success($response);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve bulk pricing tiers: ' . $e->getMessage());
            return ApiResponse::error('Failed to retrieve bulk pricing tiers: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get all child categories of a parent category
     *
     * @param int $parentId
     * @return array
     */
    private function getAllChildCategories($parentId)
    {
        $childCategories = [];

        $directChildren = Category::where('parent_id', $parentId)->pluck('id')->toArray();

        if (count($directChildren) > 0) {
            $childCategories = array_merge($childCategories, $directChildren);

            foreach ($directChildren as $childId) {
                $subChildren = $this->getAllChildCategories($childId);
                if (count($subChildren) > 0) {
                    $childCategories = array_merge($childCategories, $subChildren);
                }
            }
        }

        return $childCategories;
    }

    /**
     * Get top tier products with filtering and sorting
     */
    public function getTopTierProducts(Request $request)
    {
        try {
            $query = Product::where('is_top_tier', true)
                ->where('published', 1)
                ->where('approved', 1);

            // Apply search filter
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Apply category filter
            if ($request->has('category') && $request->category !== 'all') {
                $query->where('category_id', $request->category);
            }

            // Apply sorting
            switch ($request->get('sort_by')) {
                case 'price-low':
                    $query->orderBy('unit_price', 'asc');
                    break;
                case 'price-high':
                    $query->orderBy('unit_price', 'desc');
                    break;
                case 'rating':
                    $query->orderBy('rating', 'desc');
                    break;
                case 'margin':
                    $query->orderBy('margin', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }

            $products = $query->with(['category', 'brand'])
                ->limit(20)
                ->get();

            // Transform products to match frontend expectations
            $transformedProducts = $products->map(function($product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                return [
                    'id' => (string)$product->id,
                    'title' => $product->name,
                    'name' => $product->name,
                    'slug' => $product->slug ?? '',
                    'description' => strip_tags($product->description ?? ''),
                    'shortDescription' => strip_tags(substr($product->description ?? '', 0, 150)),
                    'price' => (float)$prices['displayPrice'],
                    'originalPrice' => (float)$prices['regularPrice'],
                    'discountPercentage' => $product->discount_type === 'percent' ? (float)$product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'dropshipperPrice' => (float)$prices['displayPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'rating' => (float)($product->rating ?? 0),
                    'reviewCount' => (int)($product->num_of_reviews ?? 0),
                    'ratingCount' => (int)($product->num_of_reviews ?? 0),
                    'stock' => (int)($product->current_stock ?? 0),
                    'inStock' => ($product->current_stock ?? 0) > 0,
                    'isInStock' => ($product->current_stock ?? 0) > 0,
                    'isOutOfStock' => ($product->current_stock ?? 0) <= 0,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'images' => [],
                    'brand' => [
                        'id' => (string)($product->brand_id ?? ''),
                        'name' => $product->brand ? $product->brand->name : '',
                        'slug' => $product->brand ? ($product->brand->slug ?? '') : ''
                    ],
                    'categories' => $product->category ? [[
                        'id' => (string)$product->category->id,
                        'name' => $product->category->name,
                        'slug' => $product->category->slug ?? ''
                    ]] : [],
                    'tags' => $product->tags ? explode(',', $product->tags) : [],
                    'isNew' => false,
                    'isBestSeller' => false,
                    'isSale' => ($product->discount ?? 0) > 0,
                    'isFeatured' => (bool)($product->featured ?? false),
                    'isTopRated' => ($product->rating ?? 0) > 4,
                    'isPopular' => ($product->num_of_sale ?? 0) > 10,
                    'isTrending' => $product->created_at ? $product->created_at->gt(now()->subDays(30)) : false,
                    'isLimitedStock' => ($product->current_stock ?? 0) < 10,
                    'createdAt' => $product->created_at ? $product->created_at->toISOString() : null,
                    'updatedAt' => $product->updated_at ? $product->updated_at->toISOString() : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProducts,
                'total' => $products->count()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getTopTierProducts: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch top tier products',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get top tier products statistics
     */
    public function getTopTierStats()
    {
        try {
            $stats = [
                'totalPremiumProducts' => Product::where('is_top_tier', true)->count(),
                'averageMargin' => Product::where('is_top_tier', true)->avg('margin') ?? 0,
                'earlyAccessProducts' => Product::where('is_early_access', true)->count(),
                'limitedOffers' => Product::where('is_limited_offer', true)->count()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getTopTierStats: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch top tier stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get early access products
     */
    public function getEarlyAccessProducts(Request $request)
    {
        try {
            $query = Product::query()
                ->where('is_early_access', true)
                ->where('published', 1)
                ->where('approved', 1);

            // Apply search filter
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Apply category filter
            if ($request->has('category') && $request->category !== 'all') {
                $query->where('category_id', $request->category);
            }

            // Apply sorting
            switch ($request->sort_by) {
                case 'price-low':
                    $query->orderBy('unit_price', 'asc');
                    break;
                case 'price-high':
                    $query->orderBy('unit_price', 'desc');
                    break;
                case 'rating':
                    $query->orderBy('rating', 'desc');
                    break;
                case 'margin':
                    $query->orderBy('margin', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }

            $products = $query->with(['category', 'brand', 'reviews', 'thumbnail'])
                ->paginate(20);

            // Transform products to match frontend expectations
            $transformedProducts = $products->map(function($product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                return [
                    'id' => (string)$product->id,
                    'title' => $product->name,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'description' => strip_tags($product->description),
                    'shortDescription' => strip_tags($product->description),
                    'price' => (float)$prices['displayPrice'],
                    'originalPrice' => (float)$prices['regularPrice'],
                    'discountPercentage' => $product->discount_type === 'percent' ? (float)$product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'dropshipperPrice' => (float)$prices['displayPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'rating' => (float)$product->rating,
                    'reviewCount' => (int)$product->num_of_reviews,
                    'ratingCount' => (int)$product->num_of_reviews,
                    'stock' => (int)$product->current_stock,
                    'inStock' => $product->current_stock > 0,
                    'isInStock' => $product->current_stock > 0,
                    'isOutOfStock' => $product->current_stock <= 0,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'images' => $product->photos ? array_map(function($photo) {
                        return uploaded_asset($photo);
                    }, json_decode($product->photos, true) ?? []) : [],
                    'brand' => [
                        'id' => (string)($product->brand_id ?? ''),
                        'name' => $product->brand ? $product->brand->name : '',
                        'slug' => $product->brand ? $product->brand->slug : ''
                    ],
                    'categories' => $product->category ? [[
                        'id' => (string)$product->category->id,
                        'name' => $product->category->name,
                        'slug' => $product->category->slug ?? ''
                    ]] : [],
                    'tags' => $product->tags ? explode(',', $product->tags) : [],
                    'isNew' => false,
                    'isBestSeller' => false,
                    'isSale' => $product->discount > 0,
                    'isFeatured' => (bool)$product->featured,
                    'isTopRated' => $product->rating > 4,
                    'isPopular' => $product->num_of_sale > 10,
                    'isTrending' => $product->created_at->gt(now()->subDays(30)),
                    'isLimitedStock' => $product->current_stock < 10,
                    'createdAt' => $product->created_at->toISOString(),
                    'updatedAt' => $product->updated_at->toISOString()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProducts
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getEarlyAccessProducts: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch early access products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get limited time offers
     */
    public function getLimitedOffers(Request $request)
    {
        try {
            $query = Product::query()
                ->where('is_limited_offer', true)
                ->where('published', 1)
                ->where('approved', 1);

            // Only include offers that haven't expired - but make it optional since the field might not exist
            if (Schema::hasColumn('products', 'offer_end_date')) {
                $query->where(function($q) {
                    $q->whereNull('offer_end_date')
                        ->orWhere('offer_end_date', '>', now());
                });
            }

            // Apply search filter
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Apply category filter
            if ($request->has('category') && $request->category !== 'all') {
                $query->where('category_id', $request->category);
            }

            // Apply sorting
            switch ($request->get('sort_by')) {
                case 'price-low':
                    $query->orderBy('unit_price', 'asc');
                    break;
                case 'price-high':
                    $query->orderBy('unit_price', 'desc');
                    break;
                case 'rating':
                    $query->orderBy('rating', 'desc');
                    break;
                case 'margin':
                    $query->orderBy('margin', 'desc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }

            $products = $query->with(['category', 'brand'])
                ->limit(20)
                ->get();

            // Transform products to match frontend expectations
            $transformedProducts = $products->map(function($product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                return [
                    'id' => (string)$product->id,
                    'title' => $product->name,
                    'name' => $product->name,
                    'slug' => $product->slug ?? '',
                    'description' => strip_tags($product->description ?? ''),
                    'shortDescription' => strip_tags(substr($product->description ?? '', 0, 150)),
                    'price' => (float)$prices['displayPrice'],
                    'originalPrice' => (float)$prices['regularPrice'],
                    'discountPercentage' => $product->discount_type === 'percent' ? (float)$product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'dropshipperPrice' => (float)$prices['displayPrice'],
                    'has_discount' => $product->discount > 0,
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                    'discountPercent' => $product->discount_type === 'percent' ? (float)$product->discount : null,
                    'rating' => (float)($product->rating ?? 0),
                    'reviewCount' => (int)($product->num_of_reviews ?? 0),
                    'ratingCount' => (int)($product->num_of_reviews ?? 0),
                    'stock' => (int)($product->current_stock ?? 0),
                    'inStock' => ($product->current_stock ?? 0) > 0,
                    'isInStock' => ($product->current_stock ?? 0) > 0,
                    'isOutOfStock' => ($product->current_stock ?? 0) <= 0,
                    'thumbnail' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                    'images' => [],
                    'brand' => [
                        'id' => (string)($product->brand_id ?? ''),
                        'name' => $product->brand ? $product->brand->name : '',
                        'slug' => $product->brand ? ($product->brand->slug ?? '') : ''
                    ],
                    'categories' => $product->category ? [[
                        'id' => (string)$product->category->id,
                        'name' => $product->category->name,
                        'slug' => $product->category->slug ?? ''
                    ]] : [],
                    'tags' => $product->tags ? explode(',', $product->tags) : [],
                    'isNew' => false,
                    'isBestSeller' => false,
                    'isSale' => ($product->discount ?? 0) > 0,
                    'isFeatured' => (bool)($product->featured ?? false),
                    'isTopRated' => ($product->rating ?? 0) > 4,
                    'isPopular' => ($product->num_of_sale ?? 0) > 10,
                    'isTrending' => $product->created_at ? $product->created_at->gt(now()->subDays(30)) : false,
                    'isLimitedStock' => ($product->current_stock ?? 0) < 10,
                    'createdAt' => $product->created_at ? $product->created_at->toISOString() : null,
                    'updatedAt' => $product->updated_at ? $product->updated_at->toISOString() : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedProducts,
                'total' => $products->count()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getLimitedOffers: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch limited offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
