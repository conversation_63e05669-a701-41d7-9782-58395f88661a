<?php

namespace App\Http\Controllers;

use PDF;
use Auth;
use Artisan;
use Cache;
use App\Models\User;
use App\Models\Brand;
use App\Models\Product;
use App\Models\Category;
use App\Models\ImportStatus;
use App\Models\VariantsExport;
use App\Models\ProductsExport;
use App\Models\ProductsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\ProductService;
use App\Services\ProductTaxService;
use App\Services\ProductStockService;
use App\Jobs\ProcessBrandBulkUpload;
use App\Imports\BrandBulkImport;
use App\Imports\RowsCountImport;
use App\Exports\BrandExport;

class BrandBulkUploadController extends Controller
{
    public function __construct(
        ProductService $productService,
        ProductTaxService $productTaxService,
        ProductStockService $productStockService
    ) {
        $this->productService = $productService;
        $this->productTaxService = $productTaxService;
        $this->productStockService = $productStockService;

        $this->middleware(['permission:product_bulk_import'])->only('index');
        $this->middleware(['permission:product_bulk_export'])->only('export');
    }

    public function index()
    {
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.product_bulk_upload.index');
            }
            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_brand_upload.index');
        }
    }

    public function bulk_brand_upload(Request $request)
    {
        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'insert',
                    'type' => 'brand',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessBrandBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Brands imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing brands: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing brands: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }

        return redirect()->back();
    }

    public function export()
    {
        //dd('dd');
        return Excel::download(new BrandExport, 'brands.xlsx');
    }

    public function pdf_download_products()
    {
        $products = Product::all();
        return PDF::loadView('backend.downloads.products', [
            'products' => $products,
        ], [], [])->download('products.pdf');
    }
    public function excel_download_brands()
    {
        return Excel::download(new BrandExport, 'brands.xlsx');
    }

    //bulk edit
    public function bulkEdit(){
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.brand_bulk_upload.index');
            }

            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_brand_upload.brand_bulk_edit');
        }
    }

    //bulk-update
    public function bulkBrandUpdate(Request $request)
    {

        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'update',
                    'type' => 'brand',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessBrandBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Brands imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing Brands: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing Brands: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }

        return back();
    }

    public function bulk_brand_delete(){
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.brand_bulk_upload.index');
            }

            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_brand_upload.brand_bulk_delete');
        }
    }

    public function bulk_brand_destroy(Request $request)
    {

        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'delete',
                    'type' => 'brand',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessBrandBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Brands imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing brands: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing brands: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }
        return back();
    }

    public function getbulkUploadStatus(Request $request)
    {
        $userId = $request->user()->id;
        $importStatus = ImportStatus::where('user_id', $userId)->orderBy('created_at','desc')->first();

          if ($importStatus) {
            $total = $importStatus->total;
            $successPercent = number_format(($importStatus->success / $total) * 100, 2);
            $errorPercent = number_format(($importStatus->errors / $total) * 100, 2);
            $pendingPercent = number_format(($importStatus->pending / $total) * 100, 2);

            return response()->json([
                'progress' => $importStatus->progress,
                'success' => $importStatus->success,
                'errors' => $importStatus->errors,
                'pending' => $importStatus->pending,
                'status' => $importStatus->status,
                'success_percent' => $successPercent,
                'error_percent' => $errorPercent,
                'pending_percent' => $pendingPercent,
                'rejectItems' => $importStatus->reject_item,
                'errorMessages' => $importStatus->error_messages,

            ]);
        }
        return response()->json(['status' => 'no_import_found'], 404);
    }
    
    private function startQueueWorker()
    {
        $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
        exec($command);
    }
}