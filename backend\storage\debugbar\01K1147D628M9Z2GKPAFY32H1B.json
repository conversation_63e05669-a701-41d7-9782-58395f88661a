{"__meta": {"id": "01K1147D628M9Z2GKPAFY32H1B", "datetime": "2025-07-25 08:46:30", "utime": **********.210579, "method": "GET", "uri": "/buzfi-new-backend/api/v3/categories/featured", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.64958, "end": **********.210591, "duration": 0.5610110759735107, "duration_str": "561ms", "measures": [{"label": "Booting", "start": **********.64958, "relative_start": 0, "end": **********.792944, "relative_end": **********.792944, "duration": 0.*****************, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.79295, "relative_start": 0.****************, "end": **********.210592, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "418ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.800454, "relative_start": 0.*****************, "end": **********.803057, "relative_end": **********.803057, "duration": 0.0026030540466308594, "duration_str": "2.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.207136, "relative_start": 0.****************, "end": **********.207361, "relative_end": **********.207361, "duration": 0.000225067138671875, "duration_str": "225μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.209185, "relative_start": 0.****************, "end": **********.209231, "relative_end": **********.209231, "duration": 4.601478576660156e-05, "duration_str": "46μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.28218, "accumulated_duration_str": "282ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wizard_infos` where `slug` = 'home-page-featured-category' and `wizard_info_status` = 1 order by `wizard_info_position` asc limit 1", "type": "query", "params": [], "bindings": ["home-page-featured-category", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 233}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.814706, "duration": 0.01276, "duration_str": "12.76ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:244", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=244", "ajax": false, "filename": "ApiHomePageController.php", "line": "244"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 4.522}, {"sql": "select * from `wizard_details` where `wizard_details`.`wizard_info_id` in (6) order by `wizard_detail_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 233}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.829777, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:244", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=244", "ajax": false, "filename": "ApiHomePageController.php", "line": "244"}, "connection": "buzfi", "explain": null, "start_percent": 4.522, "width_percent": 0.202}, {"sql": "select * from `categories` where `categories`.`id` in (4, 10, 65, 68, 72, 73, 85, 103, 109, 204)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 233}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.831699, "duration": 0.01024, "duration_str": "10.24ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:244", "source": {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=244", "ajax": false, "filename": "ApiHomePageController.php", "line": "244"}, "connection": "buzfi", "explain": null, "start_percent": 4.724, "width_percent": 3.629}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 10 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.882293, "duration": 0.02425, "duration_str": "24.25ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 8.353, "width_percent": 8.594}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026163' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026163"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9082139, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 16.947, "width_percent": 0.198}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028357' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028357"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.952805, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 17.145, "width_percent": 0.138}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 68 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [68], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9541228, "duration": 0.027120000000000002, "duration_str": "27.12ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 17.283, "width_percent": 9.611}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028358' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028358"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.982379, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 26.894, "width_percent": 0.12}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 204 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.983609, "duration": 0.024120000000000003, "duration_str": "24.12ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 27.015, "width_percent": 8.548}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028359' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028359"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.008572, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 35.562, "width_percent": 0.124}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 109 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0097241, "duration": 0.02944, "duration_str": "29.44ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 35.686, "width_percent": 10.433}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028360' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028360"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.039983, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.119, "width_percent": 0.106}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 4 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.04127, "duration": 0.02284, "duration_str": "22.84ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 46.226, "width_percent": 8.094}, {"sql": "select * from `uploads` where `uploads`.`id` = '34054' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["34054"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.064882, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 54.32, "width_percent": 0.124}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028361' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028361"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.065831, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 54.444, "width_percent": 0.064}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 65 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.066609, "duration": 0.027960000000000002, "duration_str": "27.96ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 54.508, "width_percent": 9.909}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028362' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028362"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.095725, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 64.416, "width_percent": 0.124}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 85 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [85], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.096866, "duration": 0.02447, "duration_str": "24.47ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 64.54, "width_percent": 8.672}, {"sql": "select * from `uploads` where `uploads`.`id` = '39457' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["39457"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1222432, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 73.212, "width_percent": 0.103}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028363' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028363"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.123301, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 73.315, "width_percent": 0.096}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 103 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.124306, "duration": 0.024579999999999998, "duration_str": "24.58ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 73.411, "width_percent": 8.711}, {"sql": "select * from `uploads` where `uploads`.`id` = '11424' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11424"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1500108, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 82.121, "width_percent": 0.152}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028365' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028365"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.151472, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 82.274, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 72 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1526332, "duration": 0.02485, "duration_str": "24.85ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 82.391, "width_percent": 8.806}, {"sql": "select * from `uploads` where `uploads`.`id` = '4880' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4880"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1786199, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 91.197, "width_percent": 0.131}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028435' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028435"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1797729, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 91.328, "width_percent": 0.078}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028364' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028364"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.180691, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 91.406, "width_percent": 0.064}, {"sql": "select count(*) as aggregate from `products` where `products`.`category_id` = 73 and `products`.`category_id` is not null", "type": "query", "params": [], "bindings": [73], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/CategoryResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\CategoryResource.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.181518, "duration": 0.02407, "duration_str": "24.07ms", "memory": 0, "memory_str": null, "filename": "Category.php:35", "source": {"index": 19, "namespace": null, "name": "app/Models/Category.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Category.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=35", "ajax": false, "filename": "Category.php", "line": "35"}, "connection": "buzfi", "explain": null, "start_percent": 91.47, "width_percent": 8.53}]}, "models": {"data": {"App\\Models\\Upload": {"retrieved": 15, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\WizardDetail": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardDetail.php&line=1", "ajax": false, "filename": "WizardDetail.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\WizardInfo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardInfo.php&line=1", "ajax": false, "filename": "WizardInfo.php", "line": "?"}}}, "count": 36, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 36}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/categories/featured", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@featured_categories", "uri": "GET api/v3/categories/featured", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@featured_categories<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=228\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=228\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:228-248</a>", "middleware": "api", "duration": "562ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1671008110 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671008110\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-864474386 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-864474386\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2103397224 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6Ilg2Y3REK2MyaUkvMUFuTXJ0RFl0Vnc9PSIsInZhbHVlIjoidnk5cjZRcThRcy84OGdNSUZRcGdwbHNrOTBvZGowOWdWM0FDTlhvajNTN2FGcmUxTXpyM3I1eEtjdWxaYTlrTWdYdGM3MVNyZ01UM0UxYm9kSWV4cllyaE5abjZvQ3pCS0IxazQwYnJRTmN4cGI0Q3VDaFA1UjBYdndaeXNmZWgiLCJtYWMiOiI2NzE1MzcwZmFhMTM0ZDBlMWZmZmEyN2RiZmExYjY4OTE4OTRiMTE0YTBiYzY2OTk3ODkwMjQ3NjRmNDVlMmJjIiwidGFnIjoiIn0%3D; buzficom_session=ydxjsSgVWUNJxMjkbADFEfhCSMmsBD6wyVSsP2Co</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103397224\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2029290026 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-cache</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">MISS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">595</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNZM0hJdGJNb21sYnFvOEtXQVV6bFE9PSIsInZhbHVlIjoiYjY2QWdnY3JFdk9iNWlKK09kdGJzQkgwWnRBTVFWcndETDcxN3E0VXJGSTk0U1huQTFnZVg2K24rNVlaUmt5YWhMVEJ0aFlKUDJBOHc3YWNCeDU1UllGUysydnc0SWNpZHVkai82UFVtUHErODFEdUxNejJGMlNJYkFrYXh4dysiLCJtYWMiOiJjMWEzZGUyZmJhZjNiMjA3Y2U3NjZiODBhNjAzYmE5ZDRiMjBmOGIyZmU2ZDI4MDQxYWYwZjkwNmM1MzljNTk5IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IllaSmQvWmM0WkdYQXBObkZ1NjJmRHc9PSIsInZhbHVlIjoiaTUxVWJjUXYyZ0hTVHA5UjI2NTF4eVpocUkxNGNNUWxMTGJvTG4zR3RXdW80KzBlNXFHbHhoVEh1ajJ5VUc4dDVFN3FOWUJsRExiQW11QzZ5SVppb0E4bjgxaFZmZlgyRncvWjFlU0QwVHluK20zNnNob1R5a201a3BaMTV1RmwiLCJtYWMiOiI0MmJlMDdmMWM3NGUyMmRmN2U1MjdjMWFjNjU1NjAwNzNmMmUwOGM5ZDA5NWEwODkwMTUwZmE5NzE2ZDlhMDIzIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNZM0hJdGJNb21sYnFvOEtXQVV6bFE9PSIsInZhbHVlIjoiYjY2QWdnY3JFdk9iNWlKK09kdGJzQkgwWnRBTVFWcndETDcxN3E0VXJGSTk0U1huQTFnZVg2K24rNVlaUmt5YWhMVEJ0aFlKUDJBOHc3YWNCeDU1UllGUysydnc0SWNpZHVkai82UFVtUHErODFEdUxNejJGMlNJYkFrYXh4dysiLCJtYWMiOiJjMWEzZGUyZmJhZjNiMjA3Y2U3NjZiODBhNjAzYmE5ZDRiMjBmOGIyZmU2ZDI4MDQxYWYwZjkwNmM1MzljNTk5IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IllaSmQvWmM0WkdYQXBObkZ1NjJmRHc9PSIsInZhbHVlIjoiaTUxVWJjUXYyZ0hTVHA5UjI2NTF4eVpocUkxNGNNUWxMTGJvTG4zR3RXdW80KzBlNXFHbHhoVEh1ajJ5VUc4dDVFN3FOWUJsRExiQW11QzZ5SVppb0E4bjgxaFZmZlgyRncvWjFlU0QwVHluK20zNnNob1R5a201a3BaMTV1RmwiLCJtYWMiOiI0MmJlMDdmMWM3NGUyMmRmN2U1MjdjMWFjNjU1NjAwNzNmMmUwOGM5ZDA5NWEwODkwMTUwZmE5NzE2ZDlhMDIzIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029290026\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1280558645 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost/buzfi-new-backend/api/v3/categories/featured</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1280558645\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/categories/featured", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@featured_categories"}, "badge": null}}