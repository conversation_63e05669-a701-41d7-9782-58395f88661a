{"__meta": {"id": "01K1147E2A1R78E9ZKY718MX1B", "datetime": "2025-07-25 08:46:31", "utime": **********.115004, "method": "GET", "uri": "/buzfi-new-backend/api/v3/products/on-sale", "ip": "::1"}, "messages": {"count": 22, "messages": [{"message": "[08:46:30] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/products\\/on-sale\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.511162, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.511564, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.637987, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.63806, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.684965, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.685043, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 1169", "message_html": null, "is_string": false, "label": "info", "time": **********.719906, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 1169", "message_html": null, "is_string": false, "label": "info", "time": **********.719976, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33037", "message_html": null, "is_string": false, "label": "info", "time": **********.798808, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33037", "message_html": null, "is_string": false, "label": "info", "time": **********.798879, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33016", "message_html": null, "is_string": false, "label": "info", "time": **********.871798, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33016", "message_html": null, "is_string": false, "label": "info", "time": **********.871897, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33015", "message_html": null, "is_string": false, "label": "info", "time": **********.924649, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33015", "message_html": null, "is_string": false, "label": "info", "time": **********.924722, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33014", "message_html": null, "is_string": false, "label": "info", "time": **********.961553, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33014", "message_html": null, "is_string": false, "label": "info", "time": **********.961627, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33009", "message_html": null, "is_string": false, "label": "info", "time": **********.995844, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:30] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33009", "message_html": null, "is_string": false, "label": "info", "time": **********.995936, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:31] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 33006", "message_html": null, "is_string": false, "label": "info", "time": **********.04521, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:31] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 33006", "message_html": null, "is_string": false, "label": "info", "time": **********.0453, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:31] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31991", "message_html": null, "is_string": false, "label": "info", "time": **********.078833, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:31] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31991", "message_html": null, "is_string": false, "label": "info", "time": **********.078923, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.315586, "end": **********.115032, "duration": 0.7994458675384521, "duration_str": "799ms", "measures": [{"label": "Booting", "start": **********.315586, "relative_start": 0, "end": **********.467955, "relative_end": **********.467955, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.467961, "relative_start": 0.****************, "end": **********.115034, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "647ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.475758, "relative_start": 0.****************, "end": **********.478776, "relative_end": **********.478776, "duration": 0.003017902374267578, "duration_str": "3.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.112041, "relative_start": 0.***************, "end": **********.112341, "relative_end": **********.112341, "duration": 0.0002999305725097656, "duration_str": "300μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.113929, "relative_start": 0.****************, "end": **********.113965, "relative_end": **********.113965, "duration": 3.600120544433594e-05, "duration_str": "36μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 177, "nb_statements": 177, "nb_visible_statements": 177, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.46336000000000005, "accumulated_duration_str": "463ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 77 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `products` where `published` = '1' and `auction_product` = 0 and `approved` = '1' and `discount` >= 5 order by `discount` desc limit 20", "type": "query", "params": [], "bindings": ["1", 0, "1", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5137591, "duration": 0.10742, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:1133", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1133", "ajax": false, "filename": "ApiProductController.php", "line": "1133"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 23.183}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (1169, 1593, 1594, 1597, 1598, 1599, 1600, 1601, 31934, 31935, 31937, 31938, 31940, 31991, 33006, 33009, 33014, 33015, 33016, 33037)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.623441, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:1133", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1133", "ajax": false, "filename": "ApiProductController.php", "line": "1133"}, "connection": "buzfi", "explain": null, "start_percent": 23.183, "width_percent": 0.606}, {"sql": "select * from `product_taxes` where `product_taxes`.`product_id` in (1169, 1593, 1594, 1597, 1598, 1599, 1600, 1601, 31934, 31935, 31937, 31938, 31940, 31991, 33006, 33009, 33014, 33015, 33016, 33037)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.627164, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:1133", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1133", "ajax": false, "filename": "ApiProductController.php", "line": "1133"}, "connection": "buzfi", "explain": null, "start_percent": 23.789, "width_percent": 0.501}, {"sql": "select * from `uploads` where `uploads`.`id` in (2467, 4918, 4920, 4926, 4928, 4930, 4932, 4934, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********) and `uploads`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.631283, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:1133", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1133", "ajax": false, "filename": "ApiProductController.php", "line": "1133"}, "connection": "buzfi", "explain": null, "start_percent": 24.29, "width_percent": 0.158}, {"sql": "select * from `suppliers` where `suppliers`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.632885, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:1133", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 1133}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1133", "ajax": false, "filename": "ApiProductController.php", "line": "1133"}, "connection": "buzfi", "explain": null, "start_percent": 24.448, "width_percent": 0.086}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.636756, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 24.534, "width_percent": 0.082}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31938 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31938, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.638572, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 24.616, "width_percent": 0.078}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.639522, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 24.694, "width_percent": 1.275}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.647015, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 25.969, "width_percent": 0.19}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.648632, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 26.159, "width_percent": 0.147}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31938 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31938], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.650948, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 26.306, "width_percent": 0.101}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.652007, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 26.407, "width_percent": 1.209}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.658063, "duration": 0.01033, "duration_str": "10.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 27.616, "width_percent": 2.229}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6689422, "duration": 0.00554, "duration_str": "5.54ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 29.845, "width_percent": 1.196}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31938 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31938, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6750062, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 31.041, "width_percent": 0.06}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.675746, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 31.101, "width_percent": 1.209}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.682154, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 32.31, "width_percent": 0.08}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.682951, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 32.39, "width_percent": 0.032}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.683487, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 32.422, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.684149, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 32.452, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31937 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31937, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.6852229, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 32.489, "width_percent": 0.043}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.685811, "duration": 0.00539, "duration_str": "5.39ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 32.532, "width_percent": 1.163}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.6916049, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 33.695, "width_percent": 0.05}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.692274, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 33.745, "width_percent": 0.108}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31937 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31937], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.693667, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 33.853, "width_percent": 0.069}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.694473, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 33.922, "width_percent": 1.17}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.700438, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 35.092, "width_percent": 1.172}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.7064018, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 36.263, "width_percent": 1.191}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31937 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31937, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.712511, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 37.455, "width_percent": 0.043}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.713118, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 37.498, "width_percent": 1.172}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.719102, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 38.67, "width_percent": 0.041}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1169 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [1169, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.720198, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 38.711, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 1169 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.720804, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 38.758, "width_percent": 1.174}, {"sql": "select * from `brands` where `brands`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.726634, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 39.932, "width_percent": 0.037}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 12 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.727215, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 39.969, "width_percent": 0.097}, {"sql": "select * from `uploads` where `uploads`.`id` = '2454' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2454"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.728196, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 40.066, "width_percent": 0.037}, {"sql": "select * from `uploads` where `uploads`.`id` = '2467' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2467"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.767317, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 40.103, "width_percent": 0.071}, {"sql": "select * from `uploads` where `uploads`.`id` = '2466' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2466"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.7683818, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 40.174, "width_percent": 0.045}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 1169 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 1169], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.7691329, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 40.219, "width_percent": 0.05}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 1169 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.769864, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 40.269, "width_percent": 1.217}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 1169 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.775973, "duration": 0.00558, "duration_str": "5.58ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 41.486, "width_percent": 1.204}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 1169 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.782131, "duration": 0.0056, "duration_str": "5.6ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 42.69, "width_percent": 1.209}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 1169 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [1169, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.788155, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 43.899, "width_percent": 0.041}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 1169 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [1169], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.788765, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 43.94, "width_percent": 1.189}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.79475, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 45.129, "width_percent": 0.037}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.7953181, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 45.166, "width_percent": 0.032}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.795854, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 45.198, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = 151 limit 1", "type": "query", "params": [], "bindings": [151], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.796498, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 45.228, "width_percent": 0.041}, {"sql": "select * from `uploads` where `uploads`.`id` = '17439' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["17439"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.797235, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 45.269, "width_percent": 0.041}, {"sql": "select * from `uploads` where `uploads`.`id` = '10000250' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10000250"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.798064, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 45.31, "width_percent": 0.045}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33037 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [33037, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.799077, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 45.356, "width_percent": 0.039}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.7996309, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 45.395, "width_percent": 1.187}, {"sql": "select * from `brands` where `brands`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.805506, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 46.581, "width_percent": 0.037}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 1 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.806082, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 46.618, "width_percent": 0.097}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028385' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028385"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.807054, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.715, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.807772, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.75, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.808583, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.784, "width_percent": 0.047}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026240' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026240"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.809373, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.832, "width_percent": 0.037}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026241' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026241"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.810096, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.869, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = '10026239' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10026239"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.810777, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 46.903, "width_percent": 0.037}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 33037 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 33037], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.811434, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 46.94, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.8121958, "duration": 0.0057, "duration_str": "5.7ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 46.987, "width_percent": 1.23}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.818475, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 48.217, "width_percent": 1.183}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.824517, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 49.4, "width_percent": 1.187}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33037 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [33037, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.830628, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 50.587, "width_percent": 0.041}, {"sql": "select * from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 83}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.846481, "duration": 0.013869999999999999, "duration_str": "13.87ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:117", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=117", "ajax": false, "filename": "DelegatesToResource.php", "line": "117"}, "connection": "buzfi", "explain": null, "start_percent": 50.628, "width_percent": 2.993}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33037 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33037], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.860989, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 53.621, "width_percent": 1.202}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.86729, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 54.823, "width_percent": 0.045}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.8679621, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 54.869, "width_percent": 0.032}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.86848, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 54.901, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.8693938, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 54.931, "width_percent": 0.05}, {"sql": "select * from `uploads` where `uploads`.`id` = '4570' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4570"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.870135, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 54.981, "width_percent": 0.043}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028421' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028421"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.870909, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 55.024, "width_percent": 0.035}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33016 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [33016, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.872133, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 55.059, "width_percent": 0.058}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.872894, "duration": 0.00561, "duration_str": "5.61ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 55.117, "width_percent": 1.211}, {"sql": "select * from `brands` where `brands`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.878987, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 56.328, "width_percent": 0.043}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 1 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.879777, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 56.371, "width_percent": 0.125}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.880992, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 56.496, "width_percent": 0.037}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 33016 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 33016], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.8817458, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 56.533, "width_percent": 0.052}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.88247, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 56.585, "width_percent": 1.174}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.888458, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 57.759, "width_percent": 1.189}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.8943648, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 58.948, "width_percent": 1.191}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33016 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [33016, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9004478, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 60.139, "width_percent": 0.05}, {"sql": "select * from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 83}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.9010801, "duration": 0.013800000000000002, "duration_str": "13.8ms", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:117", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=117", "ajax": false, "filename": "DelegatesToResource.php", "line": "117"}, "connection": "buzfi", "explain": null, "start_percent": 60.189, "width_percent": 2.978}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33016 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33016], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.915551, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 63.167, "width_percent": 1.176}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.921636, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 64.343, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.922206, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 64.382, "width_percent": 0.037}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.922739, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 64.419, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.923761, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 64.449, "width_percent": 0.039}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33015 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [33015, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9249332, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 64.488, "width_percent": 0.063}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33015 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33015], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.925622, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 64.55, "width_percent": 1.206}, {"sql": "select * from `brands` where `brands`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.931582, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 65.757, "width_percent": 0.037}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 1 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.932188, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 65.793, "width_percent": 0.097}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.93324, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 65.89, "width_percent": 0.041}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028415' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028415"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.934079, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 65.931, "width_percent": 0.035}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 33015 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 33015], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.934743, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 65.966, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33015 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33015], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9354742, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 66.013, "width_percent": 1.165}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33015 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33015], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9414742, "duration": 0.00591, "duration_str": "5.91ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 67.179, "width_percent": 1.275}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 33015 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [33015], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9478722, "duration": 0.0054, "duration_str": "5.4ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 68.454, "width_percent": 1.165}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33015 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [33015, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.953793, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 69.62, "width_percent": 0.045}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954409, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.665, "width_percent": 1.187}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960074, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 70.852, "width_percent": 0.043}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9603992, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 70.895, "width_percent": 0.03}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960642, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 70.925, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9610069, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 70.956, "width_percent": 0.039}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961824, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 70.994, "width_percent": 0.041}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9621098, "duration": 0.00534, "duration_str": "5.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 71.035, "width_percent": 1.152}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967549, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.188, "width_percent": 0.037}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9678402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.225, "width_percent": 0.095}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9685879, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.32, "width_percent": 0.039}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969058, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.358, "width_percent": 0.032}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969454, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.391, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969868, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.425, "width_percent": 0.032}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97027, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.458, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970668, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.492, "width_percent": 0.032}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.97103, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.525, "width_percent": 0.043}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9714139, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.568, "width_percent": 1.183}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977007, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 73.75, "width_percent": 1.17}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982522, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.92, "width_percent": 1.168}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98807, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.088, "width_percent": 0.063}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988517, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.15, "width_percent": 1.181}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.994175, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.331, "width_percent": 0.052}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.994548, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.383, "width_percent": 0.039}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.994843, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.421, "width_percent": 0.035}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.995256, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.456, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996149, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.493, "width_percent": 0.041}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996442, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 77.534, "width_percent": 1.159}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001924, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.693, "width_percent": 0.039}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.002279, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.731, "width_percent": 0.099}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.002988, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.831, "width_percent": 0.037}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.003562, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.867, "width_percent": 0.052}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004154, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.919, "width_percent": 0.05}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004586, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.969, "width_percent": 1.172}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.010122, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 80.141, "width_percent": 1.168}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.01566, "duration": 0.0072, "duration_str": "7.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 81.308, "width_percent": 1.554}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023033, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.862, "width_percent": 0.054}, {"sql": "select * from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023425, "duration": 0.01388, "duration_str": "13.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.916, "width_percent": 2.996}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0375, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.912, "width_percent": 1.178}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043139, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.09, "width_percent": 0.037}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043454, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.127, "width_percent": 0.032}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043719, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.159, "width_percent": 0.03}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.044722, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.189, "width_percent": 0.037}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045503, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.226, "width_percent": 0.039}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045783, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 87.265, "width_percent": 1.17}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.051292, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.434, "width_percent": 0.041}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0516212, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.475, "width_percent": 0.095}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052373, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.57, "width_percent": 0.037}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0528631, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.607, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053271, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.642, "width_percent": 0.035}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053707, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.676, "width_percent": 0.045}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.054107, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.722, "width_percent": 1.172}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0596528, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 89.893, "width_percent": 1.187}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0653, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.08, "width_percent": 1.183}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0709429, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 92.263, "width_percent": 0.041}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.071294, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 92.304, "width_percent": 1.181}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.07693, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.485, "width_percent": 0.037}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077227, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.521, "width_percent": 0.032}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077486, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.554, "width_percent": 0.028}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.077842, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.582, "width_percent": 0.037}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.078368, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.618, "width_percent": 0.043}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.079117, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.662, "width_percent": 0.041}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0793982, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.703, "width_percent": 1.168}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08511, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 94.87, "width_percent": 0.052}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08565, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 94.922, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.086123, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 94.956, "width_percent": 0.035}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.086528, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 94.991, "width_percent": 0.039}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.086975, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.03, "width_percent": 0.032}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.087369, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.062, "width_percent": 0.032}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.087743, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.095, "width_percent": 0.047}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.088147, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.142, "width_percent": 1.191}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.093781, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 96.333, "width_percent": 1.172}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.099339, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 97.505, "width_percent": 1.172}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.104912, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.677, "width_percent": 0.039}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1052508, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.716, "width_percent": 1.185}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1109629, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.901, "width_percent": 0.041}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.111288, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.942, "width_percent": 0.03}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.111537, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.972, "width_percent": 0.028}]}, "models": {"data": {"App\\Models\\ProductTax": {"retrieved": 54, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTax.php&line=1", "ajax": false, "filename": "ProductTax.php", "line": "?"}}, "App\\Models\\Upload": {"retrieved": 54, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\Tax": {"retrieved": 27, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductTranslation": {"retrieved": 18, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTranslation.php&line=1", "ajax": false, "filename": "ProductTranslation.php", "line": "?"}}, "App\\Models\\ProductStock": {"retrieved": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductStock.php&line=1", "ajax": false, "filename": "ProductStock.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\BrandTranslation": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrandTranslation.php&line=1", "ajax": false, "filename": "BrandTranslation.php", "line": "?"}}, "App\\Models\\Video": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FVideo.php&line=1", "ajax": false, "filename": "Video.php", "line": "?"}}, "App\\Models\\Supplier": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FSupplier.php&line=1", "ajax": false, "filename": "Supplier.php", "line": "?"}}}, "count": 220, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 220}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/products/on-sale", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getOnSaleProducts", "uri": "GET api/v3/products/on-sale", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getOnSaleProducts<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1117\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/products", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=1117\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiProductController.php:1117-1161</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "804ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-170406201 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-170406201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-907032675 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-907032675\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1609609967 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">public, max-age=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6IlNZM0hJdGJNb21sYnFvOEtXQVV6bFE9PSIsInZhbHVlIjoiYjY2QWdnY3JFdk9iNWlKK09kdGJzQkgwWnRBTVFWcndETDcxN3E0VXJGSTk0U1huQTFnZVg2K24rNVlaUmt5YWhMVEJ0aFlKUDJBOHc3YWNCeDU1UllGUysydnc0SWNpZHVkai82UFVtUHErODFEdUxNejJGMlNJYkFrYXh4dysiLCJtYWMiOiJjMWEzZGUyZmJhZjNiMjA3Y2U3NjZiODBhNjAzYmE5ZDRiMjBmOGIyZmU2ZDI4MDQxYWYwZjkwNmM1MzljNTk5IiwidGFnIjoiIn0%3D; buzficom_session=stHhv7kH3MVuiGuN2Jwp20RR3jJYSoa3DY7gwKeb</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609609967\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1992875460 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-cache</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">MISS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">594</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlFIZGJ4ZmxWd3RiemdiSTNpOXVLQ1E9PSIsInZhbHVlIjoiSnk4NnNSYTRGYnRiQlUyK05JYUNUQ0J5NGhTT2w0S1N3ZWllSWtGM3dpSjRGcWpyS2ZxdkJMWFcySlRRU0NoNk5MRkQyTzBxdHhNM0VZRmZFcnpNNTZjclduTzFsMmJYVlpiRDJmdHJyK2pjMDZMN3o5KzNnU0NWNkVjS25lbkIiLCJtYWMiOiI1MThlYmI1MDQ3ZGQzOTllZGEyMzM0ZTY1YjM4MzQxYmY4NzRmZWQwNWUzNmIzOWM0ODg2YWRmNGIxOWViZTdhIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6Ilh3NkhFdXF0UzVTZzBEc1gzcDhZTVE9PSIsInZhbHVlIjoiMEJuN2VFOWtzYlo3MzcwQmN0cEQyQmptVEVOWjc0NUZ5SzYvYUFQL0NVYWhaaitoZE1yOE5FTFM0cUhNbmRTVnRBUDRpd2gxNjdIRTU5S1BqUE9iUEE3WWluczVXamVVcWJXRHozVmNxemNTYzF6SFBtT0dNUkUvblFqTDdqUHYiLCJtYWMiOiI4ZGI2OTQ5NDllNzgzNDE3N2I1ZmIwYjAzMmM4YjcwOTM5NDI3Yzk4YjU3YzFkNWEyODAzM2Q5MTIwODhhMTYxIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlFIZGJ4ZmxWd3RiemdiSTNpOXVLQ1E9PSIsInZhbHVlIjoiSnk4NnNSYTRGYnRiQlUyK05JYUNUQ0J5NGhTT2w0S1N3ZWllSWtGM3dpSjRGcWpyS2ZxdkJMWFcySlRRU0NoNk5MRkQyTzBxdHhNM0VZRmZFcnpNNTZjclduTzFsMmJYVlpiRDJmdHJyK2pjMDZMN3o5KzNnU0NWNkVjS25lbkIiLCJtYWMiOiI1MThlYmI1MDQ3ZGQzOTllZGEyMzM0ZTY1YjM4MzQxYmY4NzRmZWQwNWUzNmIzOWM0ODg2YWRmNGIxOWViZTdhIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6Ilh3NkhFdXF0UzVTZzBEc1gzcDhZTVE9PSIsInZhbHVlIjoiMEJuN2VFOWtzYlo3MzcwQmN0cEQyQmptVEVOWjc0NUZ5SzYvYUFQL0NVYWhaaitoZE1yOE5FTFM0cUhNbmRTVnRBUDRpd2gxNjdIRTU5S1BqUE9iUEE3WWluczVXamVVcWJXRHozVmNxemNTYzF6SFBtT0dNUkUvblFqTDdqUHYiLCJtYWMiOiI4ZGI2OTQ5NDllNzgzNDE3N2I1ZmIwYjAzMmM4YjcwOTM5NDI3Yzk4YjU3YzFkNWEyODAzM2Q5MTIwODhhMTYxIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992875460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-377922403 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://localhost/buzfi-new-backend/api/v3/products/on-sale</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377922403\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/products/on-sale", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getOnSaleProducts"}, "badge": null}}