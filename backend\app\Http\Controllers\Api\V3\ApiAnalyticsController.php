<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\Category;
use App\Models\Shop;
use App\Models\Customer;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ApiAnalyticsController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get analytics dashboard overview
     */
    public function getDashboard(Request $request)
    {
        try {
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Get date range for comparisons
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays(30);
            $previousStartDate = Carbon::now()->subDays(60);
            $previousEndDate = Carbon::now()->subDays(30);
            
            // Get orders for the current period
            $orders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
            
            // Get orders for the previous period
            $previousOrders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->get();
            
            // Calculate metrics for current period
            $totalSales = $orders->where('payment_status', 'paid')->sum('grand_total');
            $totalOrders = $orders->count();
            $totalCustomers = $orders->pluck('user_id')->unique()->count();
            $averageOrderValue = $totalOrders > 0 ? $totalSales / $totalOrders : 0;
            
            // Calculate metrics for previous period
            $previousTotalSales = $previousOrders->where('payment_status', 'paid')->sum('grand_total');
            $previousTotalOrders = $previousOrders->count();
            $previousTotalCustomers = $previousOrders->pluck('user_id')->unique()->count();
            
            // Calculate growth rates
            $salesGrowth = $previousTotalSales > 0 ? (($totalSales - $previousTotalSales) / $previousTotalSales) * 100 : 0;
            $ordersGrowth = $previousTotalOrders > 0 ? (($totalOrders - $previousTotalOrders) / $previousTotalOrders) * 100 : 0;
            $customersGrowth = $previousTotalCustomers > 0 ? (($totalCustomers - $previousTotalCustomers) / $previousTotalCustomers) * 100 : 0;
            
            // Get revenue by channel (mock data for now)
            $revenueByChannel = [
                'website' => round($totalSales * 0.7, 2),
                'mobile_app' => round($totalSales * 0.2, 2),
                'pos' => round($totalSales * 0.1, 2),
            ];
            
            // Get sales by category
            $salesByCategory = [];
            
            // Group order details by product category
            $orderDetailsByCategory = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->join('categories', 'products.category_id', '=', 'categories.id')
                ->where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('orders.seller_id', $user->id);
                    }
                })
                ->where('orders.payment_status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->select([
                    'categories.name as category_name',
                    DB::raw('SUM(order_details.price * order_details.quantity) as total_sales')
                ])
                ->groupBy('categories.name')
                ->orderBy('total_sales', 'desc')
                ->get();
                
            foreach ($orderDetailsByCategory as $category) {
                $salesByCategory[$category->category_name] = round($category->total_sales, 2);
            }
            
            // Get top selling products
            $topSellingProducts = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('orders.seller_id', $user->id);
                    }
                })
                ->where('orders.payment_status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->select([
                    'products.id',
                    'products.name',
                    'products.sku',
                    DB::raw('SUM(order_details.quantity) as units'),
                    DB::raw('SUM(order_details.price * order_details.quantity) as revenue')
                ])
                ->groupBy('products.id', 'products.name', 'products.sku')
                ->orderBy('revenue', 'desc')
                ->limit(5)
                ->get()
                ->map(function($product) {
                    // For each product, calculate previous period sales for growth
                    // Mock growth for now
                    $growth = rand(-10, 30);
                    
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sku' => $product->sku,
                        'sales' => $product->units,
                        'revenue' => round($product->revenue, 2),
                        'units' => $product->units,
                        'growth' => $growth
                    ];
                });
                
            // Get sales trend (daily sales for the past 30 days)
            $salesTrend = [];
            
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::now()->subDays($i);
                $daySales = $orders->filter(function($order) use ($date) {
                    return $order->created_at->format('Y-m-d') === $date->format('Y-m-d') &&
                           $order->payment_status === 'paid';
                })->sum('grand_total');
                
                $salesTrend[] = [
                    'date' => $date->format('Y-m-d'),
                    'value' => round($daySales, 2)
                ];
            }
            
            // Sort by date ascending
            usort($salesTrend, function($a, $b) {
                return strtotime($a['date']) - strtotime($b['date']);
            });
            
            // Get customers trend (new customers per day for the past 30 days)
            $customersTrend = [];
            
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::now()->subDays($i);
                $dayCustomers = $orders->filter(function($order) use ($date) {
                    return $order->created_at->format('Y-m-d') === $date->format('Y-m-d');
                })->pluck('user_id')->unique()->count();
                
                $customersTrend[] = [
                    'date' => $date->format('Y-m-d'),
                    'value' => $dayCustomers
                ];
            }
            
            // Sort by date ascending
            usort($customersTrend, function($a, $b) {
                return strtotime($a['date']) - strtotime($b['date']);
            });
            
            $dashboardData = [
                'totalSales' => round($totalSales, 2),
                'totalOrders' => $totalOrders,
                'totalCustomers' => $totalCustomers,
                'averageOrderValue' => round($averageOrderValue, 2),
                'salesGrowth' => round($salesGrowth, 2),
                'ordersGrowth' => round($ordersGrowth, 2),
                'customersGrowth' => round($customersGrowth, 2),
                'revenueByChannel' => $revenueByChannel,
                'salesByCategory' => $salesByCategory,
                'topSellingProducts' => $topSellingProducts,
                'salesTrend' => $salesTrend,
                'customersTrend' => $customersTrend
            ];
            
            return $this->success($dashboardData);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch analytics dashboard: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get sales analytics
     */
    public function getSalesAnalytics(Request $request)
    {
        try {
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Get date range
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays(30);
            $previousStartDate = Carbon::now()->subDays(60);
            $previousEndDate = Carbon::now()->subDays(30);
            
            // Get orders for the current period
            $orders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
            
            // Get orders for the previous period
            $previousOrders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->get();
            
            // Calculate total sales
            $totalSales = $orders->where('payment_status', 'paid')->sum('grand_total');
            $previousTotalSales = $previousOrders->where('payment_status', 'paid')->sum('grand_total');
            
            // Calculate growth
            $salesGrowth = $previousTotalSales > 0 ? (($totalSales - $previousTotalSales) / $previousTotalSales) * 100 : 0;
            
            // Sales by period (daily, weekly, monthly)
            $salesByPeriod = [
                'daily' => round($totalSales / 30, 2),
                'weekly' => round($totalSales / 4, 2),
                'monthly' => round($totalSales, 2),
                'yearly' => round($totalSales * 12, 2)
            ];
            
            // Sales by channel (mock data for now)
            $salesByChannel = [
                'website' => round($totalSales * 0.7, 2),
                'mobile_app' => round($totalSales * 0.2, 2),
                'pos' => round($totalSales * 0.1, 2),
            ];
            
            // Sales by payment method
            $salesByPaymentMethod = [];
            
            $paymentMethods = $orders->where('payment_status', 'paid')->groupBy('payment_type');
            foreach ($paymentMethods as $method => $methodOrders) {
                $salesByPaymentMethod[$method] = round($methodOrders->sum('grand_total'), 2);
            }
            
            // If empty, add some mock data
            if (empty($salesByPaymentMethod)) {
                $salesByPaymentMethod = [
                    'credit_card' => round($totalSales * 0.4, 2),
                    'paypal' => round($totalSales * 0.3, 2),
                    'bank_transfer' => round($totalSales * 0.2, 2),
                    'cash' => round($totalSales * 0.1, 2)
                ];
            }
            
            // Sales by region (mock data based on shipping addresses)
            $salesByRegion = [
                'north_america' => round($totalSales * 0.4, 2),
                'europe' => round($totalSales * 0.3, 2),
                'asia' => round($totalSales * 0.2, 2),
                'other' => round($totalSales * 0.1, 2)
            ];
            
            // Sales trend (daily sales for the past 30 days)
            $salesTrend = [];
            
            for ($i = 0; $i < 30; $i++) {
                $date = Carbon::now()->subDays($i);
                $daySales = $orders->filter(function($order) use ($date) {
                    return $order->created_at->format('Y-m-d') === $date->format('Y-m-d') &&
                           $order->payment_status === 'paid';
                })->sum('grand_total');
                
                $salesTrend[] = [
                    'date' => $date->format('Y-m-d'),
                    'value' => round($daySales, 2)
                ];
            }
            
            // Sort by date ascending
            usort($salesTrend, function($a, $b) {
                return strtotime($a['date']) - strtotime($b['date']);
            });
            
            $salesAnalytics = [
                'total' => round($totalSales, 2),
                'growth' => round($salesGrowth, 2),
                'byPeriod' => $salesByPeriod,
                'byChannel' => $salesByChannel,
                'byPaymentMethod' => $salesByPaymentMethod,
                'byRegion' => $salesByRegion,
                'trend' => $salesTrend
            ];
            
            return $this->success($salesAnalytics);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch sales analytics: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get sales by period (day, week, month, year)
     */
    public function getSalesByPeriod(Request $request, $period)
    {
        $validator = Validator::make(['period' => $period], [
            'period' => 'required|string|in:day,week,month,year'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid period',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Determine date range based on period
            $endDate = Carbon::now();
            $startDate = null;
            $groupBy = null;
            $points = 0;
            
            switch ($period) {
                case 'day':
                    $startDate = Carbon::now()->subDay();
                    $groupBy = 'hour';
                    $points = 24;
                    break;
                case 'week':
                    $startDate = Carbon::now()->subWeek();
                    $groupBy = 'day';
                    $points = 7;
                    break;
                case 'month':
                    $startDate = Carbon::now()->subMonth();
                    $groupBy = 'day';
                    $points = 30;
                    break;
                case 'year':
                    $startDate = Carbon::now()->subYear();
                    $groupBy = 'month';
                    $points = 12;
                    break;
                default:
                    $startDate = Carbon::now()->subMonth();
                    $groupBy = 'day';
                    $points = 30;
            }
            
            // Get orders for the period
            $orders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
            
            // Generate data points based on period
            $dataPoints = [];
            
            if ($groupBy === 'hour') {
                // Hourly data for day period
                for ($i = 0; $i < $points; $i++) {
                    $hour = Carbon::now()->subHours($points - 1 - $i);
                    $hourSales = $orders->filter(function($order) use ($hour) {
                        return $order->created_at->format('Y-m-d H') === $hour->format('Y-m-d H');
                    })->sum('grand_total');
                    
                    $dataPoints[] = [
                        'date' => $hour->format('Y-m-d H:00'),
                        'value' => round($hourSales, 2)
                    ];
                }
            } elseif ($groupBy === 'day') {
                // Daily data for week/month period
                for ($i = 0; $i < $points; $i++) {
                    $day = Carbon::now()->subDays($points - 1 - $i);
                    $daySales = $orders->filter(function($order) use ($day) {
                        return $order->created_at->format('Y-m-d') === $day->format('Y-m-d');
                    })->sum('grand_total');
                    
                    $dataPoints[] = [
                        'date' => $day->format('Y-m-d'),
                        'value' => round($daySales, 2)
                    ];
                }
            } elseif ($groupBy === 'month') {
                // Monthly data for year period
                for ($i = 0; $i < $points; $i++) {
                    $month = Carbon::now()->subMonths($points - 1 - $i);
                    $monthSales = $orders->filter(function($order) use ($month) {
                        return $order->created_at->format('Y-m') === $month->format('Y-m');
                    })->sum('grand_total');
                    
                    $dataPoints[] = [
                        'date' => $month->format('Y-m'),
                        'value' => round($monthSales, 2)
                    ];
                }
            }
            
            return $this->success($dataPoints);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch sales by period: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product analytics
     */
    public function getProductAnalytics(Request $request)
    {
        try {
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Get date range
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays(30);
            
            // Get products
            $productsQuery = Product::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('user_id', $user->id);
                }
            });
            
            $totalProducts = $productsQuery->count();
            $activeProducts = $productsQuery->where('published', 1)->count();
            $outOfStockProducts = $productsQuery->where('current_stock', 0)->count();
            $lowStockProducts = $productsQuery->where('current_stock', '>', 0)
                ->where('current_stock', '<=', 5)
                ->count();
                
            // Products by category
            $productsByCategory = Product::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('user_id', $user->id);
                }
            })
            ->join('categories', 'products.category_id', '=', 'categories.id')
            ->select([
                'categories.name as category_name',
                DB::raw('COUNT(products.id) as product_count')
            ])
            ->groupBy('categories.name')
            ->get();
            
            $byCategory = [];
            foreach ($productsByCategory as $category) {
                $byCategory[$category->category_name] = $category->product_count;
            }
            
            // Products by sale status
            $bySaleStatus = [
                'featured' => Product::where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('user_id', $user->id);
                    }
                })->where('featured', 1)->count(),
                'on_sale' => Product::where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('user_id', $user->id);
                    }
                })->where('discount', '>', 0)->count(),
                'new' => Product::where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('user_id', $user->id);
                    }
                })->where('created_at', '>=', Carbon::now()->subDays(7))->count(),
                'out_of_stock' => $outOfStockProducts
            ];
            
            $productAnalytics = [
                'totalProducts' => $totalProducts,
                'activeProducts' => $activeProducts,
                'outOfStockProducts' => $outOfStockProducts,
                'lowStockProducts' => $lowStockProducts,
                'byCategory' => $byCategory,
                'bySaleStatus' => $bySaleStatus
            ];
            
            return $this->success($productAnalytics);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product analytics: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get top products
     */
    public function getTopProducts(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:day,week,month,year',
            'limit' => 'nullable|integer|min:1|max:100',
            'sortBy' => 'nullable|string|in:revenue,units,growth'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            // Get params
            $period = $request->input('period', 'month');
            $limit = min($request->input('limit', 10), 100);
            $sortBy = $request->input('sortBy', 'revenue');
            
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Determine date range based on period
            $endDate = Carbon::now();
            $startDate = null;
            $previousStartDate = null;
            $previousEndDate = null;
            
            switch ($period) {
                case 'day':
                    $startDate = Carbon::now()->subDay();
                    $previousStartDate = Carbon::now()->subDays(2);
                    $previousEndDate = Carbon::now()->subDay();
                    break;
                case 'week':
                    $startDate = Carbon::now()->subWeek();
                    $previousStartDate = Carbon::now()->subWeeks(2);
                    $previousEndDate = Carbon::now()->subWeek();
                    break;
                case 'month':
                    $startDate = Carbon::now()->subMonth();
                    $previousStartDate = Carbon::now()->subMonths(2);
                    $previousEndDate = Carbon::now()->subMonth();
                    break;
                case 'year':
                    $startDate = Carbon::now()->subYear();
                    $previousStartDate = Carbon::now()->subYears(2);
                    $previousEndDate = Carbon::now()->subYear();
                    break;
                default:
                    $startDate = Carbon::now()->subMonth();
                    $previousStartDate = Carbon::now()->subMonths(2);
                    $previousEndDate = Carbon::now()->subMonth();
            }
            
            // Get top products for current period
            $topProducts = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('orders.seller_id', $user->id);
                    }
                })
                ->where('orders.payment_status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->select([
                    'products.id',
                    'products.name',
                    'products.sku',
                    DB::raw('SUM(order_details.quantity) as units'),
                    DB::raw('SUM(order_details.price * order_details.quantity) as revenue')
                ])
                ->groupBy('products.id', 'products.name', 'products.sku')
                ->orderBy($sortBy === 'units' ? 'units' : 'revenue', 'desc')
                ->limit($limit)
                ->get();
                
            // Get previous period data for growth calculation
            $previousPeriodSales = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->where(function($query) use ($user) {
                    if ($user->user_type === 'seller') {
                        $query->where('orders.seller_id', $user->id);
                    }
                })
                ->where('orders.payment_status', 'paid')
                ->whereBetween('orders.created_at', [$previousStartDate, $previousEndDate])
                ->select([
                    'products.id',
                    DB::raw('SUM(order_details.quantity) as units'),
                    DB::raw('SUM(order_details.price * order_details.quantity) as revenue')
                ])
                ->groupBy('products.id')
                ->get()
                ->keyBy('id');
                
            // Calculate growth and format results
            $formattedTopProducts = $topProducts->map(function($product) use ($previousPeriodSales, $sortBy) {
                $previousRevenue = 0;
                $previousUnits = 0;
                
                if (isset($previousPeriodSales[$product->id])) {
                    $previousRevenue = $previousPeriodSales[$product->id]->revenue;
                    $previousUnits = $previousPeriodSales[$product->id]->units;
                }
                
                $revenueGrowth = $previousRevenue > 0 ? (($product->revenue - $previousRevenue) / $previousRevenue) * 100 : 0;
                $unitsGrowth = $previousUnits > 0 ? (($product->units - $previousUnits) / $previousUnits) * 100 : 0;
                
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'sales' => $product->units,
                    'revenue' => round($product->revenue, 2),
                    'units' => $product->units,
                    'growth' => $sortBy === 'units' ? round($unitsGrowth, 2) : round($revenueGrowth, 2)
                ];
            });
            
            // Re-sort by growth if that's the sort parameter
            if ($sortBy === 'growth') {
                $formattedTopProducts = $formattedTopProducts->sortByDesc('growth')->values();
            }
            
            return $this->success($formattedTopProducts);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch top products: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get customer analytics
     */
    public function getCustomerAnalytics(Request $request)
    {
        try {
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Get date ranges
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays(30);
            $previousStartDate = Carbon::now()->subDays(60);
            $previousEndDate = Carbon::now()->subDays(30);
            
            // Get orders for both periods
            $orders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();
            
            $previousOrders = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->get();
            
            // Get customers who made orders in current period
            $currentCustomerIds = $orders->pluck('user_id')->unique()->filter();
            $previousCustomerIds = $previousOrders->pluck('user_id')->unique()->filter();
            
            // Calculate customer metrics
            $totalCustomers = $currentCustomerIds->count();
            $previousTotalCustomers = $previousCustomerIds->count();
            
            // New customers (not in previous period)
            $newCustomers = $currentCustomerIds->diff($previousCustomerIds)->count();
            
            // Returning customers (in both periods)
            $returningCustomers = $currentCustomerIds->intersect($previousCustomerIds)->count();
            
            // Active customers (made more than one order)
            $activeCustomers = $orders->groupBy('user_id')->filter(function($customerOrders) {
                return $customerOrders->count() > 1;
            })->count();
            
            // Churn rate (customers from previous period who didn't order in current period)
            $churnedCustomers = $previousCustomerIds->diff($currentCustomerIds)->count();
            $churnRate = $previousTotalCustomers > 0 ? ($churnedCustomers / $previousTotalCustomers) * 100 : 0;
            
            // Customer acquisition cost (mock data)
            $marketingSpend = 5000; // Mock marketing spend
            $acquisitionCost = $newCustomers > 0 ? $marketingSpend / $newCustomers : 0;
            
            // Customer lifetime value (mock calculation)
            $averageOrderValue = $orders->where('payment_status', 'paid')->avg('grand_total') ?? 0;
            $averageOrdersPerCustomer = 2.5; // Mock average orders per customer lifetime
            $lifetimeValue = $averageOrderValue * $averageOrdersPerCustomer;
            
            // Customers by region (mock data based on shipping addresses)
            $customersByRegion = [
                'north_america' => intval($totalCustomers * 0.4),
                'europe' => intval($totalCustomers * 0.3),
                'asia' => intval($totalCustomers * 0.2),
                'other' => $totalCustomers - intval($totalCustomers * 0.4) - intval($totalCustomers * 0.3) - intval($totalCustomers * 0.2)
            ];
            
            // Customers by age group (mock data)
            $customersByAge = [
                '18-24' => intval($totalCustomers * 0.15),
                '25-34' => intval($totalCustomers * 0.35),
                '35-44' => intval($totalCustomers * 0.25),
                '45-54' => intval($totalCustomers * 0.15),
                '55+' => $totalCustomers - intval($totalCustomers * 0.15) - intval($totalCustomers * 0.35) - intval($totalCustomers * 0.25) - intval($totalCustomers * 0.15)
            ];
            
            // Customers by gender (mock data)
            $customersByGender = [
                'male' => intval($totalCustomers * 0.45),
                'female' => intval($totalCustomers * 0.48),
                'other' => $totalCustomers - intval($totalCustomers * 0.45) - intval($totalCustomers * 0.48)
            ];
            
            $customerAnalytics = [
                'totalCustomers' => $totalCustomers,
                'activeCustomers' => $activeCustomers,
                'newCustomers' => $newCustomers,
                'returningCustomers' => $returningCustomers,
                'churnRate' => round($churnRate, 2),
                'byRegion' => $customersByRegion,
                'byAge' => $customersByAge,
                'byGender' => $customersByGender,
                'acquisitionCost' => round($acquisitionCost, 2),
                'lifetimeValue' => round($lifetimeValue, 2)
            ];
            
            return $this->success($customerAnalytics);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch customer analytics: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get top customers
     */
    public function getTopCustomers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:month,quarter,year,all',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            // Get params
            $period = $request->input('period', 'month');
            $limit = min($request->input('limit', 10), 100);
            
            // Get current user (seller or admin)
            $user = auth()->user();
            
            // Determine date range based on period
            $endDate = Carbon::now();
            $startDate = null;
            
            switch ($period) {
                case 'month':
                    $startDate = Carbon::now()->subMonth();
                    break;
                case 'quarter':
                    $startDate = Carbon::now()->subMonths(3);
                    break;
                case 'year':
                    $startDate = Carbon::now()->subYear();
                    break;
                case 'all':
                    $startDate = Carbon::createFromTimestamp(0); // Beginning of time
                    break;
                default:
                    $startDate = Carbon::now()->subMonth();
            }
            
            // Get orders for the period
            $ordersQuery = Order::where(function($query) use ($user) {
                if ($user->user_type === 'seller') {
                    $query->where('seller_id', $user->id);
                }
            })
            ->where('payment_status', 'paid');
            
            if ($period !== 'all') {
                $ordersQuery->whereBetween('created_at', [$startDate, $endDate]);
            }
            
            $orders = $ordersQuery->get();
            
            // Group orders by customer
            $customerOrders = $orders->groupBy('user_id');
            
            // Calculate metrics for each customer
            $customersData = [];
            
            foreach ($customerOrders as $customerId => $customerOrders) {
                if (!$customerId) continue; // Skip null user_id (guest orders)
                
                $customer = User::find($customerId);
                if (!$customer) continue;
                
                $totalSpent = $customerOrders->sum('grand_total');
                $orderCount = $customerOrders->count();
                $lastPurchase = $customerOrders->sortByDesc('created_at')->first();
                $averageOrderValue = $orderCount > 0 ? $totalSpent / $orderCount : 0;
                
                $customersData[] = [
                    'id' => $customerId,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'totalSpent' => round($totalSpent, 2),
                    'orderCount' => $orderCount,
                    'lastPurchaseDate' => $lastPurchase->created_at->toIso8601String(),
                    'averageOrderValue' => round($averageOrderValue, 2)
                ];
            }
            
            // Sort by total spent and limit
            $topCustomers = collect($customersData)
                ->sortByDesc('totalSpent')
                ->take($limit)
                ->values()
                ->all();
            
            return $this->success($topCustomers);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch top customers: ' . $e->getMessage(),
                500
            );
        }
    }
} 