<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\ResourceCollection;

class CompareProductCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection->map(function($item) {
                return new CompareProductResource($item);
            }),
            'count' => $this->collection->count()
        ];
    }
}
