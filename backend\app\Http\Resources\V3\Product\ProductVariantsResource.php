<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductVariantsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($item) {
            return [
                'id' => $item->variant,
                'sku' => $item->sku ,
                'upc' => $item->upc ,
                'price' => $item->price ,
                'dropshipper_price' => $item->dropshipper_price ,
                'attributes' => $item->attributes ,
                'image' => $item->image ?? uploaded_asset($item->image) ,
            ];
        });
    }
}
