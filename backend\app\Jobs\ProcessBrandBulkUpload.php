<?php

namespace App\Jobs;

use App\Imports\BrandBulkImport;
use App\Models\ImportStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Facades\Excel;

class ProcessBrandBulkUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $filePath;
    protected $userId;
    protected $importStatus;
    protected $file;
    /**
     * Create a new job instance.
     *
     * @param string $filePath
     * @param int $userId
     * @return void
     */
    public function __construct(ImportStatus $importStatus,$userId, $filePath )
    {
        $this->filePath = $filePath;
        $this->userId = $userId;
        $this->importStatus = $importStatus;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {

            // Instantiate the import service
            $importService = new BrandBulkImport($this->importStatus, $this->userId);

            // Perform the import
            Excel::import($importService, $this->filePath);

            // Update status to completed
            $this->importStatus->update([
                'status' => 'completed',
            ]);
            flash('Brands stored successfully completed.')->success();
        } catch (\Exception $e) {
            // Handle any exceptions during import
            $this->importStatus->update([
                'status' => 'failed',
            ]);
            flash('Brands stored failed.')->error();
            // Log the error
            \Log::error('Brands import failed: ' . $e->getMessage());
        }
    }
}