<?php

namespace App\Http\Resources\V3\Discount;

use Illuminate\Http\Resources\Json\JsonResource;

class DiscountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type,
            'value' => (float) $this->value,
            'min_purchase_amount' => isset($this->min_purchase_amount) ? (float) $this->min_purchase_amount : null,
            'max_discount_amount' => isset($this->max_discount_amount) ? (float) $this->max_discount_amount : null,
            'usage_limit' => $this->usage_limit,
            'usage_count' => $this->usage_count,
            'per_customer_limit' => $this->per_customer_limit,
            'start_date' => $this->start_date->toIso8601String(),
            'end_date' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'is_active' => (bool) $this->is_active,
            'is_featured' => (bool) $this->is_featured,
            'priority' => $this->priority,
            'categories' => $this->categories,
            'products' => $this->products,
            'excluded_products' => $this->excluded_products,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 