<?php

namespace App\Events\Messaging;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UnreadCountUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The user ID.
     *
     * @var int
     */
    protected $userId;

    /**
     * The unread count.
     *
     * @var int
     */
    public $unreadCount;

    /**
     * Create a new event instance.
     *
     * @param  int  $userId
     * @return void
     */
    public function __construct($userId)
    {
        $this->userId = $userId;
        $this->unreadCount = $this->calculateUnreadCount($userId);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('user.' . $this->userId . '.conversations');
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'unread.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'unread_count' => $this->unreadCount,
        ];
    }

    /**
     * Calculate the unread messages count for a user.
     *
     * @param  int  $userId
     * @return int
     */
    private function calculateUnreadCount($userId)
    {
        // Count unread messages in conversations
        return DB::table('conversation_messages')
            ->join('conversations', function($join) use ($userId) {
                $join->on('conversation_messages.conversation_id', '=', 'conversations.id')
                     ->where(function($q) use ($userId) {
                         $q->where('conversations.user1_id', '=', $userId)
                           ->orWhere('conversations.user2_id', '=', $userId);
                     });
            })
            ->where('conversation_messages.sender_id', '!=', $userId)
            ->whereDoesntHave('readStatus', function($q) use ($userId) {
                $q->where('user_id', $userId);
            })
            ->count();
    }
} 