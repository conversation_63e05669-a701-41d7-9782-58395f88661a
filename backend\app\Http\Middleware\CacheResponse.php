<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class CacheResponse
{
    /**
     * Cache duration in minutes
     */
    private const CACHE_DURATION = 5;

    /**
     * Routes that should be cached
     */
    private const CACHEABLE_ROUTES = [
        'api/v3/products',
        'api/v3/categories',
        'api/v3/brands',
        'api/v3/homepage',
        'api/v3/featured',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only cache GET requests
        if (!$request->isMethod('GET')) {
            return $next($request);
        }

        // Check if route should be cached
        $shouldCache = $this->shouldCacheRoute($request->path());
        if (!$shouldCache) {
            return $next($request);
        }

        // Generate cache key
        $cacheKey = $this->generateCacheKey($request);

        // Return cached response if exists
        if (Cache::has($cacheKey)) {
            return response()->json(
                Cache::get($cacheKey),
                200,
                ['X-Cache' => 'HIT']
            );
        }

        // Get response and cache it
        $response = $next($request);
        $content = $response->getContent();

        try {
            $data = json_decode($content, true);
            Cache::put($cacheKey, $data, now()->addMinutes(self::CACHE_DURATION));
            $response->header('X-Cache', 'MISS');
        } catch (\Exception $e) {
            \Log::error('Cache middleware error: ' . $e->getMessage());
        }

        return $response;
    }

    /**
     * Check if route should be cached
     */
    private function shouldCacheRoute(string $path): bool
    {
        foreach (self::CACHEABLE_ROUTES as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Generate unique cache key for request
     */
    private function generateCacheKey(Request $request): string
    {
        return md5($request->fullUrl() . '|' . json_encode($request->all()));
    }
} 