{"__meta": {"id": "01K113VQN8BSSFGEWS2J24WXES", "datetime": "2025-07-25 08:40:07", "utime": **********.720773, "method": "GET", "uri": "/buzfi-new-backend/api/v3/cart", "ip": "::1"}, "messages": {"count": 11, "messages": [{"message": "[08:40:07] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/cart\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.605811, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.606347, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.debug: getTempUserId: Found temp user ID in header {\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.606532, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Cart index - User identification {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.606608, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Getting cart {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.606675, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Getting or creating cart info {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.606725, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Found existing cart info {\n    \"cart_info_id\": \"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.626289, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Found guest user cart items {\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"items_count\": 0,\n    \"items\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.628323, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Getting saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.713906, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Found saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.715721, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Cart retrieved successfully {\n    \"cart_info_id\": \"ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"items_count\": 0,\n    \"saved_items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.715804, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.430666, "end": **********.720793, "duration": 0.2901270389556885, "duration_str": "290ms", "measures": [{"label": "Booting", "start": **********.430666, "relative_start": 0, "end": **********.58711, "relative_end": **********.58711, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.587119, "relative_start": 0.*****************, "end": **********.720794, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.593422, "relative_start": 0.*****************, "end": **********.595755, "relative_end": **********.595755, "duration": 0.0023331642150878906, "duration_str": "2.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.717885, "relative_start": 0.****************, "end": **********.718078, "relative_end": **********.718078, "duration": 0.00019288063049316406, "duration_str": "193μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.719222, "relative_start": 0.****************, "end": **********.719277, "relative_end": **********.719277, "duration": 5.4836273193359375e-05, "duration_str": "55μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01682, "accumulated_duration_str": "16.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `cart_info` where `temp_user_id` = 'temp_1753457987293_cr35y75mk' and `status` = 'active' and `user_id` is null limit 1", "type": "query", "params": [], "bindings": ["temp_1753457987293_cr35y75mk", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, {"index": 17, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.608992, "duration": 0.01328, "duration_str": "13.28ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:125", "source": {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=125", "ajax": false, "filename": "EnhancedCartService.php", "line": "125"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 78.954}, {"sql": "update `cart_info` set `last_activity_at` = '2025-07-25 08:40:07', `cart_info`.`updated_at` = '2025-07-25 08:40:07' where `id` = 'ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786'", "type": "query", "params": [], "bindings": ["2025-07-25 08:40:07", "2025-07-25 08:40:07", "ec10d94f-f4dc-4d0c-9c6c-4dc7429ae786"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 152}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6237729, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:152", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=152", "ajax": false, "filename": "EnhancedCartService.php", "line": "152"}, "connection": "buzfi", "explain": null, "start_percent": 78.954, "width_percent": 10.583}, {"sql": "select * from `carts` where `temp_user_id` = 'temp_1753457987293_cr35y75mk' and `status` = 'active' and `user_id` is null", "type": "query", "params": [], "bindings": ["temp_1753457987293_cr35y75mk", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.626699, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:207", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=207", "ajax": false, "filename": "EnhancedCartService.php", "line": "207"}, "connection": "buzfi", "explain": null, "start_percent": 89.536, "width_percent": 6.124}, {"sql": "select * from `saved_items` where `temp_user_id` = 'temp_1753457987293_cr35y75mk' and `user_id` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["temp_1753457987293_cr35y75mk"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 223}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.714387, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:864", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=864", "ajax": false, "filename": "EnhancedCartService.php", "line": "864"}, "connection": "buzfi", "explain": null, "start_percent": 95.66, "width_percent": 4.34}]}, "models": {"data": {"App\\Models\\CartInfo": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCartInfo.php&line=1", "ajax": false, "filename": "CartInfo.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index", "uri": "GET api/v3/cart", "controller": "App\\Http\\Controllers\\Api\\V3\\CartController@index<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/CartController.php:28-68</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "290ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1117497247 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1117497247\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1106217621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1106217621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1320590253 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-temp-user-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457987293_cr35y75mk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"526 characters\">__stripe_mid=59bbba41-0bc6-4270-beba-c08eceb34850d377ea; __stripe_sid=2c04ab5e-db33-44eb-b794-7ff52d70f8f186a81f; XSRF-TOKEN=eyJpdiI6ImJuQ01QMHljN0dzVFdISWtjdGZLeUE9PSIsInZhbHVlIjoiMUxyaURxTVZkUGJVWW94RC9ubFRJUHZ6QmVzZHRNcTFVTUphdmdBSm1rUjBKSmxYT2gvZnZoK045NmlxamU3RWxRNEtsN1k4QTE4MDRMTWR3NjMveVJ3czFUYWdTelZ1NVpBeDRyZC9hOTlQNllFVTRYV2UvQzQyZ245K3ozQ0YiLCJtYWMiOiIwNzRmYjg2YjQ1Y2EyZWYxMTc2MWEwMTJkZDJlMzE4OWIyODJjOWUyMmFlYmY0ODMxYTc0YzRmMzI5NDg2MTUyIiwidGFnIjoiIn0%3D; buzficom_session=KZvX4CQI7W4zzgXNlar0fU6uiwrF5EyYt2q45meh</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320590253\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-878907927 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878907927\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-171680470 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:40:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">577</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikw4ZEsxMkhxTlZaQmQ2ZU90T1BRZmc9PSIsInZhbHVlIjoiQ0dZU29yaHVZYTdNRVh1Smh1cnVmaTFCelpIandDR1RoM0J2VU1rcDN0RWJldlZEdGFtT2JNaXRHMWVNZ3lHRnR5d3BMUlFBQ1N0SDYxdEVzK2U0YWFzQVdmd2dMNVphckZLOWZLbWprbUlLM2RkNWlJTFl1U1FtUmxOQXZrTWsiLCJtYWMiOiI2MDUyZDVmMjM1NGEzOWIyN2FhNDUzOTNiYzQ4NjkxYjVmNDBkOTQ0NjI0NmRkNjJhZWQ1YWRkZWRhMzc0ODkzIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IlB2d0FZTDNaYXFmQmFVaWY1dlVrVEE9PSIsInZhbHVlIjoiRWIweERMNkNxdHJ1ZjJNL1FuNWRISUZoUm84V2JwQ25pQzRRTU11MnJ2Z2o1Y3dZcVVkZldkaFVOWVdCYkdMSVJtSHZjUzNNMDhDaGs0VUlRQVFaRmd2WVdmTnBqeFc1UGl3VXpna1Z4eG5jS2RvMG9YNnB0U2JDanFOYVg1b0MiLCJtYWMiOiI5NGZjODdhYmI0YjMzNGFkNjNiZWQ5MWUxZmZhYjg3ODZhYzVhZmFjNDRlZWI5MWU1MzgxYzc5YTMyYTAwNTNiIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikw4ZEsxMkhxTlZaQmQ2ZU90T1BRZmc9PSIsInZhbHVlIjoiQ0dZU29yaHVZYTdNRVh1Smh1cnVmaTFCelpIandDR1RoM0J2VU1rcDN0RWJldlZEdGFtT2JNaXRHMWVNZ3lHRnR5d3BMUlFBQ1N0SDYxdEVzK2U0YWFzQVdmd2dMNVphckZLOWZLbWprbUlLM2RkNWlJTFl1U1FtUmxOQXZrTWsiLCJtYWMiOiI2MDUyZDVmMjM1NGEzOWIyN2FhNDUzOTNiYzQ4NjkxYjVmNDBkOTQ0NjI0NmRkNjJhZWQ1YWRkZWRhMzc0ODkzIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IlB2d0FZTDNaYXFmQmFVaWY1dlVrVEE9PSIsInZhbHVlIjoiRWIweERMNkNxdHJ1ZjJNL1FuNWRISUZoUm84V2JwQ25pQzRRTU11MnJ2Z2o1Y3dZcVVkZldkaFVOWVdCYkdMSVJtSHZjUzNNMDhDaGs0VUlRQVFaRmd2WVdmTnBqeFc1UGl3VXpna1Z4eG5jS2RvMG9YNnB0U2JDanFOYVg1b0MiLCJtYWMiOiI5NGZjODdhYmI0YjMzNGFkNjNiZWQ5MWUxZmZhYjg3ODZhYzVhZmFjNDRlZWI5MWU1MzgxYzc5YTMyYTAwNTNiIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171680470\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1787318400 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/buzfi-new-backend/api/v3/cart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>temp_user_id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457987293_cr35y75mk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787318400\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index"}, "badge": null}}