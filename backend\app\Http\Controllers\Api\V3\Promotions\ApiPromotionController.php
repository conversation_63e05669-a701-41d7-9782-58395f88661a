<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Coupon\CouponResource;
use App\Models\CartInfo;
use App\Models\Coupon;
use App\Models\Cart;
use App\Services\CouponService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiPromotionController extends ApiResponse
{
    protected $couponService;

    public function __construct(CouponService $couponService)
    {
        $this->couponService = $couponService;
    }

    /**
     * Get list of coupons with optional filtering
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCoupons(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'active' => 'boolean',
                'search' => 'string|max:100',
                'type' => 'string|in:percentage,fixed_amount,free_shipping,buy_x_get_y',
                'limit' => 'integer|min:1|max:50',
                'page' => 'integer|min:1'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 15);
            $page = $request->input('page', 1);
            $active = $request->input('active');
            $search = $request->input('search');
            $type = $request->input('type');

            $query = Coupon::query();

            if ($active !== null) {
                $query->where('is_active', $active);
            }

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('code', 'like', "%{$search}%")
                      ->orWhere('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            if ($type) {
                $query->where('discount_type', $type);
            }

            $coupons = $query->paginate($limit, ['*'], 'page', $page);

            return $this->success([
                'coupons' => CouponResource::collection($coupons),
                'pagination' => [
                    'total' => $coupons->total(),
                    'per_page' => $coupons->perPage(),
                    'current_page' => $coupons->currentPage(),
                    'last_page' => $coupons->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get coupon by code
     *
     * @param string $code
     * @return JsonResponse
     */
    public function getCouponByCode(string $code): JsonResponse
    {
        try {
            $coupon = Coupon::where('code', $code)->first();

            if (!$coupon) {
                return $this->error('Coupon not found', 'The coupon with provided code does not exist', 404);
            }

            return $this->success(new CouponResource($coupon));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Apply coupon to cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyCoupon(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|max:50',
                'cart_id' => 'string'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $code = $request->input('code');
            $cartId = $request->input('cart_id');
            $userId = Auth::id();

            // Get the cart
            $cart = null;
            if ($userId) {
                $cart = Cart::where('user_id', $userId)->first();
            } elseif ($cartId) {
                $cart = Cart::where('id', $cartId)->orWhere('temp_user_id', $cartId)->first();
            }

            if (!$cart) {
                return $this->error('Cart not found', 'Unable to find shopping cart', 404);
            }

            // Apply coupon
            $result = $this->couponService->applyCoupon($code, $cart);

            if (!$result['success']) {
                return $this->error('Coupon invalid', $result['message'], 400);
            }

            return $this->success([
                'message' => $result['message'],
                'discount_amount' => $result['discount_amount'],
                'coupon' => new CouponResource($result['coupon'])
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Remove coupon from cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function removeCoupon(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'cart_id' => 'string'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $cartId = $request->input('cart_id');
            $userId = Auth::id();

            // Get the cart
            $cart = null;
            if ($userId) {
                $cart = Cart::where('user_id', $userId)->first();
            } elseif ($cartId) {
                $cart = Cart::where('id', $cartId)->orWhere('temp_user_id', $cartId)->first();
            }

            if (!$cart) {
                return $this->error('Cart not found', 'Unable to find shopping cart', 404);
            }

            // Remove coupon
            $result = $this->couponService->removeCoupon($cart);

            if (!$result['success']) {
                return $this->error('Remove failed', $result['message'], 400);
            }

            return $this->success(['message' => $result['message']]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Validate a coupon code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateCoupon(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|max:50',
                'subtotal' => 'float|required|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            $user_id = auth()->user()->id;
            $cartInfo = CartInfo::where('user_id', $user_id)->first();
            if (empty($cartInfo)) {
                return $this->validation_error(
                    400,
                    'Please add product to cart',
                    [],
                    400
                );
            }
            $code = $request->input('code');
            $cartId = $cartInfo->id;
            $cartAmount = $request->input('subtotal');
            $userId = Auth::id();

            // Get the cart if cart_id is provided
            $cart = null;
            $cart = Cart::where('user_id', $userId)->first();


            // Validate coupon
            $result = $this->couponService->validateCoupon($code, $cart, $cartAmount);

            if (!$result['valid']) {
                return $this->success([
                    'valid' => false,
                    'message' => $result['message']
                ]);
            }

            return $this->success([
                'valid' => true,
                'message' => $result['message'],
                'discount_amount' => $result['discount_amount'] ?? 0,
                'coupon' => new CouponResource($result['coupon'])
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
}
