<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Schema\Blueprint;
use App\Models\Cart;
use App\Models\CartInfo;

class FixCartTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cart:fix-schema {--force : Run without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix cart tables schema issues and data integrity';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('force')) {
            if (!$this->confirm('This will attempt to fix cart tables. Make sure you have a backup first. Continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('Starting cart tables fix...');
        
        $this->fixCartInfoTable();
        $this->fixCartItemsTable();
        $this->establishRelationships();
        $this->fixCartData();
        
        $this->info('Cart tables fix completed successfully!');
        return 0;
    }

    /**
     * Fix cart_info table structure
     */
    protected function fixCartInfoTable()
    {
        $this->info('Checking cart_info table...');
        
        if (!Schema::hasTable('cart_info')) {
            $this->warn('cart_info table does not exist. Creating it...');
            
            Schema::create('cart_info', function (Blueprint $table) {
                $table->uuid('id')->primary();
                $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
                $table->foreignId('address_id')->nullable();
                $table->integer('item_count')->default(0);
                $table->integer('total_quantity')->default(0);
                $table->decimal('subtotal', 10, 2)->default(0);
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('coupon_code')->nullable();
                $table->decimal('shipping_total', 10, 2)->default(0);
                $table->decimal('tax_total', 10, 2)->default(0);
                $table->decimal('total', 10, 2)->default(0);
                $table->string('currency')->default('USD');
                $table->decimal('wallet_amount_used', 10, 2)->default(0);
                $table->integer('reward_points_used')->default(0);
                $table->decimal('reward_points_discount', 10, 2)->default(0);
                $table->timestamps();
            });
            
            $this->info('Created cart_info table.');
        } else {
            $this->info('cart_info table exists. Checking structure...');
            
            Schema::table('cart_info', function (Blueprint $table) {
                // Remove cart_info_id if it exists (should only be in cart_items)
                if (Schema::hasColumn('cart_info', 'cart_info_id')) {
                    $this->warn('Removing invalid cart_info_id column from cart_info table');
                    $table->dropColumn('cart_info_id');
                }
                
                // Add any missing essential columns
                if (!Schema::hasColumn('cart_info', 'total')) {
                    $this->info('Adding missing total column to cart_info table');
                    $table->decimal('total', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'item_count')) {
                    $this->info('Adding missing item_count column to cart_info table');
                    $table->integer('item_count')->default(0);
                }
                
                if (!Schema::hasColumn('cart_info', 'total_quantity')) {
                    $this->info('Adding missing total_quantity column to cart_info table');
                    $table->integer('total_quantity')->default(0);
                }
            });
        }
    }

    /**
     * Fix cart_items table structure
     */
    protected function fixCartItemsTable()
    {
        $this->info('Checking cart_items table...');
        
        if (!Schema::hasTable('cart_items')) {
            $this->warn('cart_items table does not exist. Creating it...');
            
            Schema::create('cart_items', function (Blueprint $table) {
                $table->id();
                $table->uuid('cart_info_id')->nullable()->index();
                $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
                $table->string('temp_user_id')->nullable();
                $table->foreignId('product_id')->nullable();
                $table->foreignId('owner_id')->nullable();
                $table->text('variation')->nullable();
                $table->decimal('price', 10, 2)->default(0);
                $table->decimal('tax', 10, 2)->default(0);
                $table->decimal('shipping_cost', 10, 2)->default(0);
                $table->string('shipping_type')->nullable();
                $table->integer('pickup_point')->nullable();
                $table->integer('carrier_id')->nullable();
                $table->decimal('discount', 10, 2)->default(0);
                $table->string('coupon_code')->nullable();
                $table->boolean('coupon_applied')->default(false);
                $table->integer('quantity')->default(1);
                $table->timestamps();
            });
            
            $this->info('Created cart_items table.');
        } else {
            $this->info('cart_items table exists. Checking structure...');
            
            Schema::table('cart_items', function (Blueprint $table) {
                // Add any missing essential columns
                if (!Schema::hasColumn('cart_items', 'cart_info_id')) {
                    $this->info('Adding missing cart_info_id column to cart_items table');
                    $table->uuid('cart_info_id')->nullable()->index();
                }
                
                if (!Schema::hasColumn('cart_items', 'price')) {
                    $this->info('Adding missing price column to cart_items table');
                    $table->decimal('price', 10, 2)->default(0);
                }
                
                if (!Schema::hasColumn('cart_items', 'quantity')) {
                    $this->info('Adding missing quantity column to cart_items table');
                    $table->integer('quantity')->default(1);
                }
            });
        }
    }

    /**
     * Establish relationships between tables
     */
    protected function establishRelationships()
    {
        $this->info('Establishing relationships between cart tables...');
        
        try {
            if (Schema::hasTable('cart_items') && Schema::hasTable('cart_info')) {
                // Check if the foreign key constraint exists
                $sm = Schema::getConnection()->getDoctrineSchemaManager();
                $foreignKeys = $sm->listTableForeignKeys('cart_items');
                $hasConstraint = false;
                
                foreach ($foreignKeys as $foreignKey) {
                    if (in_array('cart_info_id', $foreignKey->getLocalColumns())) {
                        $hasConstraint = true;
                        break;
                    }
                }
                
                if (!$hasConstraint) {
                    Schema::table('cart_items', function (Blueprint $table) {
                        $this->info('Adding foreign key relationship between cart_items and cart_info');
                        // Add foreign key relationship to cart_info
                        $table->foreign('cart_info_id')
                            ->references('id')
                            ->on('cart_info')
                            ->onDelete('cascade');
                    });
                }
            }
        } catch (\Exception $e) {
            $this->error('Error establishing relationships: ' . $e->getMessage());
            $this->warn('Continuing with the rest of the fixes...');
        }
    }

    /**
     * Fix cart data integrity issues
     */
    protected function fixCartData()
    {
        $this->info('Fixing cart data integrity...');
        
        // Recalculate totals for all cart_info records
        $cartInfos = CartInfo::all();
        $count = 0;
        
        foreach ($cartInfos as $cartInfo) {
            try {
                // Get all cart items for this cart
                $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                
                // Calculate totals
                $itemCount = $cartItems->count();
                $totalQuantity = $cartItems->sum('quantity');
                $subtotal = $cartItems->sum(function ($item) {
                    return $item->price * $item->quantity;
                });
                $tax = $cartItems->sum(function ($item) {
                    return ($item->tax ?? 0) * $item->quantity;
                });
                $shippingTotal = $cartItems->sum(function ($item) {
                    return $item->shipping_cost ?? 0;
                });
                
                // Calculate total including tax and shipping, minus discounts
                $total = $subtotal + $tax + $shippingTotal - ($cartInfo->discount ?? 0);
                
                // Update cart info
                $cartInfo->item_count = $itemCount;
                $cartInfo->total_quantity = $totalQuantity;
                $cartInfo->subtotal = $subtotal;
                $cartInfo->tax_total = $tax;
                $cartInfo->shipping_total = $shippingTotal;
                $cartInfo->total = $total;
                $cartInfo->save();
                
                $count++;
            } catch (\Exception $e) {
                $this->error('Error updating cart totals for cart ' . $cartInfo->id . ': ' . $e->getMessage());
            }
        }
        
        $this->info("Updated totals for {$count} cart records.");
        
        // Remove orphaned cart items (where cart_info doesn't exist)
        try {
            $orphanedItems = Cart::whereNotIn('cart_info_id', function ($query) {
                $query->select('id')->from('cart_info');
            })->count();
            
            if ($orphanedItems > 0) {
                $this->warn("Found {$orphanedItems} orphaned cart items. Cleaning up...");
                
                Cart::whereNotIn('cart_info_id', function ($query) {
                    $query->select('id')->from('cart_info');
                })->delete();
                
                $this->info("Deleted {$orphanedItems} orphaned cart items.");
            } else {
                $this->info("No orphaned cart items found.");
            }
        } catch (\Exception $e) {
            $this->error('Error cleaning up orphaned cart items: ' . $e->getMessage());
        }
    }
} 