<?php

namespace App\Http\Controllers\Api\V3\Marketing;

use App\Http\Controllers\Controller;
use App\Models\Video;
use App\Models\VideoCategory;
use App\Models\VideoView;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Api\V3\ApiResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class ApiVideoController extends Controller
{
    /**
     * Get featured videos with pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeaturedVideos(Request $request)
    {
        try {
            // Parse parameters from the request
            $params = json_decode($request->get('params', '{}'), true);
            if (!is_array($params)) {
                $params = [];
            }

            // Default values and pagination
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 24;
            $category = $params['category'] ?? null;
            $sortBy = $params['sortBy'] ?? 'popular';

            // Query builder for videos
            $query = Video::where('status', 'active')
                          ->where('is_featured', 1);
            
            // Filter by category if provided
            if ($category) {
                $query->where('category', $category);
            }
            
            // Apply sorting
            switch ($sortBy) {
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'popular':
                default:
                    $query->orderBy('views', 'desc');
                    break;
            }
            
            // Execute query with pagination
            $videos = $query->paginate($limit);
            
            // Format response
            $response = [
                'videos' => $videos->items(),
                'pagination' => [
                    'currentPage' => $videos->currentPage(),
                    'totalPages' => $videos->lastPage(),
                    'totalItems' => $videos->total(),
                    'itemsPerPage' => $videos->perPage(),
                ],
            ];
            
            $apiResponse = new ApiResponse();
            return $apiResponse->success($response);
        } catch (\Exception $e) {
            $apiResponse = new ApiResponse();
            return $apiResponse->error('API_ERROR', 'Failed to fetch featured videos', $e->getMessage());
        }
    }

    /**
     * Get video categories
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        try {
            $categories = VideoCategory::where('status', 'active')
                                      ->orderBy('name', 'asc')
                                      ->pluck('name')
                                      ->toArray();
            
            $apiResponse = new ApiResponse();
            return $apiResponse->success($categories);
        } catch (\Exception $e) {
            $apiResponse = new ApiResponse();
            return $apiResponse->error('API_ERROR', 'Failed to fetch video categories', $e->getMessage());
        }
    }

    /**
     * Get videos by category
     *
     * @param Request $request
     * @param string $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVideosByCategory(Request $request, $category)
    {
        try {
            // Parse parameters from the request
            $params = json_decode($request->get('params', '{}'), true);
            if (!is_array($params)) {
                $params = [];
            }

            // Default values and pagination
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 24;
            $sortBy = $params['sortBy'] ?? 'popular';

            // Query builder for videos
            $query = Video::where('status', 'active')
                          ->where('category', $category);
            
            // Apply sorting
            switch ($sortBy) {
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'popular':
                default:
                    $query->orderBy('views', 'desc');
                    break;
            }
            
            // Execute query with pagination
            $videos = $query->paginate($limit);
            
            // Format response
            $response = [
                'videos' => $videos->items(),
                'pagination' => [
                    'currentPage' => $videos->currentPage(),
                    'totalPages' => $videos->lastPage(),
                    'totalItems' => $videos->total(),
                    'itemsPerPage' => $videos->perPage(),
                ],
            ];
            
            $apiResponse = new ApiResponse();
            return $apiResponse->success($response);
        } catch (\Exception $e) {
            $apiResponse = new ApiResponse();
            return $apiResponse->error('API_ERROR', 'Failed to fetch videos by category', $e->getMessage());
        }
    }

    /**
     * Get a specific video by ID
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVideo($id)
    {
        try {
            $video = Video::where('id', $id)
                          ->where('status', 'active')
                          ->first();
            
            $apiResponse = new ApiResponse();
            if (!$video) {
                return $apiResponse->error('NOT_FOUND', 'Video not found', null, 404);
            }
            
            // Increment view count
            $video->increment('views');
            
            // Record view
            VideoView::create([
                'video_id' => $video->id,
                'user_id' => auth()->check() ? auth()->id() : null,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
            
            return $apiResponse->success($video);
        } catch (\Exception $e) {
            $apiResponse = new ApiResponse();
            return $apiResponse->error('API_ERROR', 'Failed to fetch video', $e->getMessage());
        }
    }

    /**
     * Search videos
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchVideos(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:2|max:100',
            ]);

            $apiResponse = new ApiResponse();
            if ($validator->fails()) {
                return $apiResponse->validation_error('VALIDATION_ERROR', 'Validation failed', $validator->errors()->toArray(), 422);
            }

            // Parse parameters from the request
            $params = json_decode($request->get('params', '{}'), true);
            if (!is_array($params)) {
                $params = [];
            }

            // Get search parameters
            $query = $request->input('query');
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 24;
            $category = $params['category'] ?? null;
            
            // Query builder for videos
            $videos = Video::where('status', 'active')
                          ->where(function($q) use ($query) {
                              $q->where('title', 'like', "%{$query}%")
                                ->orWhere('description', 'like', "%{$query}%");
                          });
            
            // Filter by category if provided
            if ($category) {
                $videos->where('category', $category);
            }
            
            // Execute query with pagination
            $results = $videos->orderBy('views', 'desc')
                             ->paginate($limit);
            
            // Format response
            $response = [
                'videos' => $results->items(),
                'pagination' => [
                    'currentPage' => $results->currentPage(),
                    'totalPages' => $results->lastPage(),
                    'totalItems' => $results->total(),
                    'itemsPerPage' => $results->perPage(),
                ],
            ];
            
            return $apiResponse->success($response);
        } catch (\Exception $e) {
            $apiResponse = new ApiResponse();
            return $apiResponse->error('SEARCH_ERROR', 'Failed to search videos', $e->getMessage());
        }
    }

    /**
     * Upload a new video
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadVideo(Request $request)
    {
        try {
            // Check if user is authenticated
            if (!auth()->check()) {
                $apiResponse = new ApiResponse();
                return $apiResponse->error('UNAUTHORIZED', 'User must be logged in to upload videos', null, 401);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|min:5|max:100',
                'description' => 'required|string|min:10|max:500',
                'category' => 'required|string|exists:video_categories,name',
                'video' => 'required|file|mimetypes:video/mp4,video/webm,video/quicktime|max:102400', // 100MB
                'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB
            ]);

            if ($validator->fails()) {
                $apiResponse = new ApiResponse();
                return $apiResponse->validation_error('VALIDATION_ERROR', 'Validation failed', $validator->errors()->toArray(), 422);
            }

            // Start database transaction
            DB::beginTransaction();

            // Upload video file
            $videoFile = $request->file('video');
            $videoFileName = 'user_video_' . Str::uuid() . '.' . $videoFile->getClientOriginalExtension();
            $videoPath = $videoFile->storeAs('videos/user_uploads', $videoFileName, 'public');
            
            // Generate video URL
            $videoUrl = Storage::url($videoPath);

            // Calculate video duration if possible (requires ffmpeg)
            $duration = null;
            try {
                if (function_exists('exec')) {
                    $ffprobePath = '/usr/bin/ffprobe'; // Update path as needed
                    $cmd = "$ffprobePath -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 " . storage_path('app/public/' . $videoPath);
                    exec($cmd, $output);
                    if (!empty($output[0])) {
                        $duration = (int) $output[0];
                    }
                }
            } catch (\Exception $e) {
                // Log but continue if duration extraction fails
                \Log::warning('Video duration extraction failed: ' . $e->getMessage());
            }

            // Handle thumbnail
            $thumbnailUrl = null;
            if ($request->hasFile('thumbnail')) {
                // User provided a thumbnail
                $thumbnailFile = $request->file('thumbnail');
                $thumbnailFileName = 'thumbnail_' . Str::uuid() . '.' . $thumbnailFile->getClientOriginalExtension();
                
                // Resize the thumbnail
                $image = Image::make($thumbnailFile);
                $image->fit(640, 360); // 16:9 aspect ratio
                
                // Save the resized thumbnail
                $thumbnailPath = 'thumbnails/' . $thumbnailFileName;
                Storage::disk('public')->put($thumbnailPath, (string) $image->encode());
                
                $thumbnailUrl = Storage::url($thumbnailPath);
            } else {
                // Generate thumbnail from video (requires ffmpeg)
                try {
                    if (function_exists('exec')) {
                        $ffmpegPath = '/usr/bin/ffmpeg'; // Update path as needed
                        $thumbnailFileName = 'thumbnail_' . Str::uuid() . '.jpg';
                        $thumbnailPath = storage_path('app/public/thumbnails/' . $thumbnailFileName);
                        
                        // Ensure directory exists
                        if (!file_exists(dirname($thumbnailPath))) {
                            mkdir(dirname($thumbnailPath), 0755, true);
                        }
                        
                        // Extract frame at 1 second mark
                        $cmd = "$ffmpegPath -i " . storage_path('app/public/' . $videoPath) . " -ss 00:00:01 -vframes 1 $thumbnailPath";
                        exec($cmd);
                        
                        if (file_exists($thumbnailPath)) {
                            $thumbnailUrl = Storage::url('thumbnails/' . $thumbnailFileName);
                        }
                    }
                } catch (\Exception $e) {
                    // Log but continue if thumbnail generation fails
                    \Log::warning('Thumbnail generation failed: ' . $e->getMessage());
                }
            }

            // Create new video record
            $video = Video::create([
                'title' => $request->title,
                'description' => $request->description,
                'category' => $request->category,
                'video' => $videoUrl,
                'thumbnail' => $thumbnailUrl,
                'duration' => $duration,
                'views' => 0,
                'status' => 'pending', // Requires approval
                'is_featured' => false,
                'created_by' => auth()->id(),
            ]);

            // Commit transaction
            DB::commit();

            $apiResponse = new ApiResponse();
            return $apiResponse->success([
                'message' => 'Video uploaded successfully and pending approval',
                'video' => $video
            ]);
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            $apiResponse = new ApiResponse();
            return $apiResponse->error('UPLOAD_FAILED', 'Failed to upload video', $e->getMessage());
        }
    }
} 