{"__meta": {"id": "01K113VQXF7ZD9VR7QN0C10WRT", "datetime": "2025-07-25 08:40:07", "utime": **********.983637, "method": "GET", "uri": "/buzfi-new-backend/api/v3/cart/saved-items", "ip": "::1"}, "messages": {"count": 5, "messages": [{"message": "[08:40:07] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/cart\\/saved-items\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.962287, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.962754, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.debug: getTempUserId: Found temp user ID in header {\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.962874, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Getting saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.962933, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:07] LOG.info: Found saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457987293_cr35y75mk\",\n    \"items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.979413, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.791666, "end": **********.983651, "duration": 0.1919848918914795, "duration_str": "192ms", "measures": [{"label": "Booting", "start": **********.791666, "relative_start": 0, "end": **********.944914, "relative_end": **********.944914, "duration": 0.*****************, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.944921, "relative_start": 0.*****************, "end": **********.983652, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "38.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.950555, "relative_start": 0.****************, "end": **********.952877, "relative_end": **********.952877, "duration": 0.002321958541870117, "duration_str": "2.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.981205, "relative_start": 0.*****************, "end": **********.98139, "relative_end": **********.98139, "duration": 0.0001850128173828125, "duration_str": "185μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.98248, "relative_start": 0.*****************, "end": **********.982518, "relative_end": **********.982518, "duration": 3.790855407714844e-05, "duration_str": "38μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013099999999999999, "accumulated_duration_str": "13.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `saved_items` where `temp_user_id` = 'temp_1753457987293_cr35y75mk' and `user_id` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["temp_1753457987293_cr35y75mk"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 420}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9651182, "duration": 0.013099999999999999, "duration_str": "13.1ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:864", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=864", "ajax": false, "filename": "EnhancedCartService.php", "line": "864"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart/saved-items", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@savedItems", "uri": "GET api/v3/cart/saved-items", "controller": "App\\Http\\Controllers\\Api\\V3\\CartController@savedItems<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=414\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=414\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/CartController.php:414-439</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "192ms", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1314637289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1314637289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2012033216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012033216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1642184367 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-temp-user-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457987293_cr35y75mk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"526 characters\">__stripe_mid=59bbba41-0bc6-4270-beba-c08eceb34850d377ea; __stripe_sid=2c04ab5e-db33-44eb-b794-7ff52d70f8f186a81f; XSRF-TOKEN=eyJpdiI6Ikw4ZEsxMkhxTlZaQmQ2ZU90T1BRZmc9PSIsInZhbHVlIjoiQ0dZU29yaHVZYTdNRVh1Smh1cnVmaTFCelpIandDR1RoM0J2VU1rcDN0RWJldlZEdGFtT2JNaXRHMWVNZ3lHRnR5d3BMUlFBQ1N0SDYxdEVzK2U0YWFzQVdmd2dMNVphckZLOWZLbWprbUlLM2RkNWlJTFl1U1FtUmxOQXZrTWsiLCJtYWMiOiI2MDUyZDVmMjM1NGEzOWIyN2FhNDUzOTNiYzQ4NjkxYjVmNDBkOTQ0NjI0NmRkNjJhZWQ1YWRkZWRhMzc0ODkzIiwidGFnIjoiIn0%3D; buzficom_session=ZNHzaH2St0UpWR5BnIdSWNnI8wgrc49lZDv1M5TD</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642184367\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1941449911 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941449911\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2045833383 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:40:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">576</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjVBL2JZb2xlQ0JjeXRGaXJlb1N4QXc9PSIsInZhbHVlIjoiVHdpVGRtUUlvTGYwakRtTjJNbHNrb0IrUkpwNWoySkk3YTlISi9VbTVXbEg4Rmk2NXd5TDhUMUlZMnRCUFhVK0g4bXhtT0kwRXQ5b0U5R1JEdmtjOHhNQkFkTW10eGpETXd6YXNKTGkwbGtscjFvaWRpOXUvTlhsSUxpU0c0NloiLCJtYWMiOiI3NzdmMjMxMDRkYjg4OTQxOTgzOTYzMDAwN2MyNjNlNmNmYmE5MWMwYzQwNjQ2ZmNjM2Y0MmFmYjc0NDBmNTE1IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6InNtY1d6NDVzSFVjR1NQNVdHb2J0bUE9PSIsInZhbHVlIjoidUxHclJCTEVlcE9aR0ZETUg1ZTJSUjJpNVpzNVh4ZGEvd0NFdFdFOXpLUHF5VVBsVDdPV2Zrdm04OHZ0ckNzZlUyVU54Q2psVDFBMW1tVVlJWXM0SVZiZGR6aU5KemcwQlNFUmg4bFRPRHJpV1F1UW9zZ1lhNnIvaE4rcU91eGUiLCJtYWMiOiJjOWU0MmFlZjI5MjYzMWI1NmI4ZDI4M2Q1ZWEwYzYwZGMwOTJkZWFmNGMxOTJhMjQ4MTI0M2IxYjRhM2M1YTFjIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjVBL2JZb2xlQ0JjeXRGaXJlb1N4QXc9PSIsInZhbHVlIjoiVHdpVGRtUUlvTGYwakRtTjJNbHNrb0IrUkpwNWoySkk3YTlISi9VbTVXbEg4Rmk2NXd5TDhUMUlZMnRCUFhVK0g4bXhtT0kwRXQ5b0U5R1JEdmtjOHhNQkFkTW10eGpETXd6YXNKTGkwbGtscjFvaWRpOXUvTlhsSUxpU0c0NloiLCJtYWMiOiI3NzdmMjMxMDRkYjg4OTQxOTgzOTYzMDAwN2MyNjNlNmNmYmE5MWMwYzQwNjQ2ZmNjM2Y0MmFmYjc0NDBmNTE1IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6InNtY1d6NDVzSFVjR1NQNVdHb2J0bUE9PSIsInZhbHVlIjoidUxHclJCTEVlcE9aR0ZETUg1ZTJSUjJpNVpzNVh4ZGEvd0NFdFdFOXpLUHF5VVBsVDdPV2Zrdm04OHZ0ckNzZlUyVU54Q2psVDFBMW1tVVlJWXM0SVZiZGR6aU5KemcwQlNFUmg4bFRPRHJpV1F1UW9zZ1lhNnIvaE4rcU91eGUiLCJtYWMiOiJjOWU0MmFlZjI5MjYzMWI1NmI4ZDI4M2Q1ZWEwYzYwZGMwOTJkZWFmNGMxOTJhMjQ4MTI0M2IxYjRhM2M1YTFjIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045833383\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1113562342 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"58 characters\">http://localhost/buzfi-new-backend/api/v3/cart/saved-items</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>temp_user_id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457987293_cr35y75mk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113562342\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart/saved-items", "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@savedItems"}, "badge": null}}