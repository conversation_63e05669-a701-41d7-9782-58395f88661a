<?php

namespace App\Http\Resources\V3\Rerurn;

use App\Models\OrderDetail;
use App\Models\Upload;
use Illuminate\Http\Resources\Json\JsonResource;

class RerurnRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->return_code,
            'orderCode' => $this->order->code,
            'orderDate' => date('d-m-Y h:i A', $this->order->date),
            'requestDate' => date('d-m-Y h:i A', $this->date),
            'status' => ucfirst($this->return_status->value),
            'returnMethod' => "",
            'returnCondition' => "",
            'originalPackaging' =>'',
            'description' => $this->user_note,
            'refundAmount' => (float) $this->amount,
            'refundMethod' => "",
            'products' => $this->return_request_products->map(function ($item) {
                $orderDetail = OrderDetail::where('id', $item->order_detail_id)->first(); $detail = OrderDetail::where('id', $item->order_detail_id)->first();
                return [
                    'id' => $item->product->slug,
                    'name' => $item->product->name,
                    'thumbnail' => $item->product->thumbnail_img ? uploaded_asset($item->product->thumbnail_img) : "",
                    'quantity' => (float) $item->quantity,
                    'price' =>(float) $item->unit_price,
                    'reason' => '',
                    'condition' => '',
                    'sku' => (string) product_sku_by_product_id($item->product,$orderDetail->variation) ?? Null,
                ];
            }),
            'attachments'=> $this->attachments ? array_map(function ($image) {
                $upload = Upload::where('id', $image)->first();
                return [
                    'type' => $upload->type,
                    'url' => uploaded_asset($image),
                ]
            }, explode(',', $this->attachments)) : [] ,
           'timeline' =>$this->getReturnRequestTimeline($this->activityLogs)
        ];

    }
    private function getReturnRequestTimeline($activityLogs)
    {
        return $activityLogs->map(function ($log) {
            return [
                'status' => ucfirst($log->new_status),
                'timestamp' => $log->created_at->toIso8601String(),
                'description' => $log->description,

            ];
        });
    }
}
