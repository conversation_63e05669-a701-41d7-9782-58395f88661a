<?php

namespace App\Http\Resources\V3\ShippingMethod;

use Illuminate\Http\Resources\Json\JsonResource;

class ShippingCostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Calculate estimated delivery dates
        $minDate = now()->addDays($this->shippingMethod->min_days)->format('Y-m-d');
        $maxDate = now()->addDays($this->shippingMethod->max_days)->format('Y-m-d');

        return [
            'shippingCost' => (float) $this->cost,
            'totalWeight' => (float) $this->weight,
            'currency' => $this->currency ?? 'USD',
            'estimatedDelivery' => [
                'minDays' => $this->shippingMethod->min_days,
                'maxDays' => $this->shippingMethod->max_days,
                'minDate' => $minDate,
                'maxDate' => $maxDate
            ],
            'methodDetails' => [
                'id' => $this->shippingMethod->id,
                'name' => $this->shippingMethod->name,
                'description' => $this->shippingMethod->description
            ]
        ];
    }
}
