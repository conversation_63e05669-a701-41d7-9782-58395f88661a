<?php

namespace App\Http\Controllers;

use App\Models\ActivityLog;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use Artisan;
use Cache;

class ActivityController extends Controller
{
    public function showActivities()
    {
        $activities = ActivityLog::latest()->paginate(10);
        dd($activities);
        return view('admin.activities.index', compact('activities'));
    }
}
