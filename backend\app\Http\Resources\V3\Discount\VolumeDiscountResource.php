<?php

namespace App\Http\Resources\V3\Discount;

use Illuminate\Http\Resources\Json\JsonResource;

class VolumeDiscountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'tiers' => collect($this->tiers)->map(function($tier) {
                return [
                    'quantity' => (int) $tier['quantity'],
                    'discount_type' => $tier['discount_type'],
                    'discount_value' => (float) $tier['discount_value'],
                    'final_price' => isset($tier['final_price']) ? (float) $tier['final_price'] : null,
                ];
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 