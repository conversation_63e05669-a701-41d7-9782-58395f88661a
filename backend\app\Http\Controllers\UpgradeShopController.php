<?php

namespace App\Http\Controllers;

use App\Http\Requests\SellerRegistrationRequest;
use Illuminate\Http\Request;
use App\Models\Shop;
use App\Models\User;
use App\Models\BusinessSetting;
use Auth;
use Hash;
use App\Notifications\EmailVerificationNotification;
use Illuminate\Support\Facades\Notification;

class UpgradeShopController extends Controller
{
    public function create()
    {
        //dd('abcd');
        if (Auth::check()) {
            if((Auth::user()->user_type == 'customer')) {
                return view('frontend.upgrade_to_seller');
			}
			if((Auth::user()->user_type == 'admin')) {
				flash(translate('Admin or Customer cannot be a seller'))->error();
				return back();
			}if(Auth::user()->user_type == 'seller'){
				flash(translate('This user already a seller'))->error();
				return back();
			}

        } else {
            return view('frontend.upgrade_to_seller');
        }
    }
    public function store(Request $request)
    {
        $cID= auth()->user()->id;
        //$user = Auth::user();
        $user = User::findOrFail($cID);
        //$user->name = $request->name;
        //$user->email = $request->email;
        $user->user_type = "seller";
        //$user->password = Hash::make($request->password);

        if ($user->save()) {
            $shop = new Shop;
            $shop->user_id = $user->id;
            $shop->name = $request->shop_name;
            $shop->address = $request->address;
            $shop->slug = preg_replace('/\s+/', '-', str_replace("/", " ", $request->shop_name));
            $shop->save();

            #auth()->login($user, false);
            #if (BusinessSetting::where('type', 'email_verification')->first()->value == 0) {
            #    $user->email_verified_at = date('Y-m-d H:m:s');
            #    $user->save();
            #} else {
            #   $user->notify(new EmailVerificationNotification());
            #}

            flash(translate('Your Shop has been created successfully!'))->success();
            return redirect()->route('seller.shop.index');
        }

        flash(translate('Sorry! Something went wrong.'))->error();
        return back();
    }
}
