<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\WalletExchangeRate;
use App\Services\WalletValidationService;
use App\Services\WalletTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class WalletController extends Controller
{
    protected $validationService;
    protected $transactionService;

    /**
     * Constructor with dependency injection
     */
    public function __construct(WalletValidationService $validationService, WalletTransactionService $transactionService)
    {
        $this->validationService = $validationService;
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of all wallets.
     */
    public function index(Request $request)
    {
        $sort_search = null;
        $wallets = Wallet::with('user');

        if ($request->has('search')) {
            $sort_search = $request->search;
            $user_ids = User::where('name', 'like', '%' . $sort_search . '%')
                ->orWhere('email', 'like', '%' . $sort_search . '%')
                ->orWhere('phone', 'like', '%' . $sort_search . '%')
                ->pluck('id')
                ->toArray();
            $wallets = $wallets->whereIn('user_id', $user_ids);
        }

        // Allow filtering by status
        if ($request->has('status') && in_array($request->status, ['active', 'inactive'])) {
            $wallets = $wallets->where('status', $request->status);
        }
        
        // Allow sorting
        $sort_by = $request->sort_by ?? 'created_at';
        $sort_order = $request->sort_order ?? 'desc';
        
        if (in_array($sort_by, ['balance', 'reward_points', 'promotional_credits', 'created_at'])) {
            $wallets = $wallets->orderBy($sort_by, $sort_order);
        }

        $wallets = $wallets->paginate(15);
        return view('backend.wallet.index', compact('wallets', 'sort_search'));
    }

    /**
     * Display the specified wallet details.
     */
    public function details($id)
    {
        $wallet = Wallet::with(['user', 'transactions' => function ($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }])->findOrFail($id);
        
        // Get transaction statistics
        $stats = [
            'total_transactions' => WalletTransaction::where('user_id', $wallet->user_id)->count(),
            'total_credits' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('type', 'credit')
                ->sum('amount'),
            'total_debits' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('type', 'debit')
                ->sum('amount'),
            'pending_transactions' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('approval_status', 'pending')
                ->count()
        ];
        
        // Calculate balance breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'refund')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'admin_credits' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'admin')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'rewards' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'reward')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'promotions' => WalletTransaction::where('user_id', $wallet->user_id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'promotion')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount')
        ];
        
        return view('backend.wallet.details', compact('wallet', 'stats', 'balanceBreakdown'));
    }

    /**
     * Display all transactions with filtering options.
     */
    public function transactions(Request $request)
    {
        $query = WalletTransaction::with(['wallet.user', 'addedBy']);
        
        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        
        // Filter by transaction type
        if ($request->has('type') && in_array($request->type, ['credit', 'debit'])) {
            $query->where('type', $request->type);
        }
        
        // Filter by source
        if ($request->has('source') && in_array($request->source, ['order', 'refund', 'admin', 'reward', 'promotion', 'purchase', 'promotional'])) {
            $query->where('reference_type', $request->source);
        }
        
        // Filter by balance type
        if ($request->has('balance_type') && in_array($request->balance_type, ['balance', 'reward_points', 'promotional_credits'])) {
            $query->where('balance_type', $request->balance_type);
        }
        
        // Filter by approval status
        if ($request->has('approval_status') && in_array($request->approval_status, ['pending', 'approved', 'rejected'])) {
            $query->where('approval_status', $request->approval_status);
        }
        
        // Filter by date range
        if ($request->has('date_from') && $request->has('date_to')) {
            $query->whereBetween('created_at', [$request->date_from . ' 00:00:00', $request->date_to . ' 23:59:59']);
        }
        
        $transactions = $query->orderBy('created_at', 'desc')->paginate(15);
        
        return view('backend.wallet.transactions', compact('transactions'));
    }

    /**
     * Show form to add funds to wallet
     */
    public function showAddFunds($id)
    {
        $wallet = Wallet::with('user')->findOrFail($id);
        return view('backend.wallet.add_funds', compact('wallet'));
    }

    /**
     * Add funds to the specified wallet.
     */
    public function addFunds(Request $request, $id)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string',
            'reference_type' => 'nullable|string',
            'reference_id' => 'nullable|string',
        ]);

        $wallet = Wallet::findOrFail($id);
        $admin = Auth::user();

        try {
            DB::beginTransaction();

            // Create transaction record and update wallet using service
            if ($request->type == 'balance') {
                $transaction = $this->transactionService->addFundsToWallet(
                    $wallet->user_id,
                    $request->amount,
                    $request->description,
                    $request->reference_type ?? 'admin',
                    $request->reference_id,
                    false,
                    [
                        'added_by' => $admin->id,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->type == 'reward_points') {
                $transaction = $this->transactionService->addRewardPoints(
                    $wallet->user_id,
                    (int)$request->amount,
                    $request->description,
                    $request->reference_type ?? 'admin',
                    $request->reference_id,
                    null,
                    [
                        'added_by' => $admin->id,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->type == 'promotional_credits') {
                // For promotional credits
                $expiryDate = $request->has('expiry_date') ? $request->expiry_date : null;
                
                $promotionalCredit = $this->transactionService->addPromotionalCredits(
                    $wallet->user_id,
                    $request->amount,
                    $request->description,
                    $expiryDate,
                    $request->usage_limit ?? null,
                    $request->min_purchase_amount ?? null,
                    $request->applicable_categories ?? null
                );
            }

            DB::commit();

            flash(translate('Funds added successfully'))->success();
            return redirect()->route('admin.wallet.details', $id);
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error adding funds: ') . $e->getMessage())->error();
            return back();
        }
    }
    
    /**
     * Add funds to any wallet (from form).
     */
    public function addFundsStore(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'balance_type' => 'required|in:balance,reward_points,promotional_credits',
            'payment_method' => 'required|string',
            'description' => 'nullable|string',
            'approval_status' => 'required|in:approved,pending',
        ]);

        $user_id = $request->user_id;
        $admin = Auth::user();
        $requireApproval = $request->approval_status === 'pending';
        
        try {
            DB::beginTransaction();

            // Use appropriate service method based on balance type
            if ($request->balance_type === 'balance') {
                $this->transactionService->addFundsToWallet(
                    $user_id,
                    $request->amount,
                    $request->description ?? 'Admin added funds',
                    $request->payment_method,
                    null,
                    $requireApproval,
                    [
                        'admin_id' => $admin->id,
                        'admin_name' => $admin->name,
                        'admin_email' => $admin->email,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->balance_type === 'reward_points') {
                $this->transactionService->addRewardPoints(
                    $user_id,
                    (int)$request->amount,
                    $request->description ?? 'Admin added reward points',
                    $request->payment_method,
                    null,
                    null,
                    [
                        'admin_id' => $admin->id,
                        'admin_name' => $admin->name,
                        'admin_email' => $admin->email,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->balance_type === 'promotional_credits') {
                $this->transactionService->addPromotionalCredits(
                    $user_id,
                    $request->amount,
                    $request->description ?? 'Admin added promotional credits',
                    $request->has('expiry_date') ? $request->expiry_date : null,
                    null,
                    null,
                    null
                );
            }

            DB::commit();

            flash(translate('Funds added successfully'))->success();
            return redirect()->route('wallet.index');
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error adding funds: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * Show form to deduct funds from wallet
     */
    public function showDeductFunds($id)
    {
        $wallet = Wallet::with('user')->findOrFail($id);
        return view('backend.wallet.deduct_funds', compact('wallet'));
    }

    /**
     * Deduct funds from the specified wallet.
     */
    public function deductFunds(Request $request, $id)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string',
            'reference_type' => 'nullable|string',
            'reference_id' => 'nullable|string',
        ]);

        $wallet = Wallet::findOrFail($id);
        $admin = Auth::user();

        // Check if sufficient funds are available
        if ($request->type == 'balance') {
            if (!$this->validationService->validateWalletBalance($wallet->user_id, $request->amount)) {
                flash(translate('Insufficient wallet balance'))->error();
                return back();
            }
        } elseif ($request->type == 'reward_points') {
            if (!$this->validationService->validateRewardPoints($wallet->user_id, (int)$request->amount)) {
                flash(translate('Insufficient reward points'))->error();
                return back();
            }
        } elseif ($request->type == 'promotional_credits') {
            if ($wallet->promotional_credits < $request->amount) {
                flash(translate('Insufficient promotional credits'))->error();
                return back();
            }
        }

        try {
            DB::beginTransaction();

            // Create transaction record and update wallet using service
            if ($request->type == 'balance') {
                $transaction = $this->transactionService->deductFundsFromWallet(
                    $wallet->user_id,
                    $request->amount,
                    $request->description,
                    $request->reference_type ?? 'admin',
                    $request->reference_id,
                    false,
                    [
                        'added_by' => $admin->id,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->type == 'reward_points') {
                $transaction = $this->transactionService->deductRewardPoints(
                    $wallet->user_id,
                    (int)$request->amount,
                    $request->description,
                    $request->reference_type ?? 'admin',
                    $request->reference_id,
                    [
                        'added_by' => $admin->id,
                        'ip_address' => $request->ip()
                    ]
                );
            } elseif ($request->type == 'promotional_credits') {
                // Handle promotional credits deduction
                $wallet->promotional_credits -= $request->amount;
                $wallet->save();
                
                // Create transaction record
                $transaction = WalletTransaction::create([
                    'wallet_id' => $wallet->id,
                    'user_id' => $wallet->user_id,
                    'type' => 'debit',
                    'amount' => $request->amount,
                    'balance_type' => $request->type,
                    'description' => $request->description,
                    'reference_type' => $request->reference_type ?? 'admin',
                    'reference_id' => $request->reference_id,
                    'added_by' => $admin->id,
                    'approval_status' => 'approved',
                    'metadata' => [
                        'ip_address' => $request->ip()
                    ]
                ]);
            }

            DB::commit();

            flash(translate('Funds deducted successfully'))->success();
            return redirect()->route('admin.wallet.details', $id);
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error deducting funds: ') . $e->getMessage())->error();
            return back();
        }
    }
    
    /**
     * Deduct funds from any wallet (from form).
     */
    public function deductFundsStore(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'balance_type' => 'required|in:balance,reward_points,promotional_credits',
            'reason' => 'required|string',
            'description' => 'nullable|string',
            'approval_status' => 'required|in:approved,pending',
        ]);

        $user_id = $request->user_id;
        $admin = Auth::user();
        $requireApproval = $request->approval_status === 'pending';
        
        // Get wallet
        $wallet = Wallet::where('user_id', $user_id)->first();
        if (!$wallet) {
            flash(translate('User does not have a wallet'))->error();
            return back();
        }

        // Check sufficient balance if immediate approval
        if (!$requireApproval) {
            if ($request->balance_type === 'balance') {
                if (!$this->validationService->validateWalletBalance($user_id, $request->amount)) {
                    flash(translate('Insufficient wallet balance'))->error();
                    return back();
                }
            } elseif ($request->balance_type === 'reward_points') {
                if (!$this->validationService->validateRewardPoints($user_id, (int)$request->amount)) {
                    flash(translate('Insufficient reward points'))->error();
                    return back();
                }
            } elseif ($request->balance_type === 'promotional_credits') {
                if ($wallet->promotional_credits < $request->amount) {
                    flash(translate('Insufficient promotional credits'))->error();
                    return back();
                }
            }
        }

        try {
            DB::beginTransaction();

            $metadata = [
                'admin_id' => $admin->id,
                'admin_name' => $admin->name,
                'admin_email' => $admin->email,
                'reason' => $request->reason,
                'ip_address' => $request->ip()
            ];

            // Use appropriate service method based on balance type
            if ($request->balance_type === 'balance') {
                $this->transactionService->deductFundsFromWallet(
                    $user_id,
                    $request->amount,
                    $request->description ?? 'Admin deducted funds: ' . $request->reason,
                    'admin',
                    null,
                    $requireApproval,
                    $metadata
                );
            } elseif ($request->balance_type === 'reward_points') {
                if ($requireApproval) {
                    // Create pending transaction for reward points
                    $transaction = new WalletTransaction();
                    $transaction->wallet_id = $wallet->id;
                    $transaction->user_id = $user_id;
                    $transaction->type = 'debit';
                    $transaction->amount = (int)$request->amount;
                    $transaction->balance_type = 'reward_points';
                    $transaction->description = $request->description ?? 'Admin deducted reward points: ' . $request->reason;
                    $transaction->reference_type = 'admin';
                    $transaction->added_by = $admin->id;
                    $transaction->approval_status = 'pending';
                    $transaction->metadata = $metadata;
                    $transaction->save();
                } else {
                    $this->transactionService->deductRewardPoints(
                        $user_id,
                        (int)$request->amount,
                        $request->description ?? 'Admin deducted reward points: ' . $request->reason,
                        'admin',
                        null,
                        $metadata
                    );
                }
            } elseif ($request->balance_type === 'promotional_credits') {
                // Create transaction record
                $transaction = new WalletTransaction();
                $transaction->wallet_id = $wallet->id;
                $transaction->user_id = $user_id;
                $transaction->type = 'debit';
                $transaction->amount = $request->amount;
                $transaction->balance_type = 'promotional_credits';
                $transaction->description = $request->description ?? 'Admin deducted promotional credits: ' . $request->reason;
                $transaction->reference_type = 'admin';
                $transaction->added_by = $admin->id;
                $transaction->approval_status = $request->approval_status;
                $transaction->metadata = $metadata;
                $transaction->save();
                
                // Update wallet balance if approved immediately
                if (!$requireApproval) {
                    $wallet->promotional_credits -= $request->amount;
                    $wallet->last_updated_at = now();
                    $wallet->save();
                }
            }

            DB::commit();

            flash(translate('Funds deducted successfully'))->success();
            return redirect()->route('wallet.index');
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error deducting funds: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * Display pending transactions that need approval.
     */
    public function pendingTransactions()
    {
        $pendingTransactions = WalletTransaction::with(['wallet.user', 'addedBy'])
            ->where('approval_status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            
        return view('backend.wallet.pending_transactions', compact('pendingTransactions'));
    }
    
    /**
     * Approve a pending transaction.
     */
    public function approveTransaction(Request $request, $id)
    {
        $transaction = WalletTransaction::findOrFail($id);
        $admin = Auth::user();

        if ($transaction->approval_status !== 'pending') {
            flash(translate('Transaction is not in pending status'))->error();
            return back();
        }

        try {
            DB::beginTransaction();

            $wallet = Wallet::findOrFail($transaction->wallet_id);

            // Update transaction status
            $transaction->approval_status = 'approved';
            $transaction->approved_by = $admin->id;
            $transaction->approved_at = now();
            
            // Update metadata
            $metadata = $transaction->metadata ?? [];
            $metadata['approved_by'] = [
                'id' => $admin->id,
                'name' => $admin->name,
                'email' => $admin->email
            ];
            $metadata['approved_at'] = now()->toIso8601String();
            $transaction->metadata = $metadata;
            $transaction->save();

            // Update wallet balance based on transaction type
            if ($transaction->balance_type == 'balance') {
                if ($transaction->type == 'credit') {
                    $wallet->balance += $transaction->amount;
                } else {
                    // For debit transactions, verify sufficient balance before deducting
                    if ($wallet->balance < $transaction->amount) {
                        DB::rollBack();
                        flash(translate('Insufficient wallet balance to approve this debit transaction'))->error();
                        return back();
                    }
                    $wallet->balance -= $transaction->amount;
                }
            } elseif ($transaction->balance_type == 'reward_points') {
                if ($transaction->type == 'credit') {
                    $wallet->reward_points += $transaction->amount;
                } else {
                    // For debit transactions, verify sufficient points before deducting
                    if ($wallet->reward_points < $transaction->amount) {
                        DB::rollBack();
                        flash(translate('Insufficient reward points to approve this debit transaction'))->error();
                        return back();
                    }
                    $wallet->reward_points -= $transaction->amount;
                }
            } elseif ($transaction->balance_type == 'promotional_credits') {
                if ($transaction->type == 'credit') {
                    $wallet->promotional_credits += $transaction->amount;
                } else {
                    // For debit transactions, verify sufficient promotional credits before deducting
                    if ($wallet->promotional_credits < $transaction->amount) {
                        DB::rollBack();
                        flash(translate('Insufficient promotional credits to approve this debit transaction'))->error();
                        return back();
                    }
                    $wallet->promotional_credits -= $transaction->amount;
                }
            }

            $wallet->last_updated_at = now();
            $wallet->save();

            DB::commit();

            flash(translate('Transaction approved successfully'))->success();
            return back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error approving transaction: ') . $e->getMessage())->error();
            return back();
        }
    }
    
    /**
     * Reject a pending transaction.
     */
    public function rejectTransaction(Request $request, $id)
    {
        $request->validate([
            'reason' => 'required|string|max:255'
        ]);
        
        $transaction = WalletTransaction::findOrFail($id);
        $admin = Auth::user();
        
        // Only pending transactions can be rejected
        if ($transaction->approval_status !== 'pending') {
            flash(translate('This transaction is not pending approval'))->error();
            return back();
        }
        
        try {
            DB::beginTransaction();
            
            // Update transaction status
            $transaction->approval_status = 'rejected';
            
            // Update metadata
            $metadata = $transaction->metadata ?? [];
            $metadata['rejected_by'] = [
                'id' => $admin->id,
                'name' => $admin->name,
                'email' => $admin->email
            ];
            $metadata['rejected_at'] = now()->toIso8601String();
            $metadata['rejection_reason'] = $request->reason;
            $transaction->metadata = $metadata;
            $transaction->save();
            
            DB::commit();
            
            flash(translate('Transaction rejected successfully'))->success();
            return back();
            
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Error rejecting transaction: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * Display exchange rates and allow management.
     */
    public function exchangeRates()
    {
        $exchange_rates = WalletExchangeRate::orderBy('created_at', 'desc')->paginate(15);
        return view('backend.wallet.exchange_rates', compact('exchange_rates'));
    }

    /**
     * Show form to create a new exchange rate.
     */
    public function createExchangeRateForm()
    {
        return view('backend.wallet.exchange_rates_create');
    }

    /**
     * Create a new exchange rate.
     */
    public function createExchangeRate(Request $request)
    {
        $request->validate([
            'from_type' => 'required|string|in:balance,reward_points,promotional_credits',
            'to_type' => 'required|string|in:balance,reward_points,promotional_credits',
            'rate' => 'required|numeric|min:0.0001',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'fee_fixed' => 'nullable|numeric|min:0',
            'fee_percent' => 'nullable|numeric|min:0|max:100',
            'status' => 'nullable|boolean',
        ]);

        try {
            $exchange_rate = new WalletExchangeRate();
            $exchange_rate->from_type = $request->from_type;
            $exchange_rate->to_type = $request->to_type;
            $exchange_rate->rate = $request->rate;
            $exchange_rate->min_amount = $request->min_amount;
            $exchange_rate->max_amount = $request->max_amount ?? 0;
            $exchange_rate->fee_fixed = $request->fee_fixed ?? 0;
            $exchange_rate->fee_percent = $request->fee_percent ?? 0;
            $exchange_rate->status = $request->has('status') ? 1 : 0;
            $exchange_rate->save();

            flash(translate('Exchange rate created successfully'))->success();
            return redirect()->route('wallet.exchange_rates');
        } catch (\Exception $e) {
            flash(translate('Error creating exchange rate: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * Show form to edit an exchange rate.
     */
    public function editExchangeRateForm($id)
    {
        $exchange_rate = WalletExchangeRate::findOrFail($id);
        return view('backend.wallet.partials.edit_exchange_rate', compact('exchange_rate'));
    }

    /**
     * Update the specified exchange rate.
     */
    public function updateExchangeRate(Request $request, $id)
    {
        $request->validate([
            'from_type' => 'required|string|in:balance,reward_points,promotional_credits',
            'to_type' => 'required|string|in:balance,reward_points,promotional_credits',
            'rate' => 'required|numeric|min:0.0001',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'fee_fixed' => 'nullable|numeric|min:0',
            'fee_percent' => 'nullable|numeric|min:0|max:100',
            'status' => 'nullable|boolean',
        ]);

        try {
            $exchange_rate = WalletExchangeRate::findOrFail($id);
            $exchange_rate->from_type = $request->from_type;
            $exchange_rate->to_type = $request->to_type;
            $exchange_rate->rate = $request->rate;
            $exchange_rate->min_amount = $request->min_amount;
            $exchange_rate->max_amount = $request->max_amount ?? 0;
            $exchange_rate->fee_fixed = $request->fee_fixed ?? 0;
            $exchange_rate->fee_percent = $request->fee_percent ?? 0;
            $exchange_rate->status = $request->has('status') ? 1 : 0;
            $exchange_rate->save();

            flash(translate('Exchange rate updated successfully'))->success();
            return redirect()->route('wallet.exchange_rates');
        } catch (\Exception $e) {
            flash(translate('Error updating exchange rate: ') . $e->getMessage())->error();
            return back();
        }
    }

    /**
     * Delete the specified exchange rate.
     */
    public function deleteExchangeRate($id)
    {
        try {
            $exchange_rate = WalletExchangeRate::findOrFail($id);
            $exchange_rate->delete();

            flash(translate('Exchange rate deleted successfully'))->success();
        } catch (\Exception $e) {
            flash(translate('Error deleting exchange rate: ') . $e->getMessage())->error();
        }

        return back();
    }

    /**
     * Update the status of an exchange rate.
     */
    public function updateExchangeRateStatus(Request $request)
    {
        try {
            $exchange_rate = WalletExchangeRate::findOrFail($request->id);
            $exchange_rate->status = $request->status;
            $exchange_rate->save();
            
            return 1;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Display wallet statistics.
     */
    public function statistics()
    {
        // Overall wallet statistics
        $totalWallets = Wallet::count();
        $activeWallets = Wallet::where('status', 'active')->count();
        $totalBalance = Wallet::sum('balance');
        $totalRewardPoints = Wallet::sum('reward_points');
        $totalPromotionalCredits = Wallet::sum('promotional_credits');

        // Transaction statistics
        $transactionStats = [
            'total_transactions' => WalletTransaction::count(),
            'total_credits' => WalletTransaction::where('type', 'credit')->sum('amount'),
            'total_debits' => WalletTransaction::where('type', 'debit')->sum('amount'),
            'transaction_count_by_type' => [
                'credit' => WalletTransaction::where('type', 'credit')->count(),
                'debit' => WalletTransaction::where('type', 'debit')->count()
            ],
            'transaction_count_by_reference' => [
                'order' => WalletTransaction::where('reference_type', 'order')->count(),
                'refund' => WalletTransaction::where('reference_type', 'refund')->count(),
                'admin' => WalletTransaction::where('reference_type', 'admin')->count(),
                'reward' => WalletTransaction::where('reference_type', 'reward')->count(),
                'promotion' => WalletTransaction::where('reference_type', 'promotion')->count()
            ]
        ];

        // Top users by wallet balance
        $topUsersByBalance = Wallet::with('user')
            ->orderBy('balance', 'desc')
            ->limit(10)
            ->get();

        // Daily statistics for the past 30 days
        $dailyStats = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i)->format('Y-m-d');
            $dailyStats[] = [
                'date' => $date,
                'credits' => WalletTransaction::where('type', 'credit')
                    ->whereDate('created_at', $date)
                    ->sum('amount'),
                'debits' => WalletTransaction::where('type', 'debit')
                    ->whereDate('created_at', $date)
                    ->sum('amount'),
                'transaction_count' => WalletTransaction::whereDate('created_at', $date)->count()
            ];
        }

        // Recent transactions
        $recentTransactions = WalletTransaction::with('wallet.user')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('backend.wallet.statistics', compact(
            'totalWallets', 
            'activeWallets', 
            'totalBalance', 
            'totalRewardPoints',
            'totalPromotionalCredits',
            'transactionStats',
            'topUsersByBalance',
            'dailyStats',
            'recentTransactions'
        ));
    }
    
    /**
     * Display details for a specific transaction.
     */
    public function transactionDetails($id)
    {
        $transaction = WalletTransaction::with(['wallet.user', 'addedBy'])->findOrFail($id);
        return view('backend.wallet.partials.transaction_details', compact('transaction'));
    }
} 