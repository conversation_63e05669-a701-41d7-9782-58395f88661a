<?php

namespace App\Console\Commands;

use App\Models\EmailVerify;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class TestForgotPasswordIntegration extends Command
{
    protected $signature = 'test:forgot-password-integration';
    protected $description = 'Test the forgot password integration with email verification system';

    public function handle()
    {
        $this->info('Testing Forgot Password Integration...');

        try {
            // Test 1: Check if EmailVerify model methods work
            $this->info('1. Testing EmailVerify model methods...');
            
            $testEmail = '<EMAIL>';
            
            // Generate OTP
            $emailVerifyRecord = EmailVerify::generateOTP($testEmail, 10);
            $this->info("   ✓ OTP generated: {$emailVerifyRecord->otp}");
            $this->info("   ✓ Expires at: {$emailVerifyRecord->expire_time}");
            $this->info("   ✓ Resend count: {$emailVerifyRecord->resend_count}");
            
            // Test cooldown methods
            $this->info("   ✓ Is in cooldown: " . ($emailVerifyRecord->isInCooldownPeriod() ? 'Yes' : 'No'));
            $this->info("   ✓ Can resend: " . ($emailVerifyRecord->canResend() ? 'Yes' : 'No'));
            $this->info("   ✓ Remaining cooldown: {$emailVerifyRecord->getRemainingCooldownMinutes()} minutes");
            
            // Test OTP verification
            $otp = $emailVerifyRecord->otp;
            $verified = EmailVerify::verifyOTP($testEmail, $otp);
            $this->info("   ✓ OTP verification: " . ($verified ? 'Success' : 'Failed'));
            
            // Test 2: Check if User model exists and can be updated
            $this->info('2. Testing User model integration...');
            
            $user = User::where('email', $testEmail)->first();
            if (!$user) {
                $this->info('   Creating test user...');
                $user = User::create([
                    'name' => 'Test User',
                    'email' => $testEmail,
                    'password' => Hash::make('oldpassword'),
                    'user_type' => 'customer',
                    'email_verified_at' => now()
                ]);
            }
            
            $this->info("   ✓ User found/created: {$user->name} ({$user->email})");
            
            // Test password update
            $newPassword = 'newpassword123';
            $user->password = Hash::make($newPassword);
            $user->save();
            
            $this->info("   ✓ Password updated successfully");
            $this->info("   ✓ Password verification: " . (Hash::check($newPassword, $user->password) ? 'Success' : 'Failed'));
            
            // Test 3: Check API endpoints structure
            $this->info('3. Testing API endpoint structure...');
            
            $controllerPath = base_path('app/Http/Controllers/Api/V3/Auth/ApiResetPasswordController.php');
            if (file_exists($controllerPath)) {
                $this->info("   ✓ ApiResetPasswordController exists");
                
                $content = file_get_contents($controllerPath);
                $this->info("   File size: " . strlen($content) . " bytes");
                
                $methods = ['forgetRequest', 'confirmReset', 'resendResetOTP'];
                
                foreach ($methods as $method) {
                    if (strpos($content, $method) !== false) {
                        $this->info("   ✓ Method {$method} exists");
                    } else {
                        $this->error("   ✗ Method {$method} missing");
                        // Debug: show first 200 chars of content
                        $this->info("   Debug - First 200 chars: " . substr($content, 0, 200));
                    }
                }
            } else {
                $this->error("   ✗ ApiResetPasswordController not found");
            }
            
            // Test 4: Check routes
            $this->info('4. Testing route configuration...');
            
            $routePath = base_path('routes/v3/api_auth.php');
            if (file_exists($routePath)) {
                $this->info("   ✓ Auth routes file exists");
                
                $content = file_get_contents($routePath);
                $routes = ['forgot-password', 'reset-password', 'resend-reset-otp'];
                
                foreach ($routes as $route) {
                    if (strpos($content, $route) !== false) {
                        $this->info("   ✓ Route {$route} configured");
                    } else {
                        $this->error("   ✗ Route {$route} missing");
                    }
                }
            } else {
                $this->error("   ✗ Auth routes file not found");
            }
            
            // Cleanup
            $this->info('5. Cleaning up test data...');
            EmailVerify::where('email', $testEmail)->delete();
            User::where('email', $testEmail)->delete();
            $this->info("   ✓ Test data cleaned up");
            
            $this->info('');
            $this->info('✅ All tests passed! Forgot password integration is working correctly.');
            
        } catch (\Exception $e) {
            $this->error('❌ Test failed with error: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }
        
        return 0;
    }
}