<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\FlashSale\FlashSaleResource;
use App\Http\Resources\V3\ProductMiniResource;
use App\Models\Promotions\FlashSale;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiFlashSaleController extends ApiResponse
{
    /**
     * Get active flash sales
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveFlashSales(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
                'include_products' => 'boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 5);
            $includeProducts = $request->boolean('include_products', false);
            
            $query = FlashSale::active()->orderBy('priority', 'desc');
            
            if ($includeProducts) {
                $query->with('products');
            }
            
            $flashSales = $query->limit($limit)->get();
            
            return $this->success(FlashSaleResource::collection($flashSales));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get upcoming flash sales
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUpcomingFlashSales(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 5);
            
            $flashSales = FlashSale::upcoming()
                ->orderBy('start_date', 'asc')
                ->limit($limit)
                ->get();
                
            return $this->success(FlashSaleResource::collection($flashSales));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get featured flash sales
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFeaturedFlashSales(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
                'include_products' => 'boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 5);
            $includeProducts = $request->boolean('include_products', false);
            
            $query = FlashSale::active()->featured()->orderBy('priority', 'desc');
            
            if ($includeProducts) {
                $query->with('products');
            }
            
            $flashSales = $query->limit($limit)->get();
            
            return $this->success(FlashSaleResource::collection($flashSales));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get flash sale by ID or slug
     *
     * @param string $idOrSlug
     * @return JsonResponse
     */
    public function getFlashSale(string $idOrSlug): JsonResponse
    {
        try {
            // Determine if the parameter is an ID or slug
            $query = FlashSale::query();
            
            if (is_numeric($idOrSlug)) {
                $flashSale = $query->find($idOrSlug);
            } else {
                $flashSale = $query->where('slug', $idOrSlug)->first();
            }
            
            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }
            
            // Include products
            $flashSale->load('products');
            
            return $this->success(new FlashSaleResource($flashSale));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get products in a flash sale
     *
     * @param string $idOrSlug
     * @param Request $request
     * @return JsonResponse
     */
    public function getFlashSaleProducts(string $idOrSlug, Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:100',
                'page' => 'integer|min:1'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }
            
            $limit = $request->input('limit', 20);
            $page = $request->input('page', 1);
            
            // Determine if the parameter is an ID or slug
            $query = FlashSale::query();
            
            if (is_numeric($idOrSlug)) {
                $flashSale = $query->find($idOrSlug);
            } else {
                $flashSale = $query->where('slug', $idOrSlug)->first();
            }
            
            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }
            
            // Get products with pagination
            $products = $flashSale->products()->paginate($limit, ['*'], 'page', $page);
            
            return $this->success([
                'products' => ProductMiniResource::collection($products),
                'pagination' => [
                    'total' => $products->total(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 