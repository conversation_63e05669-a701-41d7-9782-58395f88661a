<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Controllers\InvoiceController;
use App\Http\Resources\V3\Orders\OrderInvoiceResource;
use App\Http\Resources\V3\Orders\InvoiceListResource;
use App\Models\Order;
use App\Services\ApiInvoiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ApiInvoiceController extends ApiResponse
{
    protected ApiInvoiceService $apiInvoiceService;

    /**
     * Create a new controller instance.
     *
     * @param ApiInvoiceService $apiInvoiceService
     */
    public function __construct(ApiInvoiceService $apiInvoiceService)
    {
        $this->apiInvoiceService = $apiInvoiceService;
    }

    /**
     * Get invoice details for a specific order
     *
     * @param int $orderId
     * @return JsonResponse
     */
    public function getOrderInvoice(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get order with invoice data
            $order = $this->apiInvoiceService->getOrderWithInvoiceData($orderId, $user->id);

            // Track invoice view
            $order->invoice_view_count += 1;
            $order->invoice_last_viewed = now();
            $order->save();

            // Return response
            return $this->success(
                new OrderInvoiceResource($order),
                'Invoice retrieved successfully'
            );
        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve invoice',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Download invoice for a specific order in the specified format
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\Response|JsonResponse
     */
    public function downloadInvoice(Request $request, string $orderId)
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Verify that the order belongs to the user
            $order = Order::where(function ($query) use ($orderId) {
                $query->where('id', $orderId)
                    ->orWhere('code', $orderId);
            })
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Get format parameter (default to PDF)
            $format = strtolower($request->query('format', 'pdf'));

            // Validate format
            if (!in_array($format, ['pdf', 'csv'])) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Invalid format specified. Supported formats are "pdf" and "csv".',
                    null,
                    422
                );
            }

            // Track invoice download
            $order->invoice_download_count += 1;
            $order->invoice_last_downloaded = now();
            $order->save();

            // Use the appropriate method based on format
            if ($format === 'pdf') {
                // Use the existing InvoiceController to generate the PDF
                $invoiceController = new InvoiceController();
                return $invoiceController->invoice_download($order->id);
            } else {
                // For CSV format, use our custom implementation
                return $this->generateCsvInvoice($order);
            }

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to download invoice',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Generate CSV invoice
     *
     * @param Order $order
     * @return \Illuminate\Http\Response|JsonResponse
     */
    private function generateCsvInvoice($order)
    {
        try {
            // Make sure order details are loaded
            $order->load(['orderDetails.product.stocks', 'user']);

            // Create CSV content
            $csvContent = $this->generateCsvContent($order);

            // Set headers for CSV download
            $headers = [
                'Content-Type' => 'text/csv; charset=utf-8',
                'Content-Disposition' => 'attachment; filename=invoice-' . $order->code . '.csv',
                'Pragma' => 'no-cache',
                'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                'Expires' => '0'
            ];

            // Return the CSV as a direct download
            return response($csvContent, 200, $headers);

        } catch (\Exception $e) {
            // If there's an error, return a JSON response
            return $this->error(
                'SERVER_ERROR',
                'Failed to generate CSV invoice: ' . $e->getMessage(),
                $e->getTraceAsString(),
                500
            );
        }
    }

    /**
     * Generate CSV content for an order
     *
     * @param Order $order
     * @return string
     */
    private function generateCsvContent($order)
    {
        // Create a temporary file handle
        $handle = fopen('php://temp', 'r+');

        // Add headers
        fputcsv($handle, ['Invoice Number', 'Order Number', 'Date', 'Customer', 'Total']);

        // Get shipping address
        $shipping_address = json_decode($order->shipping_address);

        // Add invoice details
        fputcsv($handle, [
            'INV-' . date('Y') . '-' . $order->id,
            $order->code,
            date('Y-m-d', $order->date),
            $shipping_address->name ?? 'Customer',
            number_format($order->grand_total, 2)
        ]);

        // Add item headers
        fputcsv($handle, ['', '', '', '', '']);
        fputcsv($handle, ['Item', 'SKU', 'Price', 'Quantity', 'Total']);

        // Add items
        foreach ($order->orderDetails as $detail) {
            if ($detail->product) {
                // Get SKU from product stocks
                $sku = 'N/A';
                if ($detail->product->stocks && $detail->product->stocks->count() > 0) {
                    $product_stock = json_decode($detail->product->stocks->first(), true);
                    $sku = $product_stock['sku'] ?? 'N/A';
                }

                fputcsv($handle, [
                    $detail->product->name . ($detail->variation ? ' (' . $detail->variation . ')' : ''),
                    $sku,
                    number_format($detail->price / $detail->quantity, 2),
                    $detail->quantity,
                    number_format($detail->price, 2)
                ]);
            }
        }

        // Add totals
        fputcsv($handle, ['', '', '', '', '']);
        fputcsv($handle, ['', '', '', 'Subtotal', number_format($order->orderDetails->sum('price'), 2)]);
        fputcsv($handle, ['', '', '', 'Shipping', number_format($order->orderDetails->sum('shipping_cost'), 2)]);
        fputcsv($handle, ['', '', '', 'Tax', number_format($order->orderDetails->sum('tax'), 2)]);

        if ($order->coupon_discount > 0) {
            fputcsv($handle, ['', '', '', 'Discount', number_format($order->coupon_discount, 2)]);
        }

        fputcsv($handle, ['', '', '', 'Grand Total', number_format($order->grand_total, 2)]);

        // Reset the file pointer to the beginning
        rewind($handle);

        // Read the entire file into a string
        $csvContent = stream_get_contents($handle);

        // Close the file handle
        fclose($handle);

        return $csvContent;
    }

    /**
     * Email invoice to specified email address
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function emailInvoice(Request $request, string $orderId): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'message' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Verify that the order belongs to the user
            $order = Order::where(function ($query) use ($orderId) {
                $query->where('id', $orderId)
                    ->orWhere('code', $orderId);
            })
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Generate PDF invoice
            $invoiceController = new InvoiceController();
            $pdf = $invoiceController->invoice_download_as_pdf($order->id);

            // Send email with PDF attachment
            Mail::send('emails.invoice', [
                'order' => $order,
                'message' => $request->input('message', 'Please find your invoice attached.')
            ], function ($message) use ($request, $order, $pdf) {
                $message->to($request->input('email'))
                    ->subject('Invoice for Order #' . $order->code)
                    ->attachData($pdf->output(), 'invoice-' . $order->code . '.pdf', [
                        'mime' => 'application/pdf'
                    ]);
            });

            return $this->success([
                'message' => 'Invoice sent successfully'
            ]);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to send invoice email: ' . $e->getMessage(),
                $e->getTraceAsString(),
                500
            );
        }
    }

    /**
     * Get invoice metadata (view and download statistics)
     *
     * @param string $orderId
     * @return JsonResponse
     */
    public function getInvoiceMetadata(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Verify that the order belongs to the user
            $order = Order::where(function ($query) use ($orderId) {
                $query->where('id', $orderId)
                    ->orWhere('code', $orderId);
            })
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Format the metadata
            $metadata = [
                'invoiceId' => 'inv_' . $order->code,
                'orderId' => 'ord_' . $order->code,
                'viewCount' => $order->invoice_view_count,
                'lastViewed' => $order->invoice_last_viewed ? (is_string($order->invoice_last_viewed) ? $order->invoice_last_viewed : $order->invoice_last_viewed->toIso8601String()) : null,
                'downloadCount' => $order->invoice_download_count,
                'lastDownloaded' => $order->invoice_last_downloaded ? (is_string($order->invoice_last_downloaded) ? $order->invoice_last_downloaded : $order->invoice_last_downloaded->toIso8601String()) : null
            ];

            // Return response
            return $this->success([
                'data' => $metadata
            ]);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve invoice metadata',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Mark invoice as viewed
     *
     * @param string $orderId
     * @return JsonResponse
     */
    public function markInvoiceAsViewed(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Verify that the order belongs to the user
            $order = Order::where(function ($query) use ($orderId) {
                $query->where('id', $orderId)
                    ->orWhere('code', $orderId);
            })
                ->where('user_id', $user->id)
                ->firstOrFail();

            // Increment view count and update last viewed timestamp
            $order->invoice_view_count += 1;
            $order->invoice_last_viewed = now();
            $order->save();

            // Format the response
            $data = [
                'invoiceId' => 'inv_' . $order->code,
                'viewCount' => $order->invoice_view_count,
                'lastViewed' => is_string($order->invoice_last_viewed)
                    ? $order->invoice_last_viewed
                    : $order->invoice_last_viewed->toIso8601String()
            ];

            // Return response
            return $this->success([
                'data' => $data,
                'message' => 'Invoice marked as viewed'
            ]);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to mark invoice as viewed',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a paginated list of invoices for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getInvoiceList(Request $request): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get query parameters
            $page = $request->query('page', 1);
            $limit = min($request->query('limit', 10), 50); // Max 50 items per page
            $status = $request->query('status');
            $sort = $request->query('sort', 'newest');
            $startDate = $request->query('start_date');
            $endDate = $request->query('end_date');
            $search = $request->query('search');

            // Start building the query
            $query = Order::where('user_id', $user->id)
                ->with(['orderDetails.product', 'user']);

            // Apply status filter
            if ($status) {
                if ($status === 'overdue') {
                    // For overdue, we need to check if the due date has passed
                    // Assuming due date is 15 days after order date
                    $query->where('payment_status', 'unpaid')
                        ->where('date', '<', strtotime('-15 days'));
                } else {
                    $query->where('payment_status', $status);
                }
            }

            // Apply date filters
            if ($startDate) {
                $query->where('date', '>=', strtotime($startDate));
            }

            if ($endDate) {
                $query->where('date', '<=', strtotime($endDate . ' 23:59:59'));
            }

            // Apply search
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('code', 'like', "%{$search}%")
                        ->orWhere('id', 'like', "%{$search}%");
                });
            }

            // Apply sorting
            switch ($sort) {
                case 'oldest':
                    $query->orderBy('date', 'asc');
                    break;
                case 'amount_asc':
                    $query->orderBy('grand_total', 'asc');
                    break;
                case 'amount_desc':
                    $query->orderBy('grand_total', 'desc');
                    break;
                case 'newest':
                default:
                    $query->orderBy('date', 'desc');
                    break;
            }

            // Get paginated results
            $invoices = $query->paginate($limit, ['*'], 'page', $page);

            // Format the response
            return $this->success([
                'invoices' => InvoiceListResource::collection($invoices),
                'meta' => [
                    'pagination' => [
                        'total' => $invoices->total(),
                        'count' => $invoices->count(),
                        'per_page' => $invoices->perPage(),
                        'current_page' => $invoices->currentPage(),
                        'total_pages' => $invoices->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve invoice list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a list of unpaid invoices for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUnpaidInvoices(Request $request): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get query parameters
            $page = $request->query('page', 1);
            $limit = min($request->query('limit', 10), 50); // Max 50 items per page

            // Start building the query
            $query = Order::where('user_id', $user->id)
                ->with(['orderDetails.product', 'user'])
                ->where(function ($q) {
                    // Get orders with payment_status 'unpaid'
                    $q->where('payment_status', 'unpaid')
                        // Or orders with payment_status 'pending'
                        ->orWhere('payment_status', 'pending');
                })
                ->orderBy('date', 'desc'); // Sort by date (newest first)

            // Get paginated results
            $invoices = $query->paginate($limit, ['*'], 'page', $page);

            // Format the response
            return $this->success([
                'invoices' => InvoiceListResource::collection($invoices),
                'meta' => [
                    'pagination' => [
                        'total' => $invoices->total(),
                        'count' => $invoices->count(),
                        'per_page' => $invoices->perPage(),
                        'current_page' => $invoices->currentPage(),
                        'total_pages' => $invoices->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve unpaid invoice list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a paginated list of recent invoices (issued in the last 30 days) for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecentInvoices(Request $request): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get query parameters
            $page = $request->query('page', 1);
            $limit = min($request->query('limit', 10), 50); // Max 50 items per page

            // Calculate the timestamp for 30 days ago
            $thirtyDaysAgo = strtotime('-30 days');

            // Start building the query
            $query = Order::where('user_id', $user->id)
                ->with(['orderDetails.product', 'user'])
                ->where('date', '>=', $thirtyDaysAgo)
                ->orderBy('date', 'desc'); // Sort by date (newest first)

            // Get paginated results
            $invoices = $query->paginate($limit, ['*'], 'page', $page);

            // Format the response
            return $this->success([
                'invoices' => InvoiceListResource::collection($invoices),
                'meta' => [
                    'pagination' => [
                        'total' => $invoices->total(),
                        'count' => $invoices->count(),
                        'per_page' => $invoices->perPage(),
                        'current_page' => $invoices->currentPage(),
                        'total_pages' => $invoices->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve recent invoice list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Search for invoices by invoice number, order ID, or amount
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchInvoices(Request $request): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:2',
                'page' => 'nullable|integer|min:1',
                'limit' => 'nullable|integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get query parameters
            $searchQuery = $request->query('query');
            $page = $request->query('page', 1);
            $limit = min($request->query('limit', 10), 50); // Max 50 items per page

            // Start building the query
            $query = Order::where('user_id', $user->id)
                ->with(['orderDetails.product', 'user'])
                ->where(function ($q) use ($searchQuery) {
                    // Search by order code (order ID)
                    $q->where('code', 'like', "%{$searchQuery}%")
                        // Search by order ID
                        ->orWhere('id', 'like', "%{$searchQuery}%");

                    // If the search query is numeric, also search by amount
                    if (is_numeric($searchQuery)) {
                        // Convert to float for amount comparison
                        $amount = (float) $searchQuery;

                        // Search by grand_total
                        $q->orWhere('grand_total', $amount)
                            // Search by approximate amount (within 0.01)
                            ->orWhereBetween('grand_total', [$amount - 0.01, $amount + 0.01]);
                    }
                })
                ->orderBy('date', 'desc'); // Sort by date (newest first)

            // Get paginated results
            $invoices = $query->paginate($limit, ['*'], 'page', $page);

            // Format the response
            return $this->success([
                'invoices' => InvoiceListResource::collection($invoices),
                'meta' => [
                    'pagination' => [
                        'total' => $invoices->total(),
                        'count' => $invoices->count(),
                        'per_page' => $invoices->perPage(),
                        'current_page' => $invoices->currentPage(),
                        'total_pages' => $invoices->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to search invoices',
                $e->getMessage(),
                500
            );
        }
    }
}
