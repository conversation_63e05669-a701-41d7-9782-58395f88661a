<?php

namespace App\Http\Resources\V3\Cart;

use Illuminate\Http\Resources\Json\JsonResource;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'items' => CartItemResource::collection($this['items']),
            'summary' => [
                'item_count' => (int)$this['summary']['item_count'],
                'total_quantity' => (int)$this['summary']['total_quantity'],
                'subtotal' => (float)$this['summary']['subtotal'],
                'tax' => (float)$this['summary']['tax'],
                'shipping' => (float)$this['summary']['shipping'],
                'discount' => (float)$this['summary']['discount'],
                'total' => (float)$this['summary']['total'],
                'coupon_code' => $this['summary']['coupon_code'],
                'coupon_applied' => (bool)$this['summary']['coupon_applied']
            ],
            'temp_user_id' => $this['temp_user_id'] ?? null
        ];
    }
}
