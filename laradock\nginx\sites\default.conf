server {

    listen 80 default_server;
    listen [::]:80 default_server ipv6only=on;

    # For https
    # listen 443 ssl default_server;
    # listen [::]:443 ssl default_server ipv6only=on;
    # ssl_certificate /etc/nginx/ssl/default.crt;
    # ssl_certificate_key /etc/nginx/ssl/default.key;

    server_name localhost;
    root /var/www;
    index index.php index.html index.htm;

    # Default location - can serve a simple index page
    location / {
        try_files $uri $uri/ =404;
    }

    # Laravel app at /buzfi
    location /buzfi {
        alias /var/www/backend/public;
        try_files $uri $uri/ @buzfi;

        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            fastcgi_pass php-upstream;
            fastcgi_index index.php;
            fastcgi_buffers 16 16k;
            fastcgi_buffer_size 32k;
            fastcgi_read_timeout 600;
        }
    }

    # Handle Laravel routing for /buzfi
    location @buzfi {
        rewrite /buzfi/(.*)$ /buzfi/index.php?/$1 last;
    }

    # Handle PHP files in the buzfi directory
    location ~ ^/buzfi/(.+\.php)$ {
        alias /var/www/backend/public;
        try_files /$1 =404;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME /var/www/backend/public/$1;
        fastcgi_pass php-upstream;
        fastcgi_index index.php;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 600;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }
}
