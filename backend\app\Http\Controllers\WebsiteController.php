<?php

namespace App\Http\Controllers;

use App\Models\HomePageBanner;
use App\Models\PopularProductDetail;
use App\Models\PopularProductInfo;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class WebsiteController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check
        $this->middleware(['permission:header_setup'])->only('header');
        $this->middleware(['permission:footer_setup'])->only('footer');
        $this->middleware(['permission:view_all_website_pages'])->only('pages');
        $this->middleware(['permission:website_appearance'])->only('appearance');
    }

    public function header(Request $request)
    {
        return view('backend.website_settings.header');
    }

    public function footer(Request $request)
    {
        $lang = $request->lang;
        return view('backend.website_settings.footer', compact('lang'));
    }

    public function pages(Request $request)
    {
        return view('backend.website_settings.pages.index');
    }

    public function appearance(Request $request)
    {
        return view('backend.website_settings.appearance');
    }


    public function banners()
    {
        $data['home_page_banners'] = HomePageBanner::where('status', '1')->orderBy('position', 'asc')->get();
        return view('backend.website_settings.banners', $data);
    }

    public function update_banners(Request $request)
    {
        foreach ($request->id as $key => $value) {
            $status = $request->status . '_' . $value;
            HomePageBanner::find($value)->update(
                [
                    'banner_images' => $request->banner_images[$key],
                    'link' => $request->link[$key],
                    'position' => $request->position[$key],
                    'status' => isset($status) ? 1 : 0
                ]
            );
        }
        flash(translate('Top Picks of Category inserted successfully'))->success();
        return redirect()->route('website.banners');
    }
    public function popular_products()
    {
        $this->check_popular_product_info_exist();

        $data['popular_products_info'] = PopularProductInfo::with('popular_products')->find(1);
        $selected_popular_product_ids=$data['popular_products_info']->popular_products->pluck('product_id')->toArray();
        $data['selected_popular_products']=Product::whereIn('id', $selected_popular_product_ids)->get();
        return view('backend.website_settings.popular_products', $data);
    }

    public function update_popular_products(Request $request)
    {
        $this->check_popular_product_info_exist();
        $popular_product_info = PopularProductInfo::find($request->id);
        $popular_product_info->title = $request->title;
        $popular_product_info->slug = strtolower(str_replace(' ', '-', $request->title) . '-' . Str::random(5));
        $popular_product_info->banner_images = $request->banner_images;
        $popular_product_info->status = $request->status;
        $popular_product_info->link = $request->link;
        $popular_product_info->position = 1;
        $popular_product_info->save();
        PopularProductDetail::where('popular_product_info_id', $request->id)->delete();
        foreach ($request->products as $key => $value) {
            $popular_product_detail = new PopularProductDetail();
            $popular_product_detail->popular_product_info_id = $request->id;
            $popular_product_detail->product_id = $value;
            $popular_product_detail->position = 0;
            $popular_product_detail->save();
        }
        flash(translate('Top Picks of Category inserted successfully'))->success();
        return redirect()->route('website.update_popular_products');
    }
    public function check_popular_product_info_exist()
    {
        if(!PopularProductInfo::find(1)){
            $popular_product_info = new PopularProductInfo();
            $popular_product_info->title = "Change Name Here";
            $popular_product_info->slug = strtolower(str_replace(' ', '-', 'Change Name Here') . '-' . Str::random(5));;
            $popular_product_info->banner_images = Null;$popular_product_info->status = 0;
            $popular_product_info->link = Null;
            $popular_product_info->position = 1;
            $popular_product_info->status = 0;
            $popular_product_info->save();
        }
    }
    public function get_popular_products(Request $request){
        $product_ids = $request->product_ids;
        return view('backend.website_settings.popular_products_edit', compact('product_ids'));
    }
}
