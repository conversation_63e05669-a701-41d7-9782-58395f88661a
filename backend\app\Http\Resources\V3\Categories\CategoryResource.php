<?php

namespace App\Http\Resources\V3\Categories;

use App\Http\Resources\V2\Seller\ChildCategoriesCollection;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Product;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;
class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Get parameters from request
        $include_subcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $include_products = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $product_limit = filter_var((int) $request->input('product_limit', 4), FILTER_VALIDATE_BOOLEAN);
        if(!$product_limit){
            $product_limit = 4;
        }
        // Build response
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'level' => $this->level,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'banner' => uploaded_asset($this->banner),
            'icon' => uploaded_asset($this->icon),
            'cover_image' => uploaded_asset($this->cover_image),
            'is_featured' => (boolean) $this->featured,
            'is_active' => (boolean) $this->is_visible,
            'product_count' => $this->getProductCount(),
        ];

        // Include subcategories if requested
        if ($include_subcategories && $this->relationLoaded('childrenCategories')) {
            $data['subcategories'] = SubCategoryResource::collection($this->childrenCategories);
        }

        // Include products if requested
        if ($include_products && $this->relationLoaded('products')  ) {

            $data['products'] = new ProductsResource(
                Product::where('category_id', $this->id)->where('published', 1)->where('approved', 1)->limit($product_limit)->get()); //($include_products && $this->relationLoaded('products')new ProductsResource($this->products);
        }

        return $data;
    }
}
