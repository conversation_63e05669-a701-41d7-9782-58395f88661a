<?php

namespace App\Http\Controllers\Api\V3\Promotions\Admin;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Banner\BannerResource;
use App\Models\Promotions\Banner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AdminBannerController extends ApiResponse
{
    /**
     * Get all banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'integer|min:1',
                'limit' => 'integer|min:1|max:100',
                'status' => 'nullable|string|in:active,inactive,all',
                'position' => 'nullable|string|max:50',
                'page_location' => 'nullable|string|max:50',
                'sort_by' => 'nullable|string|in:created_at,priority,title',
                'sort_order' => 'nullable|string|in:asc,desc',
                'search' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $status = $request->input('status', 'all');
            $position = $request->input('position');
            $pageLocation = $request->input('page_location');
            $sortBy = $request->input('sort_by', 'priority');
            $sortOrder = $request->input('sort_order', 'asc');
            $search = $request->input('search');

            $query = Banner::query();

            // Apply status filter
            if ($status !== 'all') {
                $now = now();
                
                if ($status === 'active') {
                    $query->where('is_active', true)
                        ->where(function($q) use ($now) {
                            $q->whereNull('start_date')
                                ->orWhere('start_date', '<=', $now);
                        })
                        ->where(function($q) use ($now) {
                            $q->whereNull('end_date')
                                ->orWhere('end_date', '>=', $now);
                        });
                } else {
                    $query->where(function($q) use ($now) {
                        $q->where('is_active', false)
                            ->orWhere(function($q2) use ($now) {
                                $q2->whereNotNull('start_date')
                                    ->where('start_date', '>', $now);
                            })
                            ->orWhere(function($q2) use ($now) {
                                $q2->whereNotNull('end_date')
                                    ->where('end_date', '<', $now);
                            });
                    });
                }
            }

            // Apply position filter
            if ($position) {
                $query->where('position', $position);
            }

            // Apply page location filter
            if ($pageLocation) {
                $query->where('page_location', $pageLocation);
            }

            // Apply search
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', '%' . $search . '%')
                        ->orWhere('subtitle', 'like', '%' . $search . '%');
                });
            }

            // Apply sorting
            $query->orderBy($sortBy, $sortOrder);

            // Get paginated results
            $banners = $query->paginate($limit, ['*'], 'page', $page);

            return $this->success([
                'banners' => BannerResource::collection($banners),
                'pagination' => [
                    'total' => $banners->total(),
                    'per_page' => $banners->perPage(),
                    'current_page' => $banners->currentPage(),
                    'last_page' => $banners->lastPage(),
                    'from' => $banners->firstItem(),
                    'to' => $banners->lastItem()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Create a new banner
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'position' => 'required|string|max:50',
                'image_url' => 'required|string|max:255',
                'mobile_image_url' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'button_text' => 'nullable|string|max:100',
                'button_url' => 'nullable|string|max:255',
                'overlay_opacity' => 'nullable|numeric|min:0|max:1',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'is_active' => 'boolean',
                'target_audience' => 'nullable|array',
                'page_location' => 'required|string|max:100',
                'priority' => 'nullable|integer|min:0',
                'width' => 'nullable|integer|min:0',
                'height' => 'nullable|integer|min:0',
                'campaign_id' => 'nullable|integer|exists:seasonal_campaigns,id',
                'tracking_code' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Create banner
            $banner = new Banner();
            $banner->title = $request->input('title');
            $banner->subtitle = $request->input('subtitle');
            $banner->position = $request->input('position');
            $banner->image_url = $request->input('image_url');
            $banner->mobile_image_url = $request->input('mobile_image_url');
            $banner->background_color = $request->input('background_color');
            $banner->text_color = $request->input('text_color');
            $banner->button_text = $request->input('button_text');
            $banner->button_url = $request->input('button_url');
            $banner->overlay_opacity = $request->input('overlay_opacity');
            $banner->start_date = $request->input('start_date');
            $banner->end_date = $request->input('end_date');
            $banner->is_active = $request->input('is_active', true);
            $banner->target_audience = $request->input('target_audience');
            $banner->page_location = $request->input('page_location');
            $banner->priority = $request->input('priority', 0);
            $banner->width = $request->input('width');
            $banner->height = $request->input('height');
            $banner->campaign_id = $request->input('campaign_id');
            $banner->tracking_code = $request->input('tracking_code');
            $banner->impressions = 0;
            $banner->clicks = 0;
            $banner->conversion_rate = 0;
            
            $banner->save();

            DB::commit();
            return $this->success(new BannerResource($banner), 'Banner created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a banner by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $banner = Banner::with('campaign')->find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }

            return $this->success(new BannerResource($banner));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Update a banner
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'position' => 'nullable|string|max:50',
                'image_url' => 'nullable|string|max:255',
                'mobile_image_url' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'button_text' => 'nullable|string|max:100',
                'button_url' => 'nullable|string|max:255',
                'overlay_opacity' => 'nullable|numeric|min:0|max:1',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'is_active' => 'boolean',
                'target_audience' => 'nullable|array',
                'page_location' => 'nullable|string|max:100',
                'priority' => 'nullable|integer|min:0',
                'width' => 'nullable|integer|min:0',
                'height' => 'nullable|integer|min:0',
                'campaign_id' => 'nullable|integer|exists:seasonal_campaigns,id',
                'tracking_code' => 'nullable|string|max:100'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Update banner fields if they are provided
            if ($request->has('title')) {
                $banner->title = $request->input('title');
            }
            
            if ($request->has('subtitle')) {
                $banner->subtitle = $request->input('subtitle');
            }
            
            if ($request->has('position')) {
                $banner->position = $request->input('position');
            }
            
            if ($request->has('image_url')) {
                $banner->image_url = $request->input('image_url');
            }
            
            if ($request->has('mobile_image_url')) {
                $banner->mobile_image_url = $request->input('mobile_image_url');
            }
            
            if ($request->has('background_color')) {
                $banner->background_color = $request->input('background_color');
            }
            
            if ($request->has('text_color')) {
                $banner->text_color = $request->input('text_color');
            }
            
            if ($request->has('button_text')) {
                $banner->button_text = $request->input('button_text');
            }
            
            if ($request->has('button_url')) {
                $banner->button_url = $request->input('button_url');
            }
            
            if ($request->has('overlay_opacity')) {
                $banner->overlay_opacity = $request->input('overlay_opacity');
            }
            
            if ($request->has('start_date')) {
                $banner->start_date = $request->input('start_date');
            }
            
            if ($request->has('end_date')) {
                $banner->end_date = $request->input('end_date');
            }
            
            if ($request->has('is_active')) {
                $banner->is_active = $request->input('is_active');
            }
            
            if ($request->has('target_audience')) {
                $banner->target_audience = $request->input('target_audience');
            }
            
            if ($request->has('page_location')) {
                $banner->page_location = $request->input('page_location');
            }
            
            if ($request->has('priority')) {
                $banner->priority = $request->input('priority');
            }
            
            if ($request->has('width')) {
                $banner->width = $request->input('width');
            }
            
            if ($request->has('height')) {
                $banner->height = $request->input('height');
            }
            
            if ($request->has('campaign_id')) {
                $banner->campaign_id = $request->input('campaign_id');
            }
            
            if ($request->has('tracking_code')) {
                $banner->tracking_code = $request->input('tracking_code');
            }
            
            $banner->save();

            DB::commit();
            return $this->success(new BannerResource($banner), 'Banner updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Delete a banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }
            
            // Delete banner
            $banner->delete();

            DB::commit();
            return $this->success(null, 'Banner deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Change banner status (activate/deactivate)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $banner = Banner::find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }

            $banner->is_active = $request->input('is_active');
            $banner->save();

            return $this->success(new BannerResource($banner), 'Banner status updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get banner statistics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getStats(int $id): JsonResponse
    {
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }

            // Calculate status
            $now = now();
            $status = 'inactive';
            
            if ($banner->is_active) {
                if (($banner->start_date === null || $banner->start_date <= $now) && 
                    ($banner->end_date === null || $banner->end_date >= $now)) {
                    $status = 'active';
                } else if ($banner->start_date !== null && $banner->start_date > $now) {
                    $status = 'scheduled';
                } else if ($banner->end_date !== null && $banner->end_date < $now) {
                    $status = 'expired';
                }
            }

            // Format time remaining if active
            $timeRemaining = null;
            if ($status === 'active' && $banner->end_date !== null) {
                $timeRemaining = [
                    'seconds' => $now->diffInSeconds($banner->end_date, false),
                    'formatted' => $banner->end_date->diffForHumans(['parts' => 2])
                ];
            }

            return $this->success([
                'impressions' => $banner->impressions,
                'clicks' => $banner->clicks,
                'conversion_rate' => $banner->conversion_rate,
                'ctr' => $banner->impressions > 0 ? ($banner->clicks / $banner->impressions) * 100 : 0,
                'status' => $status,
                'time_remaining' => $timeRemaining,
                'created_at' => $banner->created_at->toIso8601String(),
                'updated_at' => $banner->updated_at->toIso8601String(),
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Reset banner statistics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function resetStats(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $banner = Banner::find($id);

            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }

            $banner->impressions = 0;
            $banner->clicks = 0;
            $banner->conversion_rate = 0;
            $banner->save();

            DB::commit();
            return $this->success(new BannerResource($banner), 'Banner statistics reset successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get banner positions
     *
     * @return JsonResponse
     */
    public function getPositions(): JsonResponse
    {
        try {
            $positions = Banner::distinct()->pluck('position')->values();
            
            return $this->success([
                'positions' => $positions
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get banner page locations
     *
     * @return JsonResponse
     */
    public function getPageLocations(): JsonResponse
    {
        try {
            $pageLocations = Banner::distinct()->pluck('page_location')->values();
            
            return $this->success([
                'page_locations' => $pageLocations
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 