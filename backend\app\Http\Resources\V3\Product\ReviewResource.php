<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($data) {
            return [
                'user_id'=> $data->user->id,
                'user_name'=> $data->user->name,
                'user_icon'=> uploaded_asset($data->user->avatar_original),
                'rating' => floatval(number_format($data->rating,1,'.','')),
                'comment' => $data->comment,
                'time' => $data->updated_at->format('M j, Y \A\T H:i')
            ];
        });
    }
}
