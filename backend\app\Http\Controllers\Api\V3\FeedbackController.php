<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\UserFeedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class FeedbackController extends ApiResponse
{
    /**
     * Submit feedback from users
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitFeedback(Request $request)
    {
        $messages = [
            'type.required' => translate('Feedback type is required'),
            'content.required' => translate('Feedback content is required'),
        ];

        $validator = Validator::make($request->all(), [
            'type' => 'required|string',
            'content' => 'required|string',
            'rating' => 'nullable|integer|between:1,5',
            'path' => 'nullable|string',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid information', $validator->errors()->messages(), 400);
        }

        try {
            $user = Auth::user();
            
            $feedback = new UserFeedback();
            $feedback->user_id = $user ? $user->id : null;
            $feedback->name = $user ? $user->name : 'Anonymous User';
            $feedback->email = $user ? $user->email : ($request->email ?? '<EMAIL>');
            $feedback->feedback = $request->content;
            $feedback->rate = $request->rating ?? 0;
            $feedback->feedback_type = $request->type;
            $feedback->path = $request->path;
            
            // Store additional metadata if provided
            if ($request->has('metadata') && is_array($request->metadata)) {
                $feedback->metadata = json_encode($request->metadata);
            }
            
            $feedback->save();

            return $this->success(null, 'Your feedback has been submitted successfully');
        } catch (\Throwable $th) {
            return $this->error('FEEDBACK_SUBMISSION_FAILED', 'Failed to submit feedback', $th->getMessage(), 400);
        }
    }

    /**
     * Get feedback entries (optionally filtered by user)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeedbackEntries(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Admin users can see all feedback, regular users only see their own
            $query = UserFeedback::query();
            
            if (!$user || !$user->hasRole('admin')) {
                $query->where('user_id', $user ? $user->id : null);
            }
            
            // Apply any filters
            if ($request->has('type')) {
                $query->where('feedback_type', $request->type);
            }
            
            // Sort by most recent
            $query->orderBy('created_at', 'desc');
            
            $feedbackEntries = $query->paginate(10);
            
            return $this->success([
                'entries' => $feedbackEntries->items(),
                'pagination' => [
                    'current_page' => $feedbackEntries->currentPage(),
                    'per_page' => $feedbackEntries->perPage(),
                    'total' => $feedbackEntries->total(),
                    'total_pages' => $feedbackEntries->lastPage()
                ]
            ], 'Feedback entries retrieved successfully');
        } catch (\Throwable $th) {
            return $this->error('FEEDBACK_RETRIEVAL_FAILED', 'Failed to retrieve feedback entries', $th->getMessage(), 400);
        }
    }
} 