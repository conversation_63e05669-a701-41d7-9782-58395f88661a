<?php

namespace App\Events;

use App\Models\UserNotification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminNotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $notification;
    public $adminUserType;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(UserNotification $notification, $adminUserType = 'admin')
    {
        $this->notification = $notification;
        $this->adminUserType = $adminUserType;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to admin channel for general admin notifications
        return [
            new PrivateChannel('admin.notifications'),
            new PrivateChannel('notifications.' . $this->notification->user_id)
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'admin.notification';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'id' => $this->notification->id,
            'type' => $this->notification->type,
            'title' => $this->notification->title,
            'message' => $this->notification->message,
            'date' => $this->notification->created_at->toIso8601String(),
            'read' => (bool) $this->notification->read,
            'priority' => $this->notification->priority,
            'link' => $this->notification->link,
            'linkText' => $this->notification->link_text,
            'user_id' => $this->notification->user_id,
            'subject_type' => $this->notification->subject_type,
            'subject_id' => $this->notification->subject_id,
            'admin_user_type' => $this->adminUserType,
        ];
    }
} 