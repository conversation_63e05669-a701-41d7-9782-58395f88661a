<?php

namespace App\Http\Resources\V3;

use App\Helpers\ProductPriceHelper;
use App\Http\Resources\V3\Product\ProductOftenBuyTogetherResource;
use App\Http\Resources\V3\Product\ProductVariantsResource;
use App\Http\Resources\V3\Product\ProductVatTaxesResource;
use App\Http\Resources\V3\Product\ProductVideosResource;
use App\Http\Resources\V3\Product\ReviewResource;
use App\Http\Resources\V3\Product\SkuResource;
use App\Models\Attribute;
use App\Models\Product;
use App\Models\Wishlist;
use App\Models\CompareProduct;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        try {
            $category = product_category_object($this);
            
            $prices = ProductPriceHelper::getProductPrices($this);
            
            // Get wishlist status if user is authenticated
            $wishlistStatus = $this->getWishlistStatus();
            
            // Get compare status if user is authenticated
            $compareStatus = $this->getCompareStatus();
            
            // Base common fields matching TypeScript Product interface
            $commonFields = [
                'id' => (string)$this->id,
                'title' => $this->getTranslation('name') ?? '',
                'name' => $this->getTranslation('name') ?? '',
                'slug' => $this->slug ?? '',
                'shortDescription' => strip_tags($this->short_description ?? ''),
                'description' => strip_tags($this->description ?? ''),
                'metaTitle' => $this->meta_title ?? '',
                'metaDescription' => $this->meta_description ?? '',
                'metaKeywords' => $this->meta_keywords ?? '',
                'metaImage' => $this->meta_image ? uploaded_asset($this->meta_image) : "",
                'price' => $prices['displayPrice'],
                'regularPrice' => $prices['regularPrice'],
                'b2bPrice' => $this->b2b_price ?? 0,
                'user_type' => auth('sanctum')->user() ? auth('sanctum')->user()->user_type : null,
                'has_discount' => $this->discount > 0,
                'discount_percent' => (float)$this->discount,
                'tax' => new ProductVatTaxesResource($this->taxes ?? collect()),
                'rating' => (float)($this->rating ?? 0),
                'reviewCount' => (int)$this->getApprovedReviewCount(),
                'stock' => $this->getTotalStockQuantity(),
                'brand' => product_brand_object($this),
                'categories' => is_array($category) ? $category : [$category],
                'thumbnail' => $this->thumbnail_img ? uploaded_asset($this->thumbnail_img) : "",
                'images' => $this->photos ? array_map(function ($image) {
                    return uploaded_asset(trim($image));
                }, explode(',', $this->photos)) : [],
                'videoUrl' => $this->product_videos ? $this->product_videos->pluck('url')->toArray() : [],
                'videoThumbnail' => $this->video_link ? $this->video_link : "",
                'createdAt' => $this->created_at,
                'updatedAt' => $this->updated_at,
                'tags' => array_map('trim', explode(',', $this->tags ?? '')),
                'isNew' => $this->created_at ? $this->created_at->diffInDays(now()) <= 7 : false,
                'isBestSeller' => (boolean)($this->best_seller ?? false),
                'isSale' => $prices['displayPrice'] < $prices['regularPrice'],
                'inStock' => $this->getTotalStockQuantity() > 0,
                'isInStock' => $this->getTotalStockQuantity() > 0,
                'isOutOfStock' => $this->getTotalStockQuantity() <= 0,
                'discount' => $prices['regularPrice'] > 0 ? round((($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice']) * 100) : 0,
                'ratingCount' => (int)$this->getApprovedRatingCount(),
                'specifications' => $this->specifications ? json_decode($this->specifications, true) : [],
                'isVariant' => (boolean)($this->variant_product ?? false),
                'variants' => $this->variant_product ? new ProductVariantsResource($this->stocks ?? collect()) : [],
                'isFeatured' => (boolean)($this->featured ?? false),
                'isTrending' => (boolean)($this->trending ?? false),
                'isPopular' => (boolean)($this->popular ?? false),
                'isTopRated' => (boolean)(($this->rating ?? 0) >= 4),
                'isLimitedStock' => $this->getTotalStockQuantity() <= 10,
                
                // Wishlist status - included in product data to avoid extra API calls
                'wishlistStatus' => $wishlistStatus,
                'inWishlist' => $wishlistStatus['inWishlist'],
                'wishlistItemId' => $wishlistStatus['wishlistItemId'],
                
                // Compare status - included in product data to avoid extra API calls
                'compareStatus' => $compareStatus,
                'inCompare' => $compareStatus['inCompare'],
                'compareItemId' => $compareStatus['compareItemId'],
                
                // Additional fields from the existing resource
                'unit' => $this->unit ?? '',
                'is_show_quantity' => $this->stock_visibility_state ?? false,
                'is_cash_on_delivery' => (boolean)($this->cash_on_delivery ?? false),
                'is_allow_customer_review' => (boolean)($this->allow_customer_review ?? true),
                'stock_request' => (boolean)($this->stock_request ?? false),
            ];

            if ($request && $request->route() && $request->route()->getActionMethod() === 'get_product_details') {
                if (is_string($this->choice_options)) {
                    $decodedOptions = json_decode($this->choice_options, true);
                } else {
                    $decodedOptions = $this->choice_options;
                }
                
                $shipping = [
                    'isFreeShipping' => $this->shipping_type == 'free' ? true : false,
                    'shippingType' => $this->shipping_type,
                    'shippingFee' => (float)($this->shipping_cost ?? 0),
                    'shippingTime' => (int)($this->est_shipping_days ?? 0),
                    'estimatedDelivery' => (int)$this->est_shipping_days,
                ];
                
                $relatedProducts = $this->often_buy_together ? array_map(function ($product) {
                    $productDetails = Product::findOrFail($product);
                    return new ProductOftenBuyTogetherResource($productDetails);
                }, json_decode($this->often_buy_together)) : [];
                
                $attributes = $this->convertToChoiceOptions($decodedOptions);
                
                return array_merge($commonFields, [
                    'minimumOrderQuantity' => (int)$this->min_qty,
                    'shipping' => $shipping,
                    'shippingFee' => (float)($this->shipping_cost ?? 0),
                    'shippingTime' => (int)($this->est_shipping_days ?? 0),
                    'numOfSale' => $this->num_of_sale ?? 0,
                    'sku' => optional($this->stocks)->pluck('sku')->filter()->implode(', ') ?? '',
                    'attributes' => $attributes,
                    'colors' => $this->colors ? json_decode($this->colors) : [],
                    'productAdditionalInformation' => $this->product_details ? json_decode($this->product_details) : [],
                    'relatedProducts' => $relatedProducts,
                    'features' => $this->features ? json_decode($this->features, true) : [],
                    'commissionRate' => (float)($this->commission_rate ?? 0),
                    'commissionValue' => (float)($this->commission_value ?? 0),
                ]);
            } else if ($request && $request->route() && $request->route()->getActionMethod() === 'get_product_specifications') {
                return [
                    'specifications' => $this->specifications ? json_decode($this->specifications, true) : [],
                ];
            } else if ($request && $request->route() && $request->route()->getActionMethod() === 'get_product_description') {
                return [
                    'shortDescription' => strip_tags($this->short_description ?? ''),
                    'description' => strip_tags($this->description ?? ''),
                ];
            } else if ($request && $request->route() && $request->route()->getActionMethod() === 'get_product_reviews') {
                return [
                    'reviews' => new ReviewResource($this->reviews ?? collect())
                ];
            } else {
                return $commonFields;
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('ProductResource error for product ID ' . ($this->id ?? 'unknown') . ': ' . $e->getMessage());
            
            // Return basic product info to prevent complete failure
            return [
                'id' => (string)($this->id ?? 0),
                'title' => $this->name ?? 'Unknown Product',
                'name' => $this->name ?? 'Unknown Product',
                'slug' => $this->slug ?? '',
                'price' => 0,
                'originalPrice' => 0,
                'rating' => 0,
                'stock' => 0,
                'thumbnail' => '',
                'images' => [],
                'inWishlist' => false,
                'wishlistItemId' => null,
                'inCompare' => false,
                'compareItemId' => null,
                'error' => 'Product data incomplete'
            ];
        }
    }

    /**
     * Get wishlist status for the current user
     *
     * @return array
     */
    protected function getWishlistStatus()
    {
        // Check if user is authenticated via Sanctum (for API requests)
        $user = auth()->user();
        
        if (!$user) {
            Log::info("ProductResource.getWishlistStatus: No authenticated user for product {$this->id}");
            return [
                'inWishlist' => false,
                'wishlistItemId' => null
            ];
        }

        try {
            Log::info("ProductResource.getWishlistStatus: Checking wishlist for user {$user->id} and product {$this->id}");
            
            $wishlistItem = Wishlist::where('user_id', $user->id)
                ->where('product_id', $this->id)
                ->first();

            $result = [
                'inWishlist' => $wishlistItem ? true : false,
                'wishlistItemId' => $wishlistItem ? $wishlistItem->id : null
            ];
            
            Log::info("ProductResource.getWishlistStatus: Result for product {$this->id}: " . json_encode($result));
            
            return $result;
        } catch (\Exception $e) {
            // In case of any error, return default values
            Log::warning('Error checking wishlist status for product ' . $this->id . ': ' . $e->getMessage());
            return [
                'inWishlist' => false,
                'wishlistItemId' => null
            ];
        }
    }

    /**
     * Get compare status for the current user
     *
     * @return array
     */
    protected function getCompareStatus()
    {
        // Check if user is authenticated via Sanctum (for API requests)
        $user = auth()->user();
        
        if (!$user) {
            Log::info("ProductResource.getCompareStatus: No authenticated user for product {$this->id}");
            return [
                'inCompare' => false,
                'compareItemId' => null
            ];
        }

        try {
            Log::info("ProductResource.getCompareStatus: Checking compare for user {$user->id} and product {$this->id}");
            
            $compareItem = CompareProduct::where('user_id', $user->id)
                ->where('product_id', $this->id)
                ->first();

            $result = [
                'inCompare' => $compareItem ? true : false,
                'compareItemId' => $compareItem ? $compareItem->id : null
            ];
            
            Log::info("ProductResource.getCompareStatus: Result for product {$this->id}: " . json_encode($result));

            return $result;
        } catch (\Exception $e) {
            // In case of any error, return default values
            Log::warning('Error checking compare status for product ' . $this->id . ': ' . $e->getMessage());
            return [
                'inCompare' => false,
                'compareItemId' => null
            ];
        }
    }

    protected function convertToChoiceOptions($data)
    {
        $result = array();
        if ($data) {
            foreach ($data as $key => $choice) {
                if (!isset($choice['attribute_id'])) continue;
                
                $attribute = Attribute::find($choice['attribute_id']);
                if (!$attribute) continue;
                
                $item['name'] = $choice['attribute_id'];
                $item['title'] = $attribute->getTranslation('name') ?? '';
                $item['options'] = $choice['values'] ?? [];
                $item['value'] = $choice['values'] ?? []; // Adding value field to match TypeScript interface
                array_push($result, $item);
            }
        }
        return $result;
    }
}
