<?php

namespace App\Http\Resources\V3\Notifications;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationCountsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'total' => $this->resource['total'],
            'unread' => $this->resource['unread'],
            'by_type' => $this->resource['by_type']
        ];
    }
}
