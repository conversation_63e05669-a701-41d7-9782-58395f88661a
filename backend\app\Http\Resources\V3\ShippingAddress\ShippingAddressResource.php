<?php

namespace App\Http\Resources\V3\ShippingAddress;

use Illuminate\Http\Resources\Json\JsonResource;

class ShippingAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $location_available = false;
        $lat = 90.99;
        $lang = 180.99;

        if($this->latitude || $this->longitude) {
            $location_available = true;
            $lat = floatval($this->latitude) ;
            $lang = floatval($this->longitude);
        }

        return [
            'id'      =>(int) $this->id,
            'fullName' => $this->receiver_name,
            'streetAddress' => $this->street_address,
            'streetAddress2' => $this->apartment_address,
            'city' => optional($this->city)->name,
            'state' => optional($this->state)->name,
            'zipCode' => $this->postal_code,
            'country' => optional($this->country)->name,
            'phone' => $this->phone,
            'isDefault' =>(boolean) $this->set_default,
            "type" =>"shipping",

            'apartment_address' => $this->apartment_address,
            'address' => $this->address,
            'country_id' => (int)  $this->country_id,
            'state_id' =>  (int) $this->state_id,
            'city_id' =>  (int) $this->city_id,
            'location_available' => $location_available,
            'lat' => $lat,
            'lang' => $lang,
        ];
    }
}
