<?php

namespace App\Http\Resources\V3\FlashSale;

use App\Http\Resources\V3\ProductMiniResource;
use Illuminate\Http\Resources\Json\JsonResource;

class FlashSaleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'start_date' => $this->start_date->toIso8601String(),
            'end_date' => $this->end_date->toIso8601String(),
            'is_active' => (bool) $this->is_active,
            'banner_image' => $this->banner_image,
            'background_color' => $this->background_color,
            'text_color' => $this->text_color,
            'countdown_style' => $this->countdown_style,
            'max_discount_percentage' => $this->max_discount_percentage,
            'product_limit' => $this->product_limit,
            'priority' => $this->priority,
            'sale_type' => $this->sale_type,
            'terms_conditions' => $this->terms_conditions,
            'is_featured' => (bool) $this->is_featured,
            'timer_position' => $this->timer_position,
            'progress_percentage' => $this->getProgressPercentage(),
            'time_remaining' => $this->when($this->end_date && $this->isActive(), function() {
                return $this->end_date->diffForHumans(['parts' => 2]);
            }),
            'products' => $this->when($this->relationLoaded('products'), function() {
                return ProductMiniResource::collection($this->products);
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 