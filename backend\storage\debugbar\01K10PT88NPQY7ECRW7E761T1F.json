{"__meta": {"id": "01K10PT88NPQY7ECRW7E761T1F", "datetime": "2025-07-25 04:52:07", "utime": **********.711864, "method": "GET", "uri": "/buzfi-new-backend/api/v3/cart", "ip": "::1"}, "messages": {"count": 11, "messages": [{"message": "[04:52:06] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/cart\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.955382, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:06] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.976165, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:06] LOG.debug: getTempUserId: Found temp user ID in header {\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.976483, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:06] LOG.info: Cart index - User identification {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.976564, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:06] LOG.info: Getting cart {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.976713, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:06] LOG.info: Getting or creating cart info {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.976798, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:07] LOG.info: Created new cart info {\n    \"cart_info_id\": \"189bb44e-62af-4653-a24c-7a6727d84ebf\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.557068, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:07] LOG.info: Found guest user cart items {\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"items_count\": 0,\n    \"items\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.577416, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:07] LOG.info: Getting saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.57954, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:07] LOG.info: Found saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.595961, "xdebug_link": null, "collector": "log"}, {"message": "[04:52:07] LOG.info: <PERSON>t retrieved successfully {\n    \"cart_info_id\": \"189bb44e-62af-4653-a24c-7a6727d84ebf\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753444149392_dvtv36i17\",\n    \"items_count\": 0,\n    \"saved_items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.596041, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.310224, "end": **********.71189, "duration": 2.4016659259796143, "duration_str": "2.4s", "measures": [{"label": "Booting", "start": **********.310224, "relative_start": 0, "end": **********.935487, "relative_end": **********.935487, "duration": 0.***************, "duration_str": "625ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.935498, "relative_start": 0.****************, "end": **********.711892, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.272622, "relative_start": 0.****************, "end": **********.371454, "relative_end": **********.371454, "duration": 0.****************, "duration_str": "98.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.606476, "relative_start": 2.****************, "end": **********.62123, "relative_end": **********.62123, "duration": 0.*****************, "duration_str": "14.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.653386, "relative_start": 2.****************, "end": **********.653431, "relative_end": **********.653431, "duration": 4.482269287109375e-05, "duration_str": "45μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09912, "accumulated_duration_str": "99.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `cart_info` where `temp_user_id` = 'temp_1753444149392_dvtv36i17' and `status` = 'active' and `user_id` is null limit 1", "type": "query", "params": [], "bindings": ["temp_1753444149392_dvtv36i17", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, {"index": 17, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.027886, "duration": 0.07375, "duration_str": "73.75ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:125", "source": {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=125", "ajax": false, "filename": "EnhancedCartService.php", "line": "125"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 74.405}, {"sql": "insert into `cart_info` (`user_id`, `temp_user_id`, `status`, `currency`, `last_activity_at`, `session_id`, `expires_at`, `id`, `updated_at`, `created_at`) values (null, 'temp_1753444149392_dvtv36i17', 'active', 'USD', '2025-07-25 04:52:07', 'Bbq8TJ4WNNS7dzXtO8OMiItX5Jp07jOkLn4J7ReD', '2025-08-24 04:52:07', '189bb44e-62af-4653-a24c-7a6727d84ebf', '2025-07-25 04:52:07', '2025-07-25 04:52:07')", "type": "query", "params": [], "bindings": [null, "temp_1753444149392_dvtv36i17", "active", "USD", "2025-07-25 04:52:07", "Bbq8TJ4WNNS7dzXtO8OMiItX5Jp07jOkLn4J7ReD", "2025-08-24 04:52:07", "189bb44e-62af-4653-a24c-7a6727d84ebf", "2025-07-25 04:52:07", "2025-07-25 04:52:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 143}, {"index": 14, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.546612, "duration": 0.009810000000000001, "duration_str": "9.81ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:143", "source": {"index": 13, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=143", "ajax": false, "filename": "EnhancedCartService.php", "line": "143"}, "connection": "buzfi", "explain": null, "start_percent": 74.405, "width_percent": 9.897}, {"sql": "select * from `carts` where `temp_user_id` = 'temp_1753444149392_dvtv36i17' and `status` = 'active' and `user_id` is null", "type": "query", "params": [], "bindings": ["temp_1753444149392_dvtv36i17", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.569381, "duration": 0.0074, "duration_str": "7.4ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:207", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=207", "ajax": false, "filename": "EnhancedCartService.php", "line": "207"}, "connection": "buzfi", "explain": null, "start_percent": 84.302, "width_percent": 7.466}, {"sql": "update `cart_info` set `item_count` = null, `total_quantity` = null, `subtotal` = 0, `tax_total` = 0, `shipping_total` = 0, `total` = 0, `cart_info`.`updated_at` = '2025-07-25 04:52:07' where `id` = '189bb44e-62af-4653-a24c-7a6727d84ebf'", "type": "query", "params": [], "bindings": [null, null, 0, 0, 0, 0, "2025-07-25 04:52:07", "189bb44e-62af-4653-a24c-7a6727d84ebf"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 1341}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 220}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5778441, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:1341", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 1341}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=1341", "ajax": false, "filename": "EnhancedCartService.php", "line": "1341"}, "connection": "buzfi", "explain": null, "start_percent": 91.768, "width_percent": 1.221}, {"sql": "select * from `saved_items` where `temp_user_id` = 'temp_1753444149392_dvtv36i17' and `user_id` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["temp_1753444149392_dvtv36i17"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 223}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.588423, "duration": 0.0069500000000000004, "duration_str": "6.95ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:864", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=864", "ajax": false, "filename": "EnhancedCartService.php", "line": "864"}, "connection": "buzfi", "explain": null, "start_percent": 92.988, "width_percent": 7.012}]}, "models": {"data": {"App\\Models\\CartInfo": {"created": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCartInfo.php&line=1", "ajax": false, "filename": "CartInfo.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index", "uri": "GET api/v3/cart", "controller": "App\\Http\\Controllers\\Api\\V3\\CartController@index<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/CartController.php:28-68</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "2.41s", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-946452226 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-946452226\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2039011119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2039011119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1562153451 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-temp-user-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">temp_1753444149392_dvtv36i17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost:3000/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"313 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; XSRF-TOKEN=7l95OQdFUCPibkIA6gnZqEwd5mkPKBGbJ3MH0FGn; buzficom_session=M339ZD3C0gHYBXaq9xfd2TIIgMDFj7fsAc7WNdYl; __next_hmr_refresh_hash__=f37add1e9228ad5c8679f093f4e43dc51a185cb7324bd881; __stripe_sid=b5c4bfe7-047e-4529-9136-7a4ce9b63e975bca82</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562153451\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2020893744 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2020893744\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2095934687 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 11:52:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">599</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjgxREo0eE1NTHNod0JqZUJKWG9hRVE9PSIsInZhbHVlIjoiT1dYQkhrbGhiWGxVTHZpWFB1dUVJU1JubmpqN0RwV0VNTGZSQWtNRGl3RVhtOXQzeHdiN3p5QVZXRXovSTlMK3Rmc0l4VDJuUUJjVnRwWWl6NkVobTdZdVNPRUZGYW9JWGkyQVFnN1VuZjIzclJveFFLcStOVFlZOU4xL2lodUMiLCJtYWMiOiI0NDBiMzE5MzBlNjMwMjcyYTZiYTU1MDA3ZTE0Mjg5OGRlY2E3Mzg2ODYxOGMwMDk3NjQxYzE2NTFlNTEyMjhjIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 13:52:07 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IllsSTVYakNvSTVKV1F6cmZEZENRT3c9PSIsInZhbHVlIjoiZVpKQ0Y2OTRoM0JxNSt2YjRpRmZpMi9xbjdaUEtnVjZPZUJuTDFxcFFuZ1BZTGxRMi9kcGtEZ1hacjNEVDYxVTI4YWY4d0piOEJrak90UlB0K09wVWNMMkZhY3pUd2JyeVVVN0lCSzFsZnJURm9rWVBDRkk0ZEM2ZEhCTDJvRU4iLCJtYWMiOiI1M2Q2OGUwZWRmNTljM2YwMGVmYzVkNGM2ODU4Njg4ZWVmYTNlMTU0MmQ0MTI4ZDg2YzEzN2U3MGY2YzUzNmU4IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 13:52:07 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjgxREo0eE1NTHNod0JqZUJKWG9hRVE9PSIsInZhbHVlIjoiT1dYQkhrbGhiWGxVTHZpWFB1dUVJU1JubmpqN0RwV0VNTGZSQWtNRGl3RVhtOXQzeHdiN3p5QVZXRXovSTlMK3Rmc0l4VDJuUUJjVnRwWWl6NkVobTdZdVNPRUZGYW9JWGkyQVFnN1VuZjIzclJveFFLcStOVFlZOU4xL2lodUMiLCJtYWMiOiI0NDBiMzE5MzBlNjMwMjcyYTZiYTU1MDA3ZTE0Mjg5OGRlY2E3Mzg2ODYxOGMwMDk3NjQxYzE2NTFlNTEyMjhjIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 13:52:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IllsSTVYakNvSTVKV1F6cmZEZENRT3c9PSIsInZhbHVlIjoiZVpKQ0Y2OTRoM0JxNSt2YjRpRmZpMi9xbjdaUEtnVjZPZUJuTDFxcFFuZ1BZTGxRMi9kcGtEZ1hacjNEVDYxVTI4YWY4d0piOEJrak90UlB0K09wVWNMMkZhY3pUd2JyeVVVN0lCSzFsZnJURm9rWVBDRkk0ZEM2ZEhCTDJvRU4iLCJtYWMiOiI1M2Q2OGUwZWRmNTljM2YwMGVmYzVkNGM2ODU4Njg4ZWVmYTNlMTU0MmQ0MTI4ZDg2YzEzN2U3MGY2YzUzNmU4IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 13:52:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095934687\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1700265797 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7l95OQdFUCPibkIA6gnZqEwd5mkPKBGbJ3MH0FGn</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/buzfi-new-backend/api/v3/cart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>temp_user_id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">temp_1753444149392_dvtv36i17</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700265797\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index"}, "badge": null}}