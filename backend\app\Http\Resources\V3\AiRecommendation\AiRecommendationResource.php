<?php

namespace App\Http\Resources\V3\AiRecommendation;

use App\Http\Resources\V3\ProductMiniResource;
use Illuminate\Http\Resources\Json\JsonResource;

class AiRecommendationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'recommendation_type' => $this->recommendation_type,
            'algorithm_used' => $this->algorithm_used,
            'title' => $this->title,
            'description' => $this->description,
            'product_ids' => $this->product_ids,
            'category_ids' => $this->category_ids,
            'confidence_score' => $this->confidence_score,
            'is_personalized' => (bool) $this->is_personalized,
            'status' => $this->status,
            'generated_date' => isset($this->generated_date) ? $this->generated_date->toIso8601String() : null,
            'shown_date' => isset($this->shown_date) ? $this->shown_date->toIso8601String() : null,
            'clicked_date' => isset($this->clicked_date) ? $this->clicked_date->toIso8601String() : null,
            'conversion_date' => isset($this->conversion_date) ? $this->conversion_date->toIso8601String() : null,
            'click_through_rate' => $this->getClickThroughRate(),
            'conversion_rate' => $this->getConversionRate(),
            'products' => $this->when($this->relationLoaded('products'), function() {
                return ProductMiniResource::collection($this->products);
            }),
            'user' => $this->when($this->relationLoaded('user') && $this->user, function() {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                ];
            }),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 