<?php

namespace App\Http\Controllers;

use App\Http\Requests\OfferFormRequest;
use App\Models\Offer;
use App\Models\Category;
use App\Models\Product;
use App\Models\Brand;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Session;

class OfferController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $offers = Offer::orderBy('created_at', 'desc');

        if ($request->filled('search')) {
            $search = $request->search;
            $offers->where(function($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('promo_code', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $offers->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $offers->where('type', $request->type);
        }

        $offers = $offers->paginate(15);
        return view('backend.marketing.offers.index', compact('offers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::all();
        $products = Product::all();
        $brands = Brand::all();
        $customers = User::where('user_type', 'customer')->get();
        return view('backend.marketing.offers.create', compact('categories', 'products', 'brands', 'customers'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(OfferFormRequest $request)
    {
        $offer = new Offer();
        $offer->title = $request->title;
        $offer->description = $request->description;
        $offer->type = $request->type;
        $offer->discount_type = $request->discount_type;
        $offer->discount_value = $request->discount_value;
        $offer->start_date = $request->start_date;
        $offer->end_date = $request->end_date;
        $offer->promo_code = $request->promo_code;
        $offer->usage_limit = $request->usage_limit;
        $offer->min_order_amount = $request->min_order_amount;
        $offer->max_discount_amount = $request->max_discount_amount;
        $offer->status = $request->status;
        $offer->priority = $request->priority ?? 0;
        $offer->user_type = $request->user_type ?? 'all';
        $offer->is_exclusive = $request->boolean('is_exclusive');
        $offer->is_featured = $request->boolean('is_featured');
        $offer->offer_banner = $request->offer_banner;

        // Handle bulk discounts
        if ($request->filled('bulk_discounts')) {
            $offer->bulk_discounts = json_encode($request->bulk_discounts);
        }

        $offer->save();

        // Attach categories
        if ($request->filled('category_ids')) {
            $offer->categories()->attach($request->category_ids);
        }

        // Attach products
        if ($request->filled('product_ids')) {
            $offer->products()->attach($request->product_ids);
        }

        // Attach brands
        if ($request->filled('brand_ids')) {
            $offer->brands()->attach($request->brand_ids);
        }

        // Attach customers
        if ($request->filled('customer_ids')) {
            $offer->customers()->attach($request->customer_ids);
        }

        flash(translate('Offer has been created successfully'))->success();
        return redirect()->route('offers.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $offer = Offer::findOrFail($id);
        return view('backend.marketing.offers.show', compact('offer'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $offer = Offer::findOrFail($id);
        $selectedOftenBuyTogether = [];
        $categories = Category::all();
      /*  $products = Product::all();
        $brands = Brand::all();
        $customers = User::where('user_type', 'customer')->get();*/

       // return view('backend.marketing.offers.edit', compact('offer', 'categories', 'products', 'brands', 'customers'));
        return view('backend.marketing.offers.edit', compact('offer','selectedOftenBuyTogether','categories'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(OfferFormRequest $request, $id)
    {
        $offer = Offer::findOrFail($id);
        $offer->title = $request->title;
        $offer->description = $request->description;
        $offer->offer_banner = $request->offer_banner;
        $offer->promo_code = $request->promo_code;
        $offer->user_type = $request->user_type ?? 'all';
        $offer->discount_type = $request->discount_type;

        // Handle discount amount based on type
        if ($request->discount_type == 'percentage') {
            $offer->discount_percentage = $request->discount_percentage;
            $offer->discount_amount = null;
        } else {
            $offer->discount_amount = $request->discount_amount;
            $offer->discount_percentage = null;
        }

        $offer->min_order_value = $request->min_order_value;
        $offer->priority = $request->priority ?? 0;
        $offer->start_date = $request->start_date;
        $offer->end_date = $request->end_date;
        $offer->usage_limit = $request->usage_limit;
        $offer->savings_amount = $request->savings_amount;
        $offer->terms_conditions = $request->terms_conditions;
        $offer->status = $request->status ?? 'active';

        // Handle tags
        if ($request->filled('tags')) {
            $tags = $request->tags;
            if (is_string($tags)) {
                // If it's already JSON, keep it as is, otherwise convert array to JSON
                $decodedTags = json_decode($tags, true);
                $offer->tags = is_array($decodedTags) ? $tags : json_encode([$tags]);
            } elseif (is_array($tags)) {
                $offer->tags = json_encode($tags);
            }
        } else {
            $offer->tags = null;
        }

        // Boolean fields - use Laravel's boolean helper which handles checkboxes properly
        $offer->is_exclusive = $request->boolean('is_exclusive');
        $offer->is_seasonal = $request->boolean('is_seasonal');
        $offer->is_personalized = $request->boolean('is_personalized');
        $offer->is_dropshipper_only = $request->boolean('is_dropshipper_only');

        // Handle bulk discounts
        if ($request->filled('bulk_discount_quantities') && $request->filled('bulk_discount_percentages')) {
            $quantities = $request->bulk_discount_quantities;
            $percentages = $request->bulk_discount_percentages;
            $bulkDiscounts = [];

            for ($i = 0; $i < count($quantities); $i++) {
                if (isset($quantities[$i]) && isset($percentages[$i]) &&
                    !empty($quantities[$i]) && !empty($percentages[$i])) {
                    $bulkDiscounts[] = [
                        'quantity' => (int)$quantities[$i],
                        'additional_discount' => (float)$percentages[$i]
                    ];
                }
            }

            $offer->bulk_discounts = !empty($bulkDiscounts) ? json_encode($bulkDiscounts) : null;
        } else {
            $offer->bulk_discounts = null;
        }

        $offer->save();

        // Sync categories
        if ($request->filled('categories')) {
            $offer->categories()->sync($request->categories);
        } else {
            $offer->categories()->detach();
        }

        // Sync products
        if ($request->filled('products')) {
            $offer->products()->sync($request->products);
        } else {
            $offer->products()->detach();
        }

        flash(translate('Offer has been updated successfully'))->success();
        return redirect()->route('offers.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $offer = Offer::findOrFail($id);

        // Detach all relationships
        $offer->categories()->detach();
        $offer->products()->detach();
        $offer->brands()->detach();
        $offer->customers()->detach();

        $offer->delete();

        if (request()->expectsJson()) {
            return response()->json(['message' => translate('Offer has been deleted successfully')]);
        }

        flash(translate('Offer has been deleted successfully'))->success();
        return redirect()->route('offers.index');
    }

    /**
     * Bulk delete offers
     */
    public function bulk_offer_delete(Request $request)
    {
        if ($request->id) {
            foreach ($request->id as $offer_id) {
                $offer = Offer::find($offer_id);
                if ($offer) {
                    // Detach all relationships
                    $offer->categories()->detach();
                    $offer->products()->detach();
                    $offer->brands()->detach();
                    $offer->customers()->detach();

                    $offer->delete();
                }
            }
        }
        return 1;
    }

    /**
     * Update offer status
     */
    public function updateStatus(Request $request)
    {
        $offer = Offer::findOrFail($request->id);
        $offer->status = $request->status;
        $offer->save();

        return response()->json(['message' => translate('Status updated successfully')]);
    }

    /**
     * Get offers by date range for analytics
     */
    public function getOffersByDateRange(Request $request)
    {
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

        $offers = Offer::whereBetween('created_at', [$startDate, $endDate])
                      ->orderBy('created_at', 'desc')
                      ->get();

        return response()->json($offers);
    }

    /**
     * Get offer statistics
     */
    public function getStats()
    {
        $stats = [
            'total_offers' => Offer::count(),
            'active_offers' => Offer::where('status', 'active')->count(),
            'expired_offers' => Offer::where('end_date', '<', Carbon::now())->count(),
            'upcoming_offers' => Offer::where('start_date', '>', Carbon::now())->count(),
        ];

        return response()->json($stats);
    }
}
