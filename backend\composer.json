{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "aiz-packages/color-code-converter": "dev-main", "aiz-packages/combination-generate": "dev-main", "anandsiddharth/laravel-paytm-wallet": "^2.0", "authorizenet/authorizenet": "^2.0", "doctrine/dbal": "^3.9", "enshrined/svg-sanitize": "^0.15.4", "ezyang/htmlpurifier": "^4.18", "genealabs/laravel-sign-in-with-apple": "*", "genealabs/laravel-socialiter": "*", "giggsey/libphonenumber-for-php": "^9.0", "guzzlehttp/guzzle": "^7.5", "instamojo/instamojo-php": "^0.4.0", "intervention/image": "^2.5", "iyzico/iyzipay-php": "^2.0", "jenssegers/agent": "^2.6", "kingflamez/laravelrave": "dev-master", "laracasts/flash": "^3.2", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.8", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "lcobucci/jwt": "^4.0", "league/flysystem-aws-s3-v3": "^3.12", "maatwebsite/excel": "^3.1", "mehedi-iitdu/core-component-repository": "^2.2", "mercadopago/dx-php": "^2.5", "mpdf/mpdf": "^8.1", "myfatoorah/laravel-package": "^2.1", "niklasravnsborg/laravel-pdf": "^4.1", "paypal/paypal-checkout-sdk": "dev-master", "predis/predis": "^2.1", "psr/log": "^2.0", "pusher/pusher-php-server": "^7.2", "rap2hpoutre/laravel-log-viewer": "^2.5", "razorpay/razorpay": "2.*", "rmccue/requests": "^2.0", "sebacarrasco93/laravel-payku": "^1.0", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/db-dumper": "^3.3", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-permission": "^5.5", "stripe/stripe-php": "^10.5", "twilio/sdk": "^6.44", "unicodeveloper/laravel-paystack": "^1.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "psr-4": {"App\\": "app/"}, "files": ["app/Http/Helpers.php", "app/Http/ProductHelpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"symfony/thanks": true}}, "minimum-stability": "dev", "prefer-stable": true}