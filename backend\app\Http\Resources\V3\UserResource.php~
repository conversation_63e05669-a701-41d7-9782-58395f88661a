<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            //'id' => $this->id,
            'type' => (string)$this->user_type,
            'name' => (string)$this->name,
            'email' => (string)$this->email,
            'avatar' => (string)$this->avatar,
            'avatar_original' => (string)uploaded_asset($this->avatar_original),
            'phone' => (string)$this->phone,
            'email_verified' => (bool)$this->email_verified_at != null
        ];
    }
}
