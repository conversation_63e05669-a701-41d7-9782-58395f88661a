<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Product;

class PriceAlertEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;
    public $alertData;
    public $product;
    public $oldPrice;
    public $newPrice;

    /**
     * Create a new event instance.
     *
     * @param int $userId
     * @param array $alertData
     * @param Product $product
     * @param float $oldPrice
     * @param float $newPrice
     * @return void
     */
    public function __construct($userId, $alertData, $product, $oldPrice, $newPrice)
    {
        $this->userId = $userId;
        $this->alertData = $alertData;
        $this->product = $product;
        $this->oldPrice = $oldPrice;
        $this->newPrice = $newPrice;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('price-alerts.' . $this->userId);
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'price.alert';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $percentageOff = round((($this->oldPrice - $this->newPrice) / $this->oldPrice) * 100);
        
        return [
            'alert_id' => $this->alertData['id'],
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'product_image' => $this->product->thumbnail,
            'product_slug' => $this->product->slug,
            'old_price' => $this->oldPrice,
            'new_price' => $this->newPrice,
            'saved_amount' => $this->oldPrice - $this->newPrice,
            'percentage_off' => $percentageOff,
            'alert_type' => $this->alertData['alertType']
        ];
    }
} 