<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ShowRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:show-request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show the request data for debugging';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating middleware to log cart requests...');
        
        // Create the middleware file
        $middlewarePath = app_path('Http/Middleware/LogCartRequests.php');
        
        $middlewareContent = <<<'EOT'
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LogCartRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only log cart-related requests
        if (strpos($request->path(), 'api/v3/cart') !== false || 
            strpos($request->path(), 'api/v3/add-to-cart') !== false) {
            
            // Log the request data
            Log::channel('api_cart')->info('Cart API Request', [
                'method' => $request->method(),
                'path' => $request->path(),
                'url' => $request->fullUrl(),
                'payload' => $request->all(),
                'headers' => $request->header(),
                'user_id' => auth()->check() ? auth()->id() : 'guest',
                'ip' => $request->ip(),
            ]);
        }

        return $next($request);
    }
}
EOT;

        file_put_contents($middlewarePath, $middlewareContent);
        $this->info('Created middleware: ' . $middlewarePath);
        
        // Instructions for registering the middleware
        $this->info('Now you need to register the middleware in app/Http/Kernel.php:');
        $this->info('Add the following to the $middleware array:');
        $this->info('\App\Http\Middleware\LogCartRequests::class,');
        
        $this->info('After registering, API cart requests will be logged to the api_cart channel.');
    }
}
