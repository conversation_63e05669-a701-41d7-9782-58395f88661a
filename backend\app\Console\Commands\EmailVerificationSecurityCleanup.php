<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\EmailVerificationSecurityService;
use App\Models\EmailVerify;
use Illuminate\Support\Facades\Log;

class EmailVerificationSecurityCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-verification:security-cleanup 
                            {--force : Force cleanup without confirmation}
                            {--stats : Show statistics only}
                            {--batch-size=1000 : Batch size for cleanup operations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired email verification records and security data';

    /**
     * Email verification security service
     *
     * @var EmailVerificationSecurityService
     */
    protected $securityService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(EmailVerificationSecurityService $securityService)
    {
        parent::__construct();
        $this->securityService = $securityService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Email Verification Security Cleanup');
        $this->info('=====================================');

        // Show statistics if requested
        if ($this->option('stats')) {
            $this->showStatistics();
            return 0;
        }

        // Get current statistics
        $beforeStats = $this->securityService->getSecurityStatistics();
        $this->displayStatistics('Before Cleanup', $beforeStats);

        // Confirm cleanup unless forced
        if (!$this->option('force')) {
            if (!$this->confirm('Do you want to proceed with the cleanup?')) {
                $this->info('Cleanup cancelled.');
                return 0;
            }
        }

        $this->info('Starting cleanup operations...');

        // Perform cleanup operations
        $results = $this->performCleanup();

        // Display results
        $this->displayCleanupResults($results);

        // Get updated statistics
        $afterStats = $this->securityService->getSecurityStatistics();
        $this->displayStatistics('After Cleanup', $afterStats);

        $this->info('Cleanup completed successfully!');
        
        return 0;
    }

    /**
     * Show current statistics
     */
    protected function showStatistics()
    {
        $stats = $this->securityService->getSecurityStatistics();
        $this->displayStatistics('Current Statistics', $stats);
    }

    /**
     * Perform all cleanup operations
     */
    protected function performCleanup(): array
    {
        $results = [];

        try {
            // 1. Clean expired OTPs
            $this->info('Cleaning expired OTPs...');
            $results['expired_otps'] = EmailVerify::batchCleanupExpiredOTPs(
                $this->option('batch-size')
            );
            $this->line("  → Cleaned {$results['expired_otps']} expired OTPs");

            // 2. Delete old unverified records
            $this->info('Deleting old unverified records...');
            $results['old_records'] = EmailVerify::batchDeleteOldRecords(
                1, // 1 day old
                $this->option('batch-size')
            );
            $this->line("  → Deleted {$results['old_records']} old records");

            // 3. Reset expired resend counts
            $this->info('Resetting expired resend counts...');
            $results['reset_resend_counts'] = EmailVerify::resetExpiredResendCounts();
            $this->line("  → Reset {$results['reset_resend_counts']} resend counts");

            // 4. Clean up rate limiting data (if using Redis)
            $this->info('Cleaning rate limiting data...');
            $results['rate_limit_cleanup'] = $this->cleanupRateLimitData();
            $this->line("  → Cleaned rate limiting data");

            // Log cleanup results
            Log::info('Email verification security cleanup completed via command', $results);

        } catch (\Exception $e) {
            $this->error('Cleanup failed: ' . $e->getMessage());
            Log::error('Email verification security cleanup command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return ['error' => $e->getMessage()];
        }

        return $results;
    }

    /**
     * Clean up rate limiting data
     */
    protected function cleanupRateLimitData(): bool
    {
        try {
            // This would depend on your cache driver
            // For Redis, you might use SCAN to find and delete expired keys
            // For now, we'll just return true as a placeholder
            return true;
        } catch (\Exception $e) {
            Log::warning('Rate limit cleanup failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display cleanup results
     */
    protected function displayCleanupResults(array $results)
    {
        $this->info('Cleanup Results:');
        $this->info('================');

        if (isset($results['error'])) {
            $this->error('Error: ' . $results['error']);
            return;
        }

        $this->table(
            ['Operation', 'Count'],
            [
                ['Expired OTPs Cleaned', $results['expired_otps'] ?? 0],
                ['Old Records Deleted', $results['old_records'] ?? 0],
                ['Resend Counts Reset', $results['reset_resend_counts'] ?? 0],
                ['Rate Limit Data Cleaned', $results['rate_limit_cleanup'] ? 'Yes' : 'No']
            ]
        );
    }

    /**
     * Display statistics in a formatted table
     */
    protected function displayStatistics(string $title, array $stats)
    {
        $this->info($title . ':');
        $this->info(str_repeat('=', strlen($title) + 1));

        if (empty($stats)) {
            $this->warn('No statistics available');
            return;
        }

        $tableData = [];

        // Basic statistics
        if (isset($stats['expired_otps_count'])) {
            $tableData[] = ['Expired OTPs', $stats['expired_otps_count']];
        }
        if (isset($stats['old_unverified_count'])) {
            $tableData[] = ['Old Unverified Records', $stats['old_unverified_count']];
        }
        if (isset($stats['high_resend_count'])) {
            $tableData[] = ['High Resend Count Records', $stats['high_resend_count']];
        }
        if (isset($stats['total_verified'])) {
            $tableData[] = ['Total Verified', $stats['total_verified']];
        }
        if (isset($stats['total_pending'])) {
            $tableData[] = ['Total Pending', $stats['total_pending']];
        }

        // Rate limiting statistics
        if (isset($stats['rate_limits'])) {
            $rateLimits = $stats['rate_limits'];
            $tableData[] = ['Active Email Limits', $rateLimits['active_email_limits'] ?? 0];
            $tableData[] = ['Active IP Limits', $rateLimits['active_ip_limits'] ?? 0];
            $tableData[] = ['Blocked IPs', $rateLimits['blocked_ips'] ?? 0];
            $tableData[] = ['Blocked Emails', $rateLimits['blocked_emails'] ?? 0];
        }

        if (!empty($tableData)) {
            $this->table(['Metric', 'Count'], $tableData);
        }

        $this->line('');
    }
}