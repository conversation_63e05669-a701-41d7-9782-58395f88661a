<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V2\Seller\CategoriesCollection;
use App\Http\Resources\V3\Categories\CategoriesResource;
use App\Http\Resources\V3\Categories\CategoryResource;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiCategoryController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();

    }
    public function index(Request $request){
        $per_page = min((int)$request->input('per_page', 8), 10);
        $page = max((int)$request->input('page', 1), 1);
        $include_inactive=$request->input('include_inactive', false);
        $featured=$request->input('featured', true);
        $include_subcategories=$request->input('include_subcategories', true);
        $include_products=$request->input('include_products', false);
        $product_limit = min((int)$request->input('product_limit', 4), 8);
        $categories= Category::with('childrenCategories');
        if($include_inactive == false){
            $categories=$categories->where('is_visible', 1);
        }
        if($featured){
            $categories=$categories->where('featured', 1);
        }
        $categories=$categories->where('parent_id', 0);
        $paginatedCategories = $categories->paginate($per_page);
        $total_items = $paginatedCategories->total();
        $total_pages = $paginatedCategories->lastPage();

        $data = [
            'categories' => new CategoriesResource($paginatedCategories),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function details(Request $request)
    {
        $messages = array(
            'slug.required' => translate('Please enter a valid Category slug'),
            'slug.exists' => translate('Invalid Category slug.Category not found'),
        );
        $validator = Validator::make($request->all(), [
            'slug' => 'required|exists:categories,slug',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('CATEGORY_NOT_FOUND', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $category = Category::where('slug', $request->input('slug'))->first();
        return $this->success(
            New CategoryResource($category),
            translate('Category details fetched successfully')
        );
    }
    public function categories_tree(Request $request){
        $include_inactive=$request->input('include_inactive', false);
        $categories= Category::with('childrenCategories');
        $categories->where('parent_id', 0);
        if($include_inactive == false){
            $categories=$categories->where('is_visible', 1);
        }
        return $this->success(
            New CategoriesResource($categories->get()),
            translate('Category details fetched successfully')
        );
    }
}
