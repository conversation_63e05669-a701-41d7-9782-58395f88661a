<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\CostBreakdownResource;
use App\Http\Resources\V3\SavingsGoalResource;
use App\Services\SavingsTrackerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiSavingsTrackerController extends ApiResponse
{
    protected $savingsTrackerService;

    /**
     * Create a new controller instance.
     *
     * @param SavingsTrackerService $savingsTrackerService
     * @return void
     */
    public function __construct(SavingsTrackerService $savingsTrackerService)
    {
        parent::__construct();
        $this->savingsTrackerService = $savingsTrackerService;
    }

    /**
     * Get the current user's savings goal
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGoal()
    {
        try {
            $goal = $this->savingsTrackerService->getUserGoal();

            if (!$goal) {
                return $this->error(
                    'NOT_FOUND',
                    'No savings goal found',
                    '',
                    404
                );
            }

            return $this->success(new SavingsGoalResource($goal));
        } catch (\Exception $e) {
            dd($e);
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving the savings goal',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Set or update the user's savings goal
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setGoal(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'goalAmount' => 'required|numeric|min:1',
            'targetDate' => 'required|date|after:today',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            list($goal, $isNew) = $this->savingsTrackerService->setUserGoal($request->all());

            return $this->success(
                new SavingsGoalResource($goal),
                $isNew ? 'Savings goal created successfully' : 'Savings goal updated successfully',
                $isNew ? 201 : 200
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while setting the savings goal',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Update the current amount of the user's savings goal
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAmount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'currentAmount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $goal = $this->savingsTrackerService->updateCurrentAmount($request->currentAmount);

            if (!$goal) {
                return $this->error(
                    'NOT_FOUND',
                    'No savings goal found',
                    '',
                    404
                );
            }

            return $this->success(
                new SavingsGoalResource($goal),
                'Savings amount updated successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while updating the savings amount',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Delete the user's savings goal
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteGoal()
    {
        try {
            $result = $this->savingsTrackerService->deleteUserGoal();

            if (!$result) {
                return $this->error(
                    'NOT_FOUND',
                    'No savings goal found',
                    '',
                    404
                );
            }

            return $this->success(
                null,
                'Savings goal deleted successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while deleting the savings goal',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get cost breakdown for a specific period
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCostBreakdown(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'nullable|string|in:month,quarter,year',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }

            $period = $request->input('period', 'month');
            $costBreakdown = $this->savingsTrackerService->getCostBreakdown($period);

            return $this->success(new CostBreakdownResource($costBreakdown));
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving the cost breakdown',
                $e->getMessage(),
                500
            );
        }
    }
    /**
     * Get cost breakdown for a specific period
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSavingsOrderBreakdown(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'period' => 'nullable|string|in:month,quarter,year',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }


            $period = $request->input('period', 'month');
            $per_page = $request->input('per_page', '10');
            $costBreakdown = $this->savingsTrackerService->getSavingsOrderBreakdown($period,$per_page);

            return $this->success($costBreakdown);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving the cost breakdown',
                $e->getMessage(),
                500
            );
        }
    }/**
     * Get cost breakdown for a specific period
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function getMonthlySavingsTrend(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'numberOfMonth' => 'nullable|numeric',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }


            $period = $request->input('numberOfMonth', 6);
            $costBreakdown = $this->savingsTrackerService->getMonthlySavingsTrend($period);

            return $this->success($costBreakdown);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving the cost breakdown',
                $e->getMessage(),
                500
            );
        }
    }
    public function getSavingsStats(Request $request)
    {
        try {
            $costBreakdown = $this->savingsTrackerService->getSavingsStats();

            return $this->success($costBreakdown);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving the cost breakdown',
                $e->getMessage(),
                500
            );
        }
    }
}
