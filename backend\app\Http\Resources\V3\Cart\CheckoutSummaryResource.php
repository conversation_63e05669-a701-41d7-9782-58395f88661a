<?php

namespace App\Http\Resources\V3\Cart;

use App\Helpers\ProductPriceHelper;
use Illuminate\Http\Resources\Json\JsonResource;

class CheckoutSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $subtotal = 0;
        $total_tax = 0;
        $total_shipping = 0;

        foreach ($this->carts as $cartItem) {
            // Use the stored price in cart item to ensure consistency with user type pricing
            $itemTotal = $cartItem->price * $cartItem->quantity;
            $subtotal += $itemTotal;
            
            // Add tax if applicable
            if ($cartItem->product->tax_type === 'percent') {
                $total_tax += ($itemTotal * $cartItem->product->tax) / 100;
            } else {
                $total_tax += $cartItem->product->tax * $cartItem->quantity;
            }

            // Add shipping cost
            $total_shipping += $cartItem->shipping_cost;
        }

        // Apply coupon discount if exists
        $discount = 0;
        if ($this->coupon_code && $this->discount > 0) {
            if ($this->discount_type === 'percent') {
                $discount = ($subtotal * $this->discount) / 100;
            } else {
                $discount = $this->discount;
            }
        }

        $total = $subtotal + $total_tax + $total_shipping - $discount;

        return [
            'items' => CartItemResource::collection($this->carts),
            'summary' => [
                'subtotal' => round($subtotal, 2),
                'tax' => round($total_tax, 2),
                'shipping' => round($total_shipping, 2),
                'discount' => round($discount, 2),
                'total' => round($total, 2),
                'coupon' => $this->coupon_code ? [
                    'code' => $this->coupon_code,
                    'discount_type' => $this->discount_type,
                    'discount_value' => (float)$this->discount
                ] : null,
                'items_count' => $this->carts->sum('quantity')
            ],
            'shipping_address' => $this->address_id ? [
                'id' => $this->address->id,
                'name' => $this->address->name,
                'email' => $this->address->email,
                'phone' => $this->address->phone,
                'address' => $this->address->address,
                'country' => $this->address->country,
                'state' => $this->address->state,
                'city' => $this->address->city,
                'postal_code' => $this->address->postal_code
            ] : null
        ];
    }
}
