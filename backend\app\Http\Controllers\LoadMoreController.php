<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product; 

class LoadMoreController extends Controller
{

    // public function loadMoreData(Request $request) {
    //     try {
    //         $detailedProduct = Product::orderBy('id', 'desc')->paginate(5);

    //         if ($request->ajax()) {
    //             if ($detailedProduct->isEmpty()) {
    //                 return response()->json(['html' => '', 'message' => 'No more products to load.']);
    //             }

    //             $view = view('frontend.data', compact('detailedProduct'))->render();
    //             return response()->json(['html' => $view]);
    //         }

    //         return view('frontend.product_details.detailsNewDetailsHtml', compact('detailedProduct'));
    //     } catch (\Exception $e) {
    //         \Log::error('Error loading more data: ' . $e->getMessage());
    //         return response()->json(['error' => 'An error occurred while loading more data.'], 500);
    //     }
    // }
    


    public function loadMoreRelatedProducts($product_id, $offset)
    {
        $product = Product::findOrFail($product_id);
        $related_products = get_related_products($product, 5, $offset);

        return response()->json($related_products);
    }



}
