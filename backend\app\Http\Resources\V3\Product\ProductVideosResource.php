<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductVideosResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($data) {
            return [
                'id'=> $data->id,
                'thumbnail'=> uploaded_asset($data->thumbnail),
                'title'=> $data->title,
                'duration' => $data->duration,
                'video' => uploaded_asset($data->upload_id),
                'views' => $data->views,
                'sequence' => $data->sequence
            ];
        });
    }
}
