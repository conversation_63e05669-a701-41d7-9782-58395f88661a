<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Banner\SeasonalBannerResource;
use App\Models\Promotions\SeasonalBanner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiSeasonalBannerController extends ApiResponse
{
    /**
     * Get active seasonal banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveSeasonalBanners(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:20',
                'section_position' => 'string|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 5);
            $query = SeasonalBanner::active()->orderBy('display_order', 'asc');
            
            if ($request->has('section_position')) {
                $sectionPosition = $request->input('section_position');
                $query->sectionPosition($sectionPosition);
            }
            
            $banners = $query->limit($limit)->get();
            
            // Load campaign data if available
            $banners->load('campaign');
            
            return $this->success(SeasonalBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get upcoming seasonal banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUpcomingSeasonalBanners(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 5);
            
            $banners = SeasonalBanner::upcoming()
                ->orderBy('start_date', 'asc')
                ->limit($limit)
                ->get();
                
            // Load campaign data if available
            $banners->load('campaign');
            
            return $this->success(SeasonalBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get seasonal banners by season type
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSeasonalBannersByType(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'season_type' => 'required|string|max:50',
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $seasonType = $request->input('season_type');
            $limit = $request->input('limit', 5);
            
            $banners = SeasonalBanner::active()
                ->seasonType($seasonType)
                ->orderBy('display_order', 'asc')
                ->limit($limit)
                ->get();
                
            // Load campaign data if available
            $banners->load('campaign');
            
            return $this->success(SeasonalBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get seasonal banner by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getSeasonalBanner(int $id): JsonResponse
    {
        try {
            $banner = SeasonalBanner::find($id);
            
            if (!$banner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }
            
            // Load campaign data if available
            if ($banner->campaign_id) {
                $banner->load('campaign');
            }
            
            return $this->success(new SeasonalBannerResource($banner));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get seasonal banners by campaign ID
     *
     * @param int $campaignId
     * @return JsonResponse
     */
    public function getSeasonalBannersByCampaign(int $campaignId): JsonResponse
    {
        try {
            $banners = SeasonalBanner::where('campaign_id', $campaignId)
                ->orderBy('display_order', 'asc')
                ->get();
                
            // Load campaign data
            $banners->load('campaign');
            
            return $this->success(SeasonalBannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 