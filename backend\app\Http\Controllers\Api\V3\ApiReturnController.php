<?php

namespace App\Http\Controllers\Api\v3;

use App\Enums\ReturnStatus;
use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Rerurn\RerurnRequestResource;
use App\Http\Resources\V3\ReturnMethods\ReturnEligibilityResource;
use App\Http\Resources\V3\ReturnMethods\ReturnFeedbackResource;
use App\Http\Resources\V3\ReturnMethods\ShippingLabelResource;
use App\Models\Order;
use App\Models\ReturnMethod;
use App\Models\ReturnRequestInfo;
use App\Models\ReturnRequestProduct;
use App\Services\ActivityLogService;
use App\Services\ReturnMethodService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Helpers\NotificationHelper;

class ApiReturnController extends ApiResponse
{
    protected $activityLogService;
    protected $returnMethodService;

    public function __construct(ActivityLogService $activityLogService, ReturnMethodService $returnMethodService)
    {
        parent::__construct();
        $this->activityLogService = $activityLogService;
        $this->returnMethodService = $returnMethodService;
    }

    public function save_order_return_request(Request $request, Order $order)
    {
        // Convert JSON string to array if it's a string
        $itemIds = is_string($request->input('itemIds')) ? json_decode($request->input('itemIds'), true) : $request->input('itemIds');

        $messages = array(
            'itemIds.required' => translate('Please provide at least one item ID'),
            'itemIds.array' => translate('Item IDs must be in an array'),
            'itemIds.*.exists' => translate('Some item IDs are invalid or not found in order details'),
        );

        $request->merge([
            'itemIds' => $itemIds
        ]);

        $validator = Validator::make($request->all(), [
            'itemIds' => 'required|array',
            'itemIds.*' => [
                'required',
                'integer',
                Rule::exists('order_details', 'product_id')
                    ->where(function ($query) use ($order) {
                        $query->where('order_id', $order->id);
                    })
            ],
            'reason_for_return' => 'required|string',
            'originalPackaging' => 'boolean',
            'preferred_resolution' => 'required|string',
            'acceptTerms' => 'accepted',
            'attachments' => 'nullable|array',
            'attachments.*.filename' => 'required_with:attachments|string',
            'attachments.*.base64Content' => 'required_with:attachments|string|starts_with:data:image/',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400
            );
        }

        $reason_for_return = $request->input('reason_for_return');
        $description = $request->input('description');
        $preferred_resolution = $request->input('preferred_resolution');
        $returnMethod = $request->input('returnMethod');
        $returnCondition = $request->input('returnCondition');
        $originalPackaging = $request->input('originalPackaging');
        $order_details = $order->orderDetails()->whereIn('product_id', $itemIds)
            ->where('order_id', $order->id)->get();
        $checkExists = ReturnRequestProduct::whereIn('order_detail_id', $order_details->pluck('id')->toArray())->get();
        if($checkExists->count() > 0){
            return $this->error(
                'INVALID_REQUEST',
                'This order is already requested for refund',
                400
            );
        }
        try {
            DB::beginTransaction();
            $attachments = '';
            if ($request->hasFile('attachments')) {
                $upload = new \App\Utility\ApiAizUploadUtility();
                $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                if ($result['success'] == true) {
                    $attachments = $result['files'];
                }
            }
            $return_refund_request = new ReturnRequestInfo();
            $return_refund_request->return_code = 'RET-' . date('Ymd-His') . rand(10, 99);
            $return_refund_request->user_id = auth()->user()->id;
            $return_refund_request->order_id = $order->id;
            $return_refund_request->date = strtotime('now');
            $return_refund_request->amount = $order_details->sum('price');
            $return_refund_request->admin_note = Null;
            $return_refund_request->reason_for_return = $reason_for_return;
            $return_refund_request->user_note = $description;
            $return_refund_request->preferred_resolution = $preferred_resolution;
            $return_refund_request->return_status = ReturnStatus::PENDING;
            $return_refund_request->attachments = $attachments;
            $return_refund_request->returnMethod = $returnMethod;
            $return_refund_request->returnCondition = $returnCondition;
            $return_refund_request->originalPackaging = $originalPackaging;

            $return_refund_request->save();

            foreach ($itemIds as $item_id) {
                $order_detail = $order->orderDetails()->where('product_id', $item_id)->first();
                $return_refund_request->return_request_products()->create([
                    'return_request_info_id' => $return_refund_request->id,
                    'order_detail_id' => $order_detail->id,
                    'product_id' => $order_detail->product_id,
                    'quantity' => $order_detail->quantity,
                    'unit_price' => $order_detail->unit_price,
                    'amount' => $order_detail->quantity * $order_detail->unit_price,
                    'return_status' => ReturnStatus::PENDING,
                    'seller_id' => $order_detail->seller_id,
                    'seller_approval' => 0,
                    'admin_note_for_product' => Null,
                    'seller_note_for_product' => Null,
                ]);
            }

            $this->activityLogService->log(
                'return_request',
                'Return Request Placed',
                $return_refund_request->id,
                ReturnRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                '',
                ReturnStatus::PENDING,
                $reason_for_return,
                email_end_time: null,
            );

            NotificationHelper::sendReturnRequestNotification($return_refund_request);

            DB::commit();

            return $this->success(
                new RerurnRequestResource($return_refund_request),
                'Return Request Placed',
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Handle return request by numeric order ID (for frontend compatibility)
     */
    public function submitReturnRequest(Request $request, $orderId)
    {
        try {

            // Try to find order by ID first, then by code
            $order = null;
            if (is_numeric($orderId)) {
                $order = Order::find($orderId);
            }

            // If not found by ID, try by code
            if (!$order) {
                $order = Order::where('code', $orderId)->first();
            }

            if (!$order) {
                return $this->error(
                    'ORDER_NOT_FOUND',
                    'Order not found with the provided identifier',
                    404
                );
            }

            // Verify order belongs to authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    403
                );
            }

            // Check if request has any data at all
            if (empty($request->all()) && empty($request->allFiles()) && empty($request->getContent())) {
                return $this->error(
                    'EMPTY_REQUEST',
                    'No data received in the request. Please ensure you are sending FormData properly.',
                    400
                );
            }

            // Check if request is JSON or FormData and adjust validation accordingly
            $isJsonRequest = $request->isJson() || $request->header('Content-Type') === 'application/json';

            if ($isJsonRequest) {
                // JSON request validation (simpler, no file validation)
                $validator = Validator::make($request->all(), [
                    'reason_for_return' => 'required|string|max:500',
                    'order_details' => 'required|string',
                    'customer_message' => 'nullable|string|max:1000',
                    'preferred_resolution' => 'nullable|string|max:255',
                ], [
                    'reason_for_return.required' => 'The reason for return field is required.',
                    'order_details.required' => 'The order details field is required.',
                ]);
            } else {
                // FormData request validation (with file validation)
                $validator = Validator::make($request->all(), [
                    'reason_for_return' => 'required|string|max:500',
                    'order_details' => 'required|string',
                    'customer_message' => 'nullable|string|max:1000',
                    'preferred_resolution' => 'nullable|string|max:255',
                    'return_images' => 'nullable|array|max:20',
                    'return_images.*' => 'file|max:50240', // 50MB max per file - more flexible
                    'attachments' => 'nullable|array|max:20',
                    'attachments.*' => 'file|max:50240', // 50MB max per file - support all file types
                ], [
                    'reason_for_return.required' => 'The reason for return field is required.',
                    'order_details.required' => 'The order details field is required.',
                    'return_images.max' => 'You can upload maximum 20 files.',
                    'return_images.*.max' => 'Each file must not exceed 50MB.',
                    'attachments.max' => 'You can upload maximum 20 attachments.',
                    'attachments.*.max' => 'Each attachment must not exceed 50MB.',
                ]);
            }

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid data',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Parse and validate order details
            $order_details = json_decode($request->input('order_details'), true);

            if (!$order_details || !is_array($order_details) || empty($order_details)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Order details must be a valid JSON array with at least one item',
                    400
                );
            }

            $orderDetailIds = collect($order_details)->pluck('id')->filter()->toArray();
            if (empty($orderDetailIds)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Order details must contain valid item IDs',
                    400
                );
            }

            // Verify order details belong to this order
            $orderDetailsFromDb = $order->orderDetails()->whereIn('id', $orderDetailIds)->get();
            if ($orderDetailsFromDb->count() !== count($orderDetailIds)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Some order items were not found or do not belong to this order',
                    400
                );
            }

            // Check if return request already exists for any of these items
            // Also filter out orphaned records (where returnRequestInfo doesn't exist)
            $existingReturnProducts = ReturnRequestProduct::whereIn('order_detail_id', $orderDetailIds)
                ->whereHas('returnRequestInfo') // Only get products with valid return request info
                ->with('returnRequestInfo')
                ->get();

            if ($existingReturnProducts->count() > 0) {
                $existingOrderDetailIds = $existingReturnProducts->pluck('order_detail_id')->toArray();
                $duplicateItems = [];

                // Get product names for better error message
                foreach ($existingOrderDetailIds as $orderDetailId) {
                    $orderDetail = $orderDetailsFromDb->firstWhere('id', $orderDetailId);
                    if ($orderDetail && $orderDetail->product) {
                        $duplicateItems[] = $orderDetail->product->name ?? "Product ID: {$orderDetail->product_id}";
                    }
                }

                // If all items are duplicates, reject the request
                if (count($existingOrderDetailIds) === count($orderDetailIds)) {
                    return $this->error(
                        'DUPLICATE_RETURN_REQUEST',
                        'All selected items already have return requests in this order. Products: ' . implode(', ', $duplicateItems),
                        400
                    );
                }

                // If some items are duplicates, filter them out and proceed with valid items
                $validOrderDetailIds = array_diff($orderDetailIds, $existingOrderDetailIds);
                $order_details = array_filter($order_details, function($item) use ($validOrderDetailIds) {
                    return in_array($item['id'], $validOrderDetailIds);
                });

                // Update orderDetailsFromDb to only include valid items
                $orderDetailsFromDb = $orderDetailsFromDb->whereIn('id', $validOrderDetailIds);

                if (empty($order_details)) {
                    return $this->error(
                        'NO_VALID_ITEMS',
                        'No valid items to return. All selected items already have return requests in this order.',
                        400
                    );
                }
            }

            // Clean up any orphaned return request products for this order before proceeding
            $orphanedCount = ReturnRequestProduct::whereIn('order_detail_id', $orderDetailIds)
                ->whereDoesntHave('returnRequestInfo')
                ->delete();

            DB::beginTransaction();

            // Handle multiple file uploads (images and attachments)
            $allAttachments = [];

            // Handle return_images
            if ($request->hasFile('return_images')) {
                try {
                    $upload = new \App\Utility\ApiAizUploadUtility();
                    $result = $upload->multipleFileUpload($request->file('return_images'), auth()->user()->id);

                    if ($result['success'] == true) {
                        $uploadedFiles = explode(',', $result['files']);
                        $allAttachments = array_merge($allAttachments, $uploadedFiles);
                    }
                } catch (\Exception $e) {
                    // Handle error silently
                }
            }

            // Handle additional attachments
            if ($request->hasFile('attachments')) {
                try {
                    $upload = new \App\Utility\ApiAizUploadUtility();
                    $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);

                    if ($result['success'] == true) {
                        $uploadedFiles = explode(',', $result['files']);
                        $allAttachments = array_merge($allAttachments, $uploadedFiles);
                    }
                } catch (\Exception $e) {
                    // Handle error silently
                }
            }

            // Convert attachments array back to comma-separated string
            $attachmentsString = !empty($allAttachments) ? implode(',', $allAttachments) : '';

            // Calculate total amount
            $totalAmount = $orderDetailsFromDb->sum(function($detail) use ($order_details) {
                $requestedDetail = collect($order_details)->firstWhere('id', $detail->id);
                $quantity = $requestedDetail['quantity'] ?? $detail->quantity;
                return $detail->price * min($quantity, $detail->quantity);
            });

            // Create return request info
            $return_request = new ReturnRequestInfo();
            $return_request->return_code = 'RET-' . date('Ymd-His') . rand(10, 99);
            $return_request->user_id = auth()->user()->id;
            $return_request->order_id = $order->id;
            $return_request->date = strtotime('now');
            $return_request->amount = $totalAmount;
            $return_request->reason_for_return = $request->input('reason_for_return');
            $return_request->user_note = $request->input('customer_message');
            $return_request->preferred_resolution = $request->input('preferred_resolution', 'refund');
            $return_request->return_status = ReturnStatus::PENDING;
            $return_request->attachments = $attachmentsString;

            $return_request->save();

            // Create return request products
            foreach ($order_details as $item) {
                $orderDetail = $orderDetailsFromDb->firstWhere('id', $item['id']);
                if ($orderDetail) {
                    $quantity = min($item['quantity'], $orderDetail->quantity);

                    $return_request->return_request_products()->create([
                        'return_request_info_id' => $return_request->id,
                        'order_detail_id' => $orderDetail->id,
                        'product_id' => $orderDetail->product_id,
                        'quantity' => $quantity,
                        'unit_price' => $orderDetail->price,
                        'amount' => $orderDetail->price * $quantity,
                        'return_status' => ReturnStatus::PENDING,
                        'seller_id' => $orderDetail->seller_id,
                        'seller_approval' => 0,
                    ]);
                }
            }

            // Log activity
            $this->activityLogService->log(
                'return_request',
                'Return Request Placed',
                $return_request->id,
                ReturnRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                '',
                ReturnStatus::PENDING,
                $request->input('reason_for_return'),
                '',
                $order->id,
                Order::class
            );

            NotificationHelper::sendReturnRequestNotification($return_request);

            DB::commit();

            return $this->success(
                new RerurnRequestResource($return_request),
                'Return Request Placed Successfully'
            );

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to create return request: ' . $e->getMessage(),
                500
            );
        }
    }

    public function return_request_details(Request $request, $return_code)
    {
        $returnRequestInfo = ReturnRequestInfo::with('order', 'return_request_products', 'return_request_products.product')
            ->where('return_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        return $this->success(
            new RerurnRequestResource($returnRequestInfo),
            'Success Fully fetched return request details',
        );
    }

    public function return_request_update(Request $request, $return_code)
    {
        $returnRequestInfo = ReturnRequestInfo::with('order', 'return_request_products', 'return_request_products.product')
            ->where('return_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        if($returnRequestInfo->return_status !== ReturnStatus::PENDING){
            return $this->error(
                'INVALID_REQUEST',
                'This return request is already processed',
                400
            );
        }
        $validator = Validator::make($request->all(), [
            'returnMethod' => 'required|string',
            'additionalInformation' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*.filename' => 'required_with:attachments|string',
            'attachments.*.base64Content' => 'required_with:attachments|string|starts_with:data:image/',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();
            $attachments = '';
            if ($request->hasFile('attachments')) {
                $upload = new \App\Utility\ApiAizUploadUtility();
                $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                if ($result['success'] == true) {
                    $attachments = $result['files'];
                    $returnRequestInfo->attachments = $attachments;
                }
            }

            $description = $request->input('additionalInformation');
            $returnMethod = $request->input('returnMethod');
            if($description !== null ||$description !== '' ){
                $returnRequestInfo->user_note = $description;
            }
            $returnRequestInfo->returnMethod = $returnMethod;
            $returnRequestInfo->save();

            DB::commit();

            return $this->success(
                new RerurnRequestResource($returnRequestInfo),
                'Return Request Updated',
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                $e->getMessage(),
                500
            );
        }
    }

    public function return_request_cancel(Request $request, $return_code)
    {
        $returnRequestInfo = ReturnRequestInfo::with('order', 'return_request_products', 'return_request_products.product')
            ->where('return_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        if($returnRequestInfo->return_status !== ReturnStatus::PENDING){
            return $this->error(
                'INVALID_REQUEST',
                'This return request is already processed',
                400
            );
        }
        $description = $request->input('note');
        if($description !== null ||$description !== '' ){
            $returnRequestInfo->user_note = $description;
        }
        $returnRequestInfo->return_status = ReturnStatus::CANCELLED_BY_USER;
        $returnRequestInfo->save();
        return $this->success(
            new RerurnRequestResource($returnRequestInfo),
            'Return Request Cancelled',
        );
    }

    /**
     * Generate and retrieve a shipping label for an approved return
     */
    public function getShippingLabel(Request $request, $id)
    {
        try {
            // Find the return request
            $returnRequest = ReturnRequestInfo::where('return_code', $id)->firstOrFail();

            // Check if the return request belongs to the authenticated user
            if ($returnRequest->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this return request',
                    '',
                    403
                );
            }

            // Check if the return request is approved
            if ($returnRequest->return_status !== ReturnStatus::APPROVED) {
                return $this->error(
                    'NOT_APPROVED',
                    'This return request has not been approved yet',
                    '',
                    400
                );
            }

            return $this->success(
                [],
                'coming Soon',
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Return request not found',
                '',
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while generating the shipping label',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Reschedule a courier pickup for a return
     */
    public function reschedulePickup(Request $request, $id)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'date' => 'required|date|after_or_equal:today',
                'timeSlot' => 'required|string',
                'address' => 'required|string',
                'contactName' => 'required|string',
                'contactPhone' => 'required|string',
                'instructions' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Find the return request
            $returnRequest = ReturnRequestInfo::where('return_code', $id)->firstOrFail();

            // Check if the return request belongs to the authenticated user
            if ($returnRequest->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this return request',
                    '',
                    403
                );
            }

            // Check if the return request has a pickup scheduled
            $existingPickup = $returnRequest->latestPickupDetail;
            if (!$existingPickup) {
                return $this->error(
                    'NO_PICKUP',
                    'No pickup has been scheduled for this return request',
                    '',
                    400
                );
            }

            // Reschedule the pickup
            $pickupData = $this->returnMethodService->reschedulePickup($returnRequest, $request->all());

            if (!$pickupData) {
                return $this->error(
                    'RESCHEDULE_FAILED',
                    'Failed to reschedule pickup',
                    '',
                    500
                );
            }

            return $this->success(
                $pickupData,
                'Pickup rescheduled successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Return request not found',
                '',
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while rescheduling the pickup',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get available return methods for an order
     */
    public function getReturnMethods(Request $request, $orderId)
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();

            // Check if the order belongs to the authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    '',
                    403
                );
            }

            return $this->returnMethodService->getReturnMethodsForOrder($order);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving return methods',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Check if an order is eligible for return
     */
    public function checkReturnEligibility(Request $request, $orderId)
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();

            // Check if the order belongs to the authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    '',
                    403
                );
            }

            // Get return eligibility details
            $eligibilityData = $this->returnMethodService->getReturnEligibility($order);

            return $this->success(
                new ReturnEligibilityResource($eligibilityData),
                'Return eligibility details retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while checking return eligibility',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Submit feedback for a completed return
     */
    public function submitFeedback(Request $request, $id)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'satisfactionRating' => 'required|integer|min:1|max:5',
                'processRating' => 'required|integer|min:1|max:5',
                'serviceRating' => 'required|integer|min:1|max:5',
                'comments' => 'nullable|string|max:1000',
                'wouldRecommend' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Find the return request
            $returnRequest = ReturnRequestInfo::where('return_code', $id)->firstOrFail();

            // Check if the return request belongs to the authenticated user
            if ($returnRequest->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this return request',
                    '',
                    403
                );
            }

            // Check if the return request is completed
            if ($returnRequest->return_status !== ReturnStatus::COMPLETED) {
                return $this->error(
                    'NOT_COMPLETED',
                    'Feedback can only be submitted for completed returns',
                    '',
                    400
                );
            }

            // Check if feedback has already been submitted
            if (!empty($returnRequest->feedback)) {
                return $this->error(
                    'FEEDBACK_ALREADY_SUBMITTED',
                    'Feedback has already been submitted for this return',
                    '',
                    400
                );
            }

            // Submit the feedback
            $feedbackData = $this->returnMethodService->submitFeedback($returnRequest, $request->all());

            if (!$feedbackData) {
                return $this->error(
                    'FEEDBACK_SUBMISSION_FAILED',
                    'Failed to submit feedback',
                    '',
                    500
                );
            }

            return $this->success(
                new ReturnFeedbackResource($feedbackData),
                'Feedback submitted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Return request not found',
                '',
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while submitting feedback',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get already returned items for an order
     */
    public function getOrderReturnedItems(Request $request, $orderId)
    {
        try {
            // Try to find order by ID first, then by code
            $order = null;
            if (is_numeric($orderId)) {
                $order = Order::find($orderId);
            }

            if (!$order) {
                $order = Order::where('code', $orderId)->first();
            }

            if (!$order) {
                return $this->error(
                    'ORDER_NOT_FOUND',
                    'Order not found with the provided identifier',
                    404
                );
            }

            // Verify order belongs to authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    403
                );
            }

            // Get all returned items for this order
            $returnedItems = ReturnRequestProduct::whereHas('returnRequestInfo', function($query) use ($order) {
                $query->where('order_id', $order->id);
            })
            ->with(['returnRequestInfo', 'product', 'orderDetail'])
            ->get();

            $returnedItemsData = $returnedItems->map(function($item) {
                return [
                    'order_detail_id' => $item->order_detail_id,
                    'product_id' => $item->product_id,
                    'product_name' => $item->product ? $item->product->name : 'Product not found',
                    'quantity' => $item->quantity,
                    'return_code' => $item->returnRequestInfo ? $item->returnRequestInfo->return_code : null,
                    'return_status' => $item->returnRequestInfo ? $item->returnRequestInfo->return_status : null,
                    'return_date' => $item->returnRequestInfo ? date('Y-m-d H:i:s', $item->returnRequestInfo->date) : null
                ];
            });

            return $this->success([
                'order_id' => $order->id,
                'order_code' => $order->code,
                'returned_items' => $returnedItemsData,
                'returned_items_count' => $returnedItems->count()
            ], 'Already returned items fetched successfully');

        } catch (\Exception $e) {
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to fetch returned items: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Debug endpoint to test request handling
     */
    public function debug_request(Request $request)
    {
        return $this->success([
            'method' => $request->method(),
            'input_all' => $request->all(),
            'files_count' => count($request->allFiles()),
            'content_length' => $request->header('Content-Length'),
            'content_type' => $request->header('Content-Type'),
            'has_body' => !empty($request->getContent()),
            'body_size' => strlen($request->getContent()),
        ], 'Debug request successful');
    }

    /**
     * Test return request endpoint (for general return requests without specific order ID)
     */
    public function test_return_request(Request $request)
    {
        try {
            // Enhanced validation with better error messages
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|string',
                'reason_for_return' => 'required|string|max:500',
                'order_details' => 'required|string',
                'customer_message' => 'nullable|string|max:1000',
                'preferred_resolution' => 'nullable|string|max:255',
                'return_images' => 'nullable|array|max:10',
                'return_images.*' => 'file|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max per image
                'attachments' => 'nullable|array|max:10',
                'attachments.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240', // 10MB max per file
            ], [
                'order_id.required' => 'Order ID is required.',
                'reason_for_return.required' => 'The reason for return field is required.',
                'order_details.required' => 'The order details field is required.',
                'return_images.max' => 'You can upload maximum 10 images.',
                'return_images.*.image' => 'Each file must be an image.',
                'return_images.*.mimes' => 'Images must be jpeg, png, jpg, or gif format.',
                'return_images.*.max' => 'Each image must not exceed 5MB.',
                'attachments.max' => 'You can upload maximum 10 attachments.',
                'attachments.*.mimes' => 'Attachments must be jpeg, png, jpg, gif, pdf, doc, or docx format.',
                'attachments.*.max' => 'Each attachment must not exceed 10MB.',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid data',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Try to find order by ID or code
            $orderId = $request->input('order_id');
            $order = null;

            if (is_numeric($orderId)) {
                $order = Order::find($orderId);
            }

            if (!$order) {
                $order = Order::where('code', $orderId)->first();
            }

            if (!$order) {
                return $this->error(
                    'ORDER_NOT_FOUND',
                    'Order not found with the provided identifier',
                    404
                );
            }

            // Verify order belongs to authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    403
                );
            }

            // Parse and validate order details
            $order_details = json_decode($request->input('order_details'), true);

            if (!$order_details || !is_array($order_details) || empty($order_details)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Order details must be a valid JSON array with at least one item',
                    400
                );
            }

            $orderDetailIds = collect($order_details)->pluck('id')->filter()->toArray();
            if (empty($orderDetailIds)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Order details must contain valid item IDs',
                    400
                );
            }

            // Verify order details belong to this order
            $orderDetailsFromDb = $order->orderDetails()->whereIn('id', $orderDetailIds)->get();
            if ($orderDetailsFromDb->count() !== count($orderDetailIds)) {
                return $this->error(
                    'INVALID_ORDER_DETAILS',
                    'Some order items were not found or do not belong to this order',
                    400
                );
            }

            // Check if return request already exists for any of these items
            $existingReturnProducts = ReturnRequestProduct::whereIn('order_detail_id', $orderDetailIds)->exists();
            if ($existingReturnProducts) {
                return $this->error(
                    'INVALID_REQUEST',
                    'One or more items from this order already have a return request',
                    400
                );
            }

            DB::beginTransaction();

            // Handle multiple file uploads (images and attachments)
            $allAttachments = [];

            // Handle return_images
            if ($request->hasFile('return_images')) {
                try {
                    $upload = new \App\Utility\ApiAizUploadUtility();
                    $result = $upload->multipleFileUpload($request->file('return_images'), auth()->user()->id);
                    if ($result['success'] == true) {
                        $allAttachments = array_merge($allAttachments, explode(',', $result['files']));
                    }
                } catch (\Exception $e) {
                    // Handle error silently
                }
            }

            // Handle additional attachments
            if ($request->hasFile('attachments')) {
                try {
                    $upload = new \App\Utility\ApiAizUploadUtility();
                    $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                    if ($result['success'] == true) {
                        $allAttachments = array_merge($allAttachments, explode(',', $result['files']));
                    }
                } catch (\Exception $e) {
                    // Handle error silently
                }
            }

            // Convert attachments array back to comma-separated string
            $attachmentsString = !empty($allAttachments) ? implode(',', $allAttachments) : '';

            // Calculate total amount
            $totalAmount = $orderDetailsFromDb->sum(function($detail) use ($order_details) {
                $requestedDetail = collect($order_details)->firstWhere('id', $detail->id);
                $quantity = $requestedDetail['quantity'] ?? $detail->quantity;
                return $detail->price * min($quantity, $detail->quantity);
            });

            // Create return request info
            $return_request = new ReturnRequestInfo();
            $return_request->return_code = 'RET-' . date('Ymd-His') . rand(10, 99);
            $return_request->user_id = auth()->user()->id;
            $return_request->order_id = $order->id;
            $return_request->date = strtotime('now');
            $return_request->amount = $totalAmount;
            $return_request->reason_for_return = $request->input('reason_for_return');
            $return_request->user_note = $request->input('customer_message');
            $return_request->preferred_resolution = $request->input('preferred_resolution', 'refund');
            $return_request->return_status = ReturnStatus::PENDING;
            $return_request->attachments = $attachmentsString;

            $return_request->save();

            // Create return request products
            foreach ($order_details as $item) {
                $orderDetail = $orderDetailsFromDb->firstWhere('id', $item['id']);
                if ($orderDetail) {
                    $quantity = min($item['quantity'], $orderDetail->quantity);

                    $return_request->return_request_products()->create([
                        'return_request_info_id' => $return_request->id,
                        'order_detail_id' => $orderDetail->id,
                        'product_id' => $orderDetail->product_id,
                        'quantity' => $quantity,
                        'unit_price' => $orderDetail->price,
                        'amount' => $orderDetail->price * $quantity,
                        'return_status' => ReturnStatus::PENDING,
                        'seller_id' => $orderDetail->seller_id,
                        'seller_approval' => 0,
                    ]);
                }
            }

            // Log activity
            $this->activityLogService->log(
                'return_request',
                'Return Request Placed',
                $return_request->id,
                ReturnRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                '',
                ReturnStatus::PENDING,
                $request->input('reason_for_return')
            );

            NotificationHelper::sendReturnRequestNotification($return_request);

            DB::commit();

            return $this->success(
                new RerurnRequestResource($return_request),
                'Return Request Placed Successfully'
            );

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to create return request: ' . $e->getMessage(),
                500
            );
        }
    }

    private function getFileDebugInfo(Request $request)
    {
        $fileDetails = [];
        foreach ($request->allFiles() as $key => $file) {
            $fileDetails[] = [
                'key' => $key,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime' => $file->getMimeType(),
                'is_valid' => $file->isValid(),
                'path' => $file->getPathname()
            ];
        }
        return $fileDetails;
    }
}
