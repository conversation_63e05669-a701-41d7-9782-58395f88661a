<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\AuctionProductBid;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\ProductTax;
use App\Models\ProductStock;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AuctionProductController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check for admin routes
        $this->middleware(['permission:view_all_auction_products'])->only([
            'all_auction_product_list', 'inhouse_auction_products', 'seller_auction_products'
        ]);
        $this->middleware(['permission:add_auction_product'])->only([
            'product_create_admin', 'product_store_admin'
        ]);
        $this->middleware(['permission:edit_auction_product'])->only([
            'product_edit_admin', 'product_update_admin'
        ]);
        $this->middleware(['permission:delete_auction_product'])->only([
            'product_destroy_admin'
        ]);
    }

    // Public Methods

    /**
     * Show all auction products for public
     */
    public function all_auction_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('auction_product', 1)
            ->where('published', 1)
            ->where('auction_start_date', '<=', strtotime("now"))
            ->where('auction_end_date', '>=', strtotime("now"))
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(12);
        return view('frontend.auction_products.index', compact('products', 'sort_search'));
    }

    /**
     * Show auction product details
     */
    public function auction_product_details($slug)
    {
        $product = Product::where('slug', $slug)
            ->where('auction_product', 1)
            ->where('published', 1)
            ->firstOrFail();

        $bids = AuctionProductBid::where('product_id', $product->id)
            ->with('user')
            ->orderBy('amount', 'desc')
            ->take(10)
            ->get();

        return view('frontend.auction_products.details', compact('product', 'bids'));
    }

    /**
     * User's purchase history for auction products
     */
    public function purchase_history_user(Request $request)
    {
        $orders = Order::where('user_id', Auth::id())
            ->whereHas('orderDetails.product', function($query) {
                $query->where('auction_product', 1);
            })
            ->latest()
            ->paginate(15);

        return view('frontend.auction_products.purchase_history', compact('orders'));
    }

    // Admin Methods

    /**
     * Show all auction products for admin
     */
    public function all_auction_product_list(Request $request)
    {
        $sort_search = null;
        $products = Product::where('auction_product', 1)->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.auction_products.index', compact('products', 'sort_search'));
    }

    /**
     * Show inhouse auction products for admin
     */
    public function inhouse_auction_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('auction_product', 1)
            ->where('added_by', 'admin')
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.auction_products.inhouse', compact('products', 'sort_search'));
    }

    /**
     * Show seller auction products for admin
     */
    public function seller_auction_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('auction_product', 1)
            ->where('added_by', 'seller')
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.auction_products.seller', compact('products', 'sort_search'));
    }

    /**
     * Show admin auction product create form
     */
    public function product_create_admin()
    {
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        return view('backend.auction_products.create', compact('categories', 'brands'));
    }

    /**
     * Store admin auction product
     */
    public function product_store_admin(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'starting_bid' => 'required|numeric|min:0',
            'auction_start_date' => 'required|date',
            'auction_end_date' => 'required|date|after:auction_start_date',
        ]);

        $product = new Product;
        $product->name = $request->name;
        $product->category_id = $request->category_id;
        $product->brand_id = $request->brand_id;
        $product->photos = $request->photos;
        $product->thumbnail_img = $request->thumbnail_img;
        $product->description = $request->description;
        $product->unit_price = $request->starting_bid;
        $product->auction_product = 1;
        $product->auction_start_date = strtotime($request->auction_start_date);
        $product->auction_end_date = strtotime($request->auction_end_date);
        $product->starting_bid = $request->starting_bid;
        $product->slug = Str::slug($request->name) . '-' . Str::random(5);
        $product->user_id = 1; // Admin
        $product->added_by = 'admin';
        $product->published = 1;

        if ($product->save()) {
            // Create stock entry
            $stock = new ProductStock;
            $stock->product_id = $product->id;
            $stock->variant = '';
            $stock->sku = $product->slug;
            $stock->price = $request->starting_bid;
            $stock->qty = 1;
            $stock->save();

            flash(translate('Auction product has been created successfully'))->success();
            return redirect()->route('auction.all_products');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show admin auction product edit form
     */
    public function product_edit_admin($id)
    {
        $product = Product::where('auction_product', 1)->findOrFail($id);
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        return view('backend.auction_products.edit', compact('product', 'categories', 'brands'));
    }

    /**
     * Update admin auction product
     */
    public function product_update_admin(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'starting_bid' => 'required|numeric|min:0',
            'auction_start_date' => 'required|date',
            'auction_end_date' => 'required|date|after:auction_start_date',
        ]);

        $product = Product::where('auction_product', 1)->findOrFail($id);
        $product->name = $request->name;
        $product->category_id = $request->category_id;
        $product->brand_id = $request->brand_id;
        $product->photos = $request->photos;
        $product->thumbnail_img = $request->thumbnail_img;
        $product->description = $request->description;
        $product->unit_price = $request->starting_bid;
        $product->auction_start_date = strtotime($request->auction_start_date);
        $product->auction_end_date = strtotime($request->auction_end_date);
        $product->starting_bid = $request->starting_bid;

        if ($product->save()) {
            flash(translate('Auction product has been updated successfully'))->success();
            return redirect()->route('auction.all_products');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Delete admin auction product
     */
    public function product_destroy_admin($id)
    {
        $product = Product::where('auction_product', 1)->findOrFail($id);
        
        // Delete related bids
        AuctionProductBid::where('product_id', $product->id)->delete();
        
        // Delete product stocks
        ProductStock::where('product_id', $product->id)->delete();
        
        // Delete product taxes
        ProductTax::where('product_id', $product->id)->delete();

        if ($product->delete()) {
            flash(translate('Auction product has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Show admin auction product orders
     */
    public function admin_auction_product_orders(Request $request)
    {
        $orders = Order::whereHas('orderDetails.product', function($query) {
            $query->where('auction_product', 1);
        })->latest()->paginate(15);

        return view('backend.auction_products.orders', compact('orders'));
    }

    // Seller Methods

    /**
     * Show seller auction products
     */
    public function auction_product_list_seller(Request $request)
    {
        $sort_search = null;
        $products = Product::where('auction_product', 1)
            ->where('user_id', Auth::id())
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('seller.auction_products.index', compact('products', 'sort_search'));
    }

    /**
     * Show seller auction product create form
     */
    public function product_create_seller()
    {
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        return view('seller.auction_products.create', compact('categories', 'brands'));
    }

    /**
     * Store seller auction product
     */
    public function product_store_seller(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'starting_bid' => 'required|numeric|min:0',
            'auction_start_date' => 'required|date',
            'auction_end_date' => 'required|date|after:auction_start_date',
        ]);

        $product = new Product;
        $product->name = $request->name;
        $product->category_id = $request->category_id;
        $product->brand_id = $request->brand_id;
        $product->photos = $request->photos;
        $product->thumbnail_img = $request->thumbnail_img;
        $product->description = $request->description;
        $product->unit_price = $request->starting_bid;
        $product->auction_product = 1;
        $product->auction_start_date = strtotime($request->auction_start_date);
        $product->auction_end_date = strtotime($request->auction_end_date);
        $product->starting_bid = $request->starting_bid;
        $product->slug = Str::slug($request->name) . '-' . Str::random(5);
        $product->user_id = Auth::id();
        $product->added_by = 'seller';
        $product->published = 0; // Needs approval

        if ($product->save()) {
            // Create stock entry
            $stock = new ProductStock;
            $stock->product_id = $product->id;
            $stock->variant = '';
            $stock->sku = $product->slug;
            $stock->price = $request->starting_bid;
            $stock->qty = 1;
            $stock->save();

            flash(translate('Auction product has been created successfully and is pending approval'))->success();
            return redirect()->route('auction_products.seller.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show seller auction product edit form
     */
    public function product_edit_seller($id)
    {
        $product = Product::where('auction_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        return view('seller.auction_products.edit', compact('product', 'categories', 'brands'));
    }

    /**
     * Update seller auction product
     */
    public function product_update_seller(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'starting_bid' => 'required|numeric|min:0',
            'auction_start_date' => 'required|date',
            'auction_end_date' => 'required|date|after:auction_start_date',
        ]);

        $product = Product::where('auction_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        
        $product->name = $request->name;
        $product->category_id = $request->category_id;
        $product->brand_id = $request->brand_id;
        $product->photos = $request->photos;
        $product->thumbnail_img = $request->thumbnail_img;
        $product->description = $request->description;
        $product->unit_price = $request->starting_bid;
        $product->auction_start_date = strtotime($request->auction_start_date);
        $product->auction_end_date = strtotime($request->auction_end_date);
        $product->starting_bid = $request->starting_bid;

        if ($product->save()) {
            flash(translate('Auction product has been updated successfully'))->success();
            return redirect()->route('auction_products.seller.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Delete seller auction product
     */
    public function product_destroy_seller($id)
    {
        $product = Product::where('auction_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        
        // Delete related bids
        AuctionProductBid::where('product_id', $product->id)->delete();
        
        // Delete product stocks
        ProductStock::where('product_id', $product->id)->delete();
        
        // Delete product taxes
        ProductTax::where('product_id', $product->id)->delete();

        if ($product->delete()) {
            flash(translate('Auction product has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Show seller auction product orders
     */
    public function seller_auction_product_orders(Request $request)
    {
        $orders = Order::whereHas('orderDetails.product', function($query) {
            $query->where('auction_product', 1)
                  ->where('user_id', Auth::id());
        })->latest()->paginate(15);

        return view('seller.auction_products.orders', compact('orders'));
    }

    // API Methods (for API routes)

    /**
     * API: Get auction products list
     */
    public function index()
    {
        $products = Product::where('published', 1)
            ->where('auction_product', 1)
            ->where('auction_start_date', '<=', strtotime("now"))
            ->where('auction_end_date', '>=', strtotime("now"))
            ->latest()
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * API: Get auction product details
     */
    public function details_auction_product($id)
    {
        $product = Product::where('auction_product', 1)
            ->where('published', 1)
            ->findOrFail($id);

        $bids = AuctionProductBid::where('product_id', $product->id)
            ->with('user')
            ->orderBy('amount', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'product' => $product,
                'bids' => $bids
            ]
        ]);
    }

    /**
     * API: Get user's bided products
     */
    public function bided_products_list()
    {
        $user_bids = AuctionProductBid::where('user_id', auth()->id())
            ->pluck('product_id')
            ->unique();

        $products = Product::whereIn('id', $user_bids)
            ->where('auction_product', 1)
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * API: Get user's auction purchase history
     */
    public function user_purchase_history(Request $request)
    {
        $orders = Order::where('user_id', auth()->id())
            ->whereHas('orderDetails.product', function($query) {
                $query->where('auction_product', 1);
            })
            ->latest()
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }
} 