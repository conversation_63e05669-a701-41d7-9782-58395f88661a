<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriberController extends ApiResponse
{
    public function store(Request $request)
    {
        $messages = array(

            'email.required' => translate('Email is required'),
            'email.email' => translate('Valid email is required'),
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        try {
            $subscriber = Subscriber::where('email', $request->email)->first();
            if($subscriber == null){
                $subscriber = new Subscriber;
                $subscriber->email = $request->email;
                $subscriber->save();
            }
            return $this->success(Null,'Your successfully subscribed');
        }catch (\Throwable $th) {
            return $this->error(400,'Failed to Submit feedback', $th->getMessage());
        }
    }
}
