{"__meta": {"id": "01K113WMA6KJFDEWT8X0TR89H2", "datetime": "2025-07-25 08:40:37", "utime": **********.062915, "method": "GET", "uri": "/buzfi-new-backend/api/v3/cart", "ip": "::1"}, "messages": {"count": 11, "messages": [{"message": "[08:40:37] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/cart\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.02032, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.020723, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.debug: getTempUserId: Found temp user ID in header {\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\"\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.020848, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Cart index - User identification {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.020896, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Getting cart {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.020941, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Getting or creating cart info {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.020982, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Found existing cart info {\n    \"cart_info_id\": \"483aa506-5b78-43ac-88e9-f05f61567fa1\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.054114, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Found guest user cart items {\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"items_count\": 0,\n    \"items\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.055382, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Getting saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"is_authenticated\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.05756, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: Found saved items {\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.058629, "xdebug_link": null, "collector": "log"}, {"message": "[08:40:37] LOG.info: <PERSON>t retrieved successfully {\n    \"cart_info_id\": \"483aa506-5b78-43ac-88e9-f05f61567fa1\",\n    \"user_id\": null,\n    \"temp_user_id\": \"temp_1753457812364_c9d80y7ne\",\n    \"items_count\": 0,\n    \"saved_items_count\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.058689, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.830358, "end": **********.062931, "duration": 0.2325730323791504, "duration_str": "233ms", "measures": [{"label": "Booting", "start": **********.830358, "relative_start": 0, "end": **********.990181, "relative_end": **********.990181, "duration": 0.*****************, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.990187, "relative_start": 0.*****************, "end": **********.062932, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "72.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.995888, "relative_start": 0.*****************, "end": **********.9983, "relative_end": **********.9983, "duration": 0.002412080764770508, "duration_str": "2.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.060508, "relative_start": 0.****************, "end": **********.060723, "relative_end": **********.060723, "duration": 0.00021505355834960938, "duration_str": "215μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.061604, "relative_start": 0.****************, "end": **********.061634, "relative_end": **********.061634, "duration": 3.0040740966796875e-05, "duration_str": "30μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029590000000000005, "accumulated_duration_str": "29.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `cart_info` where `temp_user_id` = 'temp_1753457812364_c9d80y7ne' and `status` = 'active' and `user_id` is null limit 1", "type": "query", "params": [], "bindings": ["temp_1753457812364_c9d80y7ne", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, {"index": 17, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0231729, "duration": 0.027100000000000003, "duration_str": "27.1ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:125", "source": {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=125", "ajax": false, "filename": "EnhancedCartService.php", "line": "125"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 91.585}, {"sql": "update `cart_info` set `last_activity_at` = '2025-07-25 08:40:37', `cart_info`.`updated_at` = '2025-07-25 08:40:37' where `id` = '483aa506-5b78-43ac-88e9-f05f61567fa1'", "type": "query", "params": [], "bindings": ["2025-07-25 08:40:37", "2025-07-25 08:40:37", "483aa506-5b78-43ac-88e9-f05f61567fa1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 152}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 185}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.051732, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:152", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=152", "ajax": false, "filename": "EnhancedCartService.php", "line": "152"}, "connection": "buzfi", "explain": null, "start_percent": 91.585, "width_percent": 5.914}, {"sql": "select * from `carts` where `temp_user_id` = 'temp_1753457812364_c9d80y7ne' and `status` = 'active' and `user_id` is null", "type": "query", "params": [], "bindings": ["temp_1753457812364_c9d80y7ne", "active"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.054447, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:207", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 207}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=207", "ajax": false, "filename": "EnhancedCartService.php", "line": "207"}, "connection": "buzfi", "explain": null, "start_percent": 97.499, "width_percent": 1.386}, {"sql": "select * from `saved_items` where `temp_user_id` = 'temp_1753457812364_c9d80y7ne' and `user_id` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["temp_1753457812364_c9d80y7ne"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, {"index": 16, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 223}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/V3/CartController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\CartController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.057842, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "EnhancedCartService.php:864", "source": {"index": 15, "namespace": null, "name": "app/Services/EnhancedCartService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\EnhancedCartService.php", "line": 864}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FEnhancedCartService.php&line=864", "ajax": false, "filename": "EnhancedCartService.php", "line": "864"}, "connection": "buzfi", "explain": null, "start_percent": 98.885, "width_percent": 1.115}]}, "models": {"data": {"App\\Models\\CartInfo": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCartInfo.php&line=1", "ajax": false, "filename": "CartInfo.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 1, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index", "uri": "GET api/v3/cart", "controller": "App\\Http\\Controllers\\Api\\V3\\CartController@index<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/cart", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FCartController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/CartController.php:28-68</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "232ms", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1094114876 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1094114876\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2053278924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2053278924\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-temp-user-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457812364_c9d80y7ne</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:3000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c03caeb96aca979edafe679b36e9fda5676f10e64d67c27e; XSRF-TOKEN=eyJpdiI6InBpcnUwQ1gwd2Z0RTNUMmNnMXkzWlE9PSIsInZhbHVlIjoiRjFJTkxhZTY4eUdUQjkvSFgydENmWVdXcU1SVVZNNUFwMVBpaTRvV2Vsak92VUc2ais5dVJwU0tweEI3NXlJUzFOOVE0WThad0hKYjdyM3huSm1weWdSSmFVczJsNzE2SS9WLzIrVk1vSGRnajB6N2xZT0pRN29JTWRqTER4enQiLCJtYWMiOiIxMGQyNTAwOGYwNDQ1ZTkxOTQ0MTZmMWU2ZGQ5ZDJlMDFkYzkzMzViODBiMTc3ZjdlMGMzMDE4Nzk0YmUxOTA5IiwidGFnIjoiIn0%3D; buzficom_session=2Hg3dlAIxmkTFNDOaL9RG1iJACSlsvyFeTeExYS8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e8zfbw8IWBryoCeFcbmkaqfCxIBGuuCTa4qmC48A</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1205458661 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:40:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">553</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikx5TjlDUWkvejhUREhRQVZ6Y2Rmdmc9PSIsInZhbHVlIjoic1U0OGZic0lFUTg5NFhBN1RxNEw2UG1pdEVZc3Z6a3QwVnY3Mm04b2w3MVZoZzEwU1VsdEFLc2M1RnVDMUt1V0dhYzlIV2lrUFRrYW5kYzJpTHlYd011K0tERFRKWDFQM25FQlFEbFFEVFpGUGtmZURONUJWREdOSkdWLzJFbloiLCJtYWMiOiJiZmM3MjIxN2ExMDMzN2E5NTcxZGU4ZWM4N2QyN2Y2NWNiZDI0Yjk1NDNhNmJmMTYyZGFjM2U2YjE3OGNkY2JjIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IktVWVBRTVZRczhjS3U5THFHUnlYdGc9PSIsInZhbHVlIjoiMkZTLzJxL3NZUGFqdWFReEJyWjJOU3VuRlBqYUtaZGJuUStDQWs2S0ZGT1orWkpqMWFIUEZXMEtlaVYvdXdob0NIdXNYOE9TSGRpZjkydFhLZklVWlQ2ZkUwSXRyRVo1MUFuNlVKcnROQWwvWkUxUTdlMzBwSlpMZ3hiQnhWc3EiLCJtYWMiOiJiN2E5MTY5Mzc3YmM4MDNiYWUxYTI4NWUyYTJmYjYzNzU2ZmMxNzI1ZmRiYmUzNDA3NjRmOGFhNjJmMjYwZjAwIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikx5TjlDUWkvejhUREhRQVZ6Y2Rmdmc9PSIsInZhbHVlIjoic1U0OGZic0lFUTg5NFhBN1RxNEw2UG1pdEVZc3Z6a3QwVnY3Mm04b2w3MVZoZzEwU1VsdEFLc2M1RnVDMUt1V0dhYzlIV2lrUFRrYW5kYzJpTHlYd011K0tERFRKWDFQM25FQlFEbFFEVFpGUGtmZURONUJWREdOSkdWLzJFbloiLCJtYWMiOiJiZmM3MjIxN2ExMDMzN2E5NTcxZGU4ZWM4N2QyN2Y2NWNiZDI0Yjk1NDNhNmJmMTYyZGFjM2U2YjE3OGNkY2JjIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IktVWVBRTVZRczhjS3U5THFHUnlYdGc9PSIsInZhbHVlIjoiMkZTLzJxL3NZUGFqdWFReEJyWjJOU3VuRlBqYUtaZGJuUStDQWs2S0ZGT1orWkpqMWFIUEZXMEtlaVYvdXdob0NIdXNYOE9TSGRpZjkydFhLZklVWlQ2ZkUwSXRyRVo1MUFuNlVKcnROQWwvWkUxUTdlMzBwSlpMZ3hiQnhWc3EiLCJtYWMiOiJiN2E5MTY5Mzc3YmM4MDNiYWUxYTI4NWUyYTJmYjYzNzU2ZmMxNzI1ZmRiYmUzNDA3NjRmOGFhNjJmMjYwZjAwIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205458661\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1457490069 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e8zfbw8IWBryoCeFcbmkaqfCxIBGuuCTa4qmC48A</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/buzfi-new-backend/api/v3/cart</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>temp_user_id</span>\" => \"<span class=sf-dump-str title=\"28 characters\">temp_1753457812364_c9d80y7ne</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457490069\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/cart", "controller_action": "App\\Http\\Controllers\\Api\\V3\\CartController@index"}, "badge": null}}