<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\CombinedOrder;
use App\Models\Order;
use App\Models\User;
use App\Models\CustomerPackagePayment;
use App\Models\SellerPackagePayment;
use App\Models\Wallet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;

class PayfastController extends Controller
{
    private $merchant_id;
    private $merchant_key;
    private $passphrase;
    private $sandbox;

    public function __construct()
    {
        $this->merchant_id = get_setting('payfast_merchant_id');
        $this->merchant_key = get_setting('payfast_merchant_key');
        $this->passphrase = get_setting('payfast_passphrase');
        $this->sandbox = get_setting('payfast_sandbox') == 1;
    }

    /**
     * Checkout payment notification handler
     */
    public function checkout_notify(Request $request)
    {
        try {
            Log::info('Payfast checkout notification received: ', $request->all());

            if ($this->validatePayfastData($request->all())) {
                $payment_status = $request->payment_status;
                $m_payment_id = $request->m_payment_id;
                $pf_payment_id = $request->pf_payment_id;
                $amount_gross = $request->amount_gross;
                $custom_str1 = $request->custom_str1; // Combined order ID

                if ($payment_status === 'COMPLETE') {
                    $this->processCheckoutPayment($custom_str1, $pf_payment_id, $amount_gross);
                }

                return response('OK', 200);
            }

            return response('Invalid signature', 400);

        } catch (\Exception $e) {
            Log::error('Payfast checkout notification error: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

    /**
     * Checkout payment return handler
     */
    public function checkout_return(Request $request)
    {
        flash(translate('Payment processed successfully'))->success();
        return redirect()->route('order_confirmed');
    }

    /**
     * Checkout payment cancel handler
     */
    public function checkout_cancel(Request $request)
    {
        flash(translate('Payment was cancelled'))->warning();
        return redirect()->route('cart');
    }

    /**
     * Wallet recharge notification handler
     */
    public function wallet_notify(Request $request)
    {
        try {
            Log::info('Payfast wallet notification received: ', $request->all());

            if ($this->validatePayfastData($request->all())) {
                $payment_status = $request->payment_status;
                $pf_payment_id = $request->pf_payment_id;
                $amount_gross = $request->amount_gross;
                $custom_str1 = $request->custom_str1; // User ID

                if ($payment_status === 'COMPLETE') {
                    $this->processWalletRecharge($custom_str1, $pf_payment_id, $amount_gross);
                }

                return response('OK', 200);
            }

            return response('Invalid signature', 400);

        } catch (\Exception $e) {
            Log::error('Payfast wallet notification error: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

    /**
     * Wallet recharge return handler
     */
    public function wallet_return(Request $request)
    {
        flash(translate('Wallet recharged successfully'))->success();
        return redirect()->route('wallet.index');
    }

    /**
     * Wallet recharge cancel handler
     */
    public function wallet_cancel(Request $request)
    {
        flash(translate('Wallet recharge was cancelled'))->warning();
        return redirect()->route('wallet.index');
    }

    /**
     * Seller package payment notification handler
     */
    public function seller_package_notify(Request $request)
    {
        try {
            Log::info('Payfast seller package notification received: ', $request->all());

            if ($this->validatePayfastData($request->all())) {
                $payment_status = $request->payment_status;
                $pf_payment_id = $request->pf_payment_id;
                $amount_gross = $request->amount_gross;
                $custom_str1 = $request->custom_str1; // User ID
                $custom_str2 = $request->custom_str2; // Package ID

                if ($payment_status === 'COMPLETE') {
                    $this->processSellerPackagePayment($custom_str1, $custom_str2, $pf_payment_id, $amount_gross);
                }

                return response('OK', 200);
            }

            return response('Invalid signature', 400);

        } catch (\Exception $e) {
            Log::error('Payfast seller package notification error: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

    /**
     * Seller package payment return handler
     */
    public function seller_package_payment_return(Request $request)
    {
        flash(translate('Seller package purchased successfully'))->success();
        return redirect()->route('seller.seller_packages_list');
    }

    /**
     * Seller package payment cancel handler
     */
    public function seller_package_payment_cancel(Request $request)
    {
        flash(translate('Seller package purchase was cancelled'))->warning();
        return redirect()->route('seller.seller_packages_list');
    }

    /**
     * Customer package payment notification handler
     */
    public function customer_package_notify(Request $request)
    {
        try {
            Log::info('Payfast customer package notification received: ', $request->all());

            if ($this->validatePayfastData($request->all())) {
                $payment_status = $request->payment_status;
                $pf_payment_id = $request->pf_payment_id;
                $amount_gross = $request->amount_gross;
                $custom_str1 = $request->custom_str1; // User ID
                $custom_str2 = $request->custom_str2; // Package ID

                if ($payment_status === 'COMPLETE') {
                    $this->processCustomerPackagePayment($custom_str1, $custom_str2, $pf_payment_id, $amount_gross);
                }

                return response('OK', 200);
            }

            return response('Invalid signature', 400);

        } catch (\Exception $e) {
            Log::error('Payfast customer package notification error: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

    /**
     * Customer package payment return handler
     */
    public function customer_package_return(Request $request)
    {
        flash(translate('Customer package purchased successfully'))->success();
        return redirect()->route('customer_packages.index');
    }

    /**
     * Customer package payment cancel handler
     */
    public function customer_package_cancel(Request $request)
    {
        flash(translate('Customer package purchase was cancelled'))->warning();
        return redirect()->route('customer_packages.index');
    }

    /**
     * Validate Payfast ITN data
     */
    private function validatePayfastData($data)
    {
        try {
            // Remove the signature from the data
            $signature = $data['signature'] ?? '';
            unset($data['signature']);

            // Sort the data by key
            ksort($data);

            // Create parameter string
            $param_string = http_build_query($data);
            
            // Add passphrase if set
            if (!empty($this->passphrase)) {
                $param_string .= '&passphrase=' . urlencode($this->passphrase);
            }

            // Calculate signature
            $calculated_signature = md5($param_string);

            return $calculated_signature === $signature;

        } catch (\Exception $e) {
            Log::error('Payfast validation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Process checkout payment
     */
    private function processCheckoutPayment($combined_order_id, $payment_id, $amount)
    {
        try {
            $combined_order = CombinedOrder::find($combined_order_id);

            if ($combined_order) {
                $combined_order->payment_status = 'paid';
                $combined_order->payment_details = json_encode([
                    'payfast_payment_id' => $payment_id,
                    'amount' => $amount,
                    'payment_method' => 'Payfast'
                ]);
                $combined_order->save();

                // Update individual orders
                foreach ($combined_order->orders as $order) {
                    $order->payment_status = 'paid';
                    $order->payment_details = $combined_order->payment_details;
                    $order->save();
                }

                Log::info('Payfast checkout payment processed successfully: ' . $payment_id);
            }

        } catch (\Exception $e) {
            Log::error('Checkout payment processing error: ' . $e->getMessage());
        }
    }

    /**
     * Process wallet recharge
     */
    private function processWalletRecharge($user_id, $payment_id, $amount)
    {
        try {
            $user = User::find($user_id);

            if ($user) {
                $user->balance += $amount;
                $user->save();

                // Create wallet transaction record
                Wallet::create([
                    'user_id' => $user_id,
                    'amount' => $amount,
                    'payment_method' => 'Payfast',
                    'payment_details' => json_encode([
                        'payfast_payment_id' => $payment_id,
                        'amount' => $amount
                    ])
                ]);

                Log::info('Payfast wallet recharge processed successfully: ' . $payment_id);
            }

        } catch (\Exception $e) {
            Log::error('Wallet recharge processing error: ' . $e->getMessage());
        }
    }

    /**
     * Process seller package payment
     */
    private function processSellerPackagePayment($user_id, $package_id, $payment_id, $amount)
    {
        try {
            seller_purchase_payment_done($user_id, $package_id, $amount, 'Payfast', json_encode([
                'payfast_payment_id' => $payment_id,
                'amount' => $amount
            ]));

            Log::info('Payfast seller package payment processed successfully: ' . $payment_id);

        } catch (\Exception $e) {
            Log::error('Seller package payment processing error: ' . $e->getMessage());
        }
    }

    /**
     * Process customer package payment
     */
    private function processCustomerPackagePayment($user_id, $package_id, $payment_id, $amount)
    {
        try {
            customer_purchase_payment_done($user_id, $package_id);

            Log::info('Payfast customer package payment processed successfully: ' . $payment_id);

        } catch (\Exception $e) {
            Log::error('Customer package payment processing error: ' . $e->getMessage());
        }
    }

    /**
     * Generate payment form (for frontend use)
     */
    public function generatePaymentForm($payment_data)
    {
        try {
            $amount = $payment_data['amount'];
            $item_name = $payment_data['item_name'] ?? 'Payment';
            $custom_str1 = $payment_data['custom_str1'] ?? '';
            $custom_str2 = $payment_data['custom_str2'] ?? '';
            $payment_type = $payment_data['payment_type'] ?? 'checkout';

            // Determine URLs based on payment type
            $notify_url = $this->getNotifyUrl($payment_type);
            $return_url = $this->getReturnUrl($payment_type);
            $cancel_url = $this->getCancelUrl($payment_type);

            $data = [
                'merchant_id' => $this->merchant_id,
                'merchant_key' => $this->merchant_key,
                'return_url' => $return_url,
                'cancel_url' => $cancel_url,
                'notify_url' => $notify_url,
                'name_first' => auth()->user()->name ?? 'Customer',
                'email_address' => auth()->user()->email ?? '<EMAIL>',
                'item_name' => $item_name,
                'amount' => number_format($amount, 2, '.', ''),
                'custom_str1' => $custom_str1,
                'custom_str2' => $custom_str2,
            ];

            // Add passphrase if set
            if (!empty($this->passphrase)) {
                $param_string = http_build_query($data) . '&passphrase=' . urlencode($this->passphrase);
            } else {
                $param_string = http_build_query($data);
            }

            $data['signature'] = md5($param_string);

            return [
                'action_url' => $this->getPayfastUrl(),
                'form_data' => $data
            ];

        } catch (\Exception $e) {
            Log::error('Payfast form generation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Payfast URL based on environment
     */
    private function getPayfastUrl()
    {
        return $this->sandbox 
            ? 'https://sandbox.payfast.co.za/eng/process' 
            : 'https://www.payfast.co.za/eng/process';
    }

    /**
     * Get notify URL based on payment type
     */
    private function getNotifyUrl($payment_type)
    {
        switch ($payment_type) {
            case 'wallet':
                return route('payfast.wallet.notify');
            case 'seller_package':
                return route('payfast.seller_package_payment.notify');
            case 'customer_package':
                return route('payfast.customer_package_payment.notify');
            default:
                return route('payfast.checkout.notify');
        }
    }

    /**
     * Get return URL based on payment type
     */
    private function getReturnUrl($payment_type)
    {
        switch ($payment_type) {
            case 'wallet':
                return route('payfast.wallet.return');
            case 'seller_package':
                return route('payfast.seller_package_payment.return');
            case 'customer_package':
                return route('payfast.customer_package_payment.return');
            default:
                return route('payfast.checkout.return');
        }
    }

    /**
     * Get cancel URL based on payment type
     */
    private function getCancelUrl($payment_type)
    {
        switch ($payment_type) {
            case 'wallet':
                return route('payfast.wallet.cancel');
            case 'seller_package':
                return route('payfast.seller_package_payment.cancel');
            case 'customer_package':
                return route('payfast.customer_package_payment.cancel');
            default:
                return route('payfast.checkout.cancel');
        }
    }

    /**
     * Verify payment status with Payfast
     */
    public function verifyPayment($payment_id)
    {
        try {
            // Payfast doesn't have a direct API to verify payments
            // This would typically be handled through ITN (Instant Transaction Notification)
            // For now, return a placeholder response
            
            return [
                'success' => true,
                'payment_id' => $payment_id,
                'status' => 'verified'
            ];

        } catch (\Exception $e) {
            Log::error('Payfast payment verification error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Verification failed'
            ];
        }
    }
} 