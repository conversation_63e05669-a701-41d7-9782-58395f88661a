<?php

namespace App\Listeners;

use App\Enums\OrderStatus;
use App\Models\CancelReason;
use App\Models\OrderDetail;
use App\Models\User;
use App\Notifications\order\DeliveredEmailNotification;
use App\Notifications\order\OnTheWayEmailNotification;
use App\Notifications\order\OrderCanceledEmailNotification;
use App\Notifications\order\OrderPlacedMailNotification;
use App\Notifications\order\ReadyToBeShippedEmailNotification;
use App\Notifications\order\ReshippedEmailNotification;
use App\Services\ActivityLogService;
use App\Services\NotificationService;
use App\Models\Order;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
//implements ShouldQueue
class NotificationEventListenerV2
{
    use InteractsWithQueue;

    protected $notificationService;
    protected ActivityLogService $activityLogService;

    public function __construct(ActivityLogService $activityLogService,NotificationService $notificationService)
    {
        $this->activityLogService = $activityLogService;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle order created events
     */
    public function handleOrderCreated($event)
    {
        try {
            $order = $event->order ?? $event;

            if (!$order instanceof Order) {
                Log::warning('NotificationEventListenerV2: Invalid order instance provided for order created event');
                return;
            }

            // Ensure order has user relationship loaded
            if (!$order->relationLoaded('user')) {
                $order->load('user');
            }

            $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;

            // Notify admin about new order
            $this->notificationService->sendAdminNotification(
                'New Order Received',
                "New order #{$order->code} has been placed by " . ($order->user->name ?? 'Customer'),
                NotificationType::ORDER,
                NotificationPriority::HIGH,
                "/{$userRole}/orders/{$order->code}",
                'View Order',
                $order,
                $order->user_id
            );

            // Confirm order placement to customer
            $this->notificationService->sendNotification(
                $order->user,
                'Order Placed Successfully',
                "Your order #{$order->code} has been placed successfully. We will notify you of any updates.",
                NotificationType::ORDER,
                NotificationPriority::HIGH,
                "/{$userRole}/orders/{$order->code}",
                'View Order',
                $order
            );
            $this->activityLogService->log(
                'order_created',
                'Customer Order Created',
                $order->id,
                Order::class,
                $order->user_id,
                get_class(auth()->user()),
                '',
                OrderStatus::PENDING,
                null,
                null
            );

            Log::info("NotificationEventListenerV2: Order created notifications sent for order #{$order->code}");

        } catch (\Exception $e) {
            Log::error('NotificationEventListenerV2: Error handling order created event: ' . $e->getMessage(), [
                'order_id' => $order->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle order updated events
     */
    public function handleOrderUpdated($event)
    {
        try {
            $order = $event->order ?? $event;

            if (!$order instanceof Order) {
                Log::warning('NotificationEventListenerV2: Invalid order instance provided for order updated event');
                return;
            }

            // Ensure order has user relationship loaded
            if (!$order->relationLoaded('user')) {
                $order->load('user');
            }

            // Check if status has changed
            if ($order->wasChanged('delivery_status') && $order->delivery_status !== 'order_placed') {
                $this->handleDeliveryStatusChange($order);
                Log::info("NotificationEventListenerV2: Order updated notifications processed for order #{$order->code}");
                $this->sendOrderStatusChangeEmail($order,$order->user);
            }

            // Check if payment status changed to paid
            if ($order->wasChanged('payment_status') && $order->payment_status === 'paid') {
                $this->handlePaymentStatusChange($order);
                $this->sendOrderStatusChangeEmail($order,$order->user);
            }



        } catch (\Exception $e) {
            Log::error('NotificationEventListenerV2: Error handling order updated event: ' . $e->getMessage(), [
                'order_id' => $order->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle order status changes
     */
    private function handleDeliveryStatusChange(Order $order)
    {
        $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;
        $oldStatus = $order->getOriginal('delivery_status');
        Log::info('order status change from : ' . $oldStatus. ' to ' . $order->delivery_status . ' for order ' . $order->code);
        $email_end_time = null;
        if ($order->delivery_status == 'on_the_way') {
            $email_end_time = Carbon::now()->addMinutes(env("DELIVERY_DALY_EMAIL_MINUTES", 2880));
        }

        // Notify customer about status change
        $this->notificationService->sendNotification(
            $order->user,
            'Order Status Updated',
            "Your order #{$order->code} status has been updated to: " . ucfirst($order->delivery_status),
            NotificationType::ORDER,
            NotificationPriority::MEDIUM,
            "/{$userRole}/orders/{$order->code}",
            'View Order',
            $order
        );
        $this->activityLogService->log(
            'order_status_changed',
            'Order delivery status changed',
            $order->id,
            Order::class,
            auth()->user()->id,
            get_class(auth()->user()),
            $oldStatus,
            $order->delivery_status,
            null,
            email_end_time: $email_end_time,
        );


    }

    /**
     * Handle payment status changes
     */
    private function handlePaymentStatusChange(Order $order)
    {
        $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;

        // Notify customer about successful payment
        $this->notificationService->sendNotification(
            $order->user,
            'Payment Confirmed',
            "Your payment for order #{$order->code} has been successfully processed.",
            NotificationType::ORDER,
            NotificationPriority::HIGH,
            "/{$userRole}/orders/{$order->code}",
            'View Order',
            $order
        );
        $this->activityLogService->log(
            'order_status_changed',
            'Customer Make Payment',
            $order->id,
            Order::class,
            auth()->user()->id,
            get_class(auth()->user()),
            OrderStatus::PENDING,
            OrderStatus::ORDER_PLACED,
            null,
            email_end_time: null,
        );
    }
    /**
     * Send appropriate email based on order status
     */
    private function sendOrderStatusChangeEmail(Order $order, User $user): void
    {
        $array = [
            'order' => $order,
            'user_name' => $user->name
        ];

        try {
            switch ($order->delivery_status) {
                case 'order_placed':
                    Log::error('NotificationEventListenerV2: Sending order placed email for order ' . $order->code);
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' Has Been Placed Successfully! ';
                    $user->notify(new OrderPlacedMailNotification($array));
                    break;
                case 'picked_up':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' is Ready to Be Shipped! ';
                    $user->notify(new ReadyToBeShippedEmailNotification($array));
                    break;
                case 'reshipped':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . '  Has Been Resent – New Tracking Details ';
                    $user->notify(new ReshippedEmailNotification($array));
                    break;
                case 'on_the_way':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' is On Its Way! ';
                    $user->notify(new OnTheWayEmailNotification($array));
                    break;
                case 'delivered':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' Has Been Delivered! ';
                    /*$order->review_email = Carbon::now()->addMinutes(env("REVIEW_EMAIL_MINUTES", 4320));
                    $order->save();*/
                    $user->notify(new DeliveredEmailNotification($array));
                    break;
                case 'cancelled':
                    $array['subject'] = translate('Update on Your Order ') . ' - ' . $order->code . ' ' . ' Canceled';
                    $cancel_reason_id = OrderDetail::where('order_id', $order->id)->first()->cancel_reason;
                    $array['cancel_reason'] = CancelReason::where('id', $cancel_reason_id)->first()->reason_code;
                    $array['cancel_by'] = 'Admin';
                    $user->notify(new OrderCanceledEmailNotification($array));
                    break;
            }

            $this->queueEmailWorker();
        } catch (\Exception $e) {
            Log::channel('email_logs')->error('Error sending email for status ' . $order->delivery_status . ': ' . $e->getMessage());
        }
    }
    /**
     * Queue email worker to process emails
     */
    private function queueEmailWorker(): void
    {
        $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
        exec($command);
    }
    /**
     * Handle failed jobs
     */
    public function failed($event, $exception)
    {
        Log::error('NotificationEventListenerV2: Job failed', [
            'event' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
