<?php

namespace App\Http\Resources\V3\Cart;

use App\Helpers\ProductPriceHelper;
use Illuminate\Http\Resources\Json\JsonResource;

class CartItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Use the stored price in cart instead of recalculating to ensure consistency
        $storedPrice = $this->price;
        
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product' => [
                'id' => $this->product->id,
                'name' => $this->product->name,
                'slug' => $this->product->slug,
                'thumbnail' => uploaded_asset($this->product->thumbnail_img),
                'price' => $storedPrice,
                'regularPrice' => (float)$this->product->unit_price,
                'has_discount' => $this->product->discount > 0,
                'discount_value' => (float)$this->product->discount,
                'stock_quantity' => (int)$this->product->current_stock
            ],
            'quantity' => (int)$this->quantity,
            'variation' => $this->variation,
            'total_price' => $storedPrice * $this->quantity,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }

    /**
     * Parse variation options from variation string
     *
     * @param string|null $variation
     * @return array|null
     */
    protected function parseVariationOptions($variation)
    {
        if (!$variation) {
            return null;
        }

        $options = [];
        $parts = explode('-', $variation);

        foreach ($parts as $part) {
            $keyValue = explode(':', $part);
            if (count($keyValue) === 2) {
                $options[$keyValue[0]] = $keyValue[1];
            }
        }

        return $options;
    }
}
