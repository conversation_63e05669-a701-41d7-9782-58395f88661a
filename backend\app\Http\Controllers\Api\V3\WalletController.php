<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\ExchangeRate;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WalletController extends Controller
{
    /**
     * Get user wallet details including transactions and reward points
     */
    public function getWalletActivities(Request $request)
    {
        $user = Auth::user();
        
        // Get or create wallet
        $wallet = Wallet::firstOrCreate(
            ['user_id' => $user->id],
            [
                'balance' => 0.00,
                'reward_points' => 0,
                'promotional_credits' => 0.00,
                'status' => 'active'
            ]
        );
        
        // Get transactions with pagination if needed
        $perPage = $request->input('per_page', 10);
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->when($request->has('page'), function($query) use ($perPage) {
                return $query->paginate($perPage);
            }, function($query) {
                return $query->limit(20)->get();
            });
            
        // Get reward points transactions
        $rewardPointsHistory = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->orderBy('created_at', 'desc')
            ->when($request->has('rewards_page'), function($query) use ($perPage) {
                return $query->paginate($perPage, ['*'], 'rewards_page');
            }, function($query) {
                return $query->limit(20)->get();
            });
            
        // Get promotional credits (active only)
        $promotionalCredits = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'promotional_credits')
            ->where('type', 'credit')
            ->where(function($q) {
                $q->whereNull('metadata->expiry_date')
                  ->orWhere(DB::raw("JSON_EXTRACT(metadata, '$.expiry_date')"), '>=', now()->toDateTimeString());
            })
            ->get();
            
        // Get exchange rates
        $exchangeRates = ExchangeRate::where('status', 'active')
            ->whereDate('valid_from', '<=', now())
            ->where(function($query) {
                $query->whereDate('valid_until', '>=', now())
                      ->orWhereNull('valid_until');
            })
            ->get();
            
        // Map exchange rates to object
        $exchangeRatesObject = [];
        foreach ($exchangeRates as $rate) {
            $exchangeRatesObject[$rate->id] = [
                'points' => $rate->points,
                'currency_amount' => $rate->currency_amount,
                'ratio' => $rate->points / $rate->currency_amount
            ];
        }
        
        // Calculate reward summary
        $rewardSummary = [
            'totalPoints' => $wallet->reward_points,
            'availablePoints' => $wallet->reward_points,
            'pendingPoints' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'expiringPoints' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('type', 'credit')
                ->where(DB::raw("JSON_EXTRACT(metadata, '$.expiry_date')"), '!=', null)
                ->where(DB::raw("JSON_EXTRACT(metadata, '$.expiry_date')"), '>=', now()->toDateTimeString())
                ->where(DB::raw("JSON_EXTRACT(metadata, '$.expiry_date')"), '<=', now()->addMonths(3)->toDateTimeString())
                ->sum('amount'),
            'conversionRate' => $exchangeRates->count() > 0 ? $exchangeRates->first()->points / $exchangeRates->first()->currency_amount : 100,
            'pointsEarnedLifetime' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('type', 'credit')
                ->sum('amount'),
            'pointsRedeemedLifetime' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('type', 'debit')
                ->sum('amount'),
            'pointsExpiredLifetime' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where(DB::raw("JSON_EXTRACT(metadata, '$.status')"), '=', 'expired')
                ->sum('amount')
        ];
        
        // Calculate balance breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('source', 'refund')
                ->where('type', 'credit')
                ->sum('amount'),
            'bonuses' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('source', 'reward')
                ->where('type', 'credit')
                ->sum('amount'),
            'promotionalCredits' => $wallet->promotional_credits,
            'other' => $wallet->balance - 
                WalletTransaction::where('user_id', $user->id)
                    ->where('balance_type', 'balance')
                    ->whereIn('source', ['refund', 'reward'])
                    ->where('type', 'credit')
                    ->sum('amount') - 
                $wallet->promotional_credits
        ];
        
        // Format wallet balance
        $walletBalance = [
            'totalBalance' => $wallet->balance,
            'availableBalance' => $wallet->balance,
            'pendingCredits' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'pendingDebits' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'debit')
                ->sum('amount'),
            'currency' => get_system_default_currency()->code,
            'breakdown' => $balanceBreakdown
        ];
        
        // Map wallet transactions to match frontend interface
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->source,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });
        
        // Map reward points history to match frontend interface
        $formattedRewardHistory = $rewardPointsHistory->map(function($transaction) {
            $type = '';
            if ($transaction->type === 'credit') {
                $type = 'earned';
            } elseif (isset($transaction->metadata['status']) && $transaction->metadata['status'] === 'expired') {
                $type = 'expired';
            } else {
                $type = 'redeemed';
            }
            
            return [
                'id' => $transaction->id,
                'points' => $transaction->amount,
                'type' => $type,
                'description' => $transaction->description,
                'source' => $transaction->source,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'expiryDate' => isset($transaction->metadata['expiry_date']) ? $transaction->metadata['expiry_date'] : null,
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });
        
        // Map promotional credits to match frontend interface
        $formattedPromotionalCredits = $promotionalCredits->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'description' => $transaction->description,
                'startDate' => $transaction->created_at->toIso8601String(),
                'expiryDate' => isset($transaction->metadata['expiry_date']) ? $transaction->metadata['expiry_date'] : null,
                'isActive' => true,
                'usageLimit' => $transaction->metadata['usage_limit'] ?? null,
                'usageCount' => $transaction->metadata['usage_count'] ?? 0,
                'minPurchaseAmount' => $transaction->metadata['min_purchase_amount'] ?? null,
                'applicableCategories' => $transaction->metadata['applicable_categories'] ?? null
            ];
        });
        
        // Return response
        return response()->json([
            'success' => true,
            'transactions' => $formattedTransactions instanceof \Illuminate\Pagination\LengthAwarePaginator ? 
                $formattedTransactions->items() : $formattedTransactions,
            'balance' => $walletBalance,
            'rewards' => $rewardSummary,
            'rewardHistory' => $formattedRewardHistory instanceof \Illuminate\Pagination\LengthAwarePaginator ? 
                $formattedRewardHistory->items() : $formattedRewardHistory,
            'promotionalCredits' => $formattedPromotionalCredits,
            'exchangeRates' => $exchangeRatesObject,
            'pagination' => $transactions instanceof \Illuminate\Pagination\LengthAwarePaginator ? [
                'total' => $transactions->total(),
                'currentPage' => $transactions->currentPage(),
                'totalPages' => $transactions->lastPage(),
                'perPage' => $transactions->perPage(),
            ] : null,
            'rewardPagination' => $rewardPointsHistory instanceof \Illuminate\Pagination\LengthAwarePaginator ? [
                'total' => $rewardPointsHistory->total(),
                'currentPage' => $rewardPointsHistory->currentPage(),
                'totalPages' => $rewardPointsHistory->lastPage(),
                'perPage' => $rewardPointsHistory->perPage(),
            ] : null,
        ]);
    }
    
    /**
     * Get wallet transactions with pagination
     */
    public function getWalletTransactions(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->when($request->input('type'), function($query, $type) {
                return $query->where('type', $type);
            })
            ->when($request->input('source'), function($query, $source) {
                return $query->where('source', $source);
            })
            ->when($request->input('balance_type'), function($query, $balanceType) {
                return $query->where('balance_type', $balanceType);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
            
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'amount' => $transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->source,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });
        
        return response()->json([
            'success' => true,
            'transactions' => $formattedTransactions,
            'totalPages' => $transactions->lastPage(),
            'currentPage' => $transactions->currentPage()
        ]);
    }
    
    /**
     * Get reward points history with pagination
     */
    public function getRewardPointsHistory(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->input('per_page', 10);
        $page = $request->input('page', 1);
        
        $rewardPointsHistory = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->when($request->input('type'), function($query, $type) {
                if ($type === 'earned') {
                    return $query->where('type', 'credit');
                } elseif ($type === 'redeemed') {
                    return $query->where('type', 'debit')
                        ->whereRaw("JSON_EXTRACT(metadata, '$.status') != 'expired' OR JSON_EXTRACT(metadata, '$.status') IS NULL");
                } elseif ($type === 'expired') {
                    return $query->whereRaw("JSON_EXTRACT(metadata, '$.status') = 'expired'");
                }
                return $query;
            })
            ->when($request->input('source'), function($query, $source) {
                return $query->where('source', $source);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);
            
        $formattedRewardHistory = $rewardPointsHistory->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'points' => $transaction->amount,
                'type' => $transaction->type === 'credit' ? 'earned' : (($transaction->metadata['status'] ?? '') === 'expired' ? 'expired' : 'redeemed'),
                'description' => $transaction->description,
                'source' => $transaction->source,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'expiryDate' => isset($transaction->metadata['expiry_date']) ? $transaction->metadata['expiry_date'] : null,
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });
        
        return response()->json([
            'success' => true,
            'rewardHistory' => $formattedRewardHistory,
            'totalPages' => $rewardPointsHistory->lastPage(),
            'currentPage' => $rewardPointsHistory->currentPage()
        ]);
    }
    
    /**
     * Redeem reward points for wallet credit
     */
    public function redeemRewardPoints(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'points' => 'required|integer|min:1',
            'exchange_rate_id' => 'required|exists:exchange_rates,id'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        $points = $request->input('points');
        
        // Get user wallet
        $wallet = Wallet::where('user_id', $user->id)->first();
        
        if (!$wallet || $wallet->reward_points < $points) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient reward points'
            ], 400);
        }
        
        // Get exchange rate
        $exchangeRate = ExchangeRate::findOrFail($request->input('exchange_rate_id'));
        
        if ($exchangeRate->status !== 'active' || 
            ($exchangeRate->valid_from && $exchangeRate->valid_from > now()) ||
            ($exchangeRate->valid_until && $exchangeRate->valid_until < now())) {
            return response()->json([
                'success' => false,
                'message' => 'Exchange rate is not active or valid'
            ], 400);
        }
        
        // Calculate credit amount
        $creditAmount = ($points / $exchangeRate->points) * $exchangeRate->currency_amount;
        
        try {
            DB::beginTransaction();
            
            // Deduct reward points
            $wallet->reward_points -= $points;
            $wallet->save();
            
            // Record reward points deduction
            $rewardDebitTransaction = new WalletTransaction();
            $rewardDebitTransaction->wallet_id = $wallet->id;
            $rewardDebitTransaction->user_id = $user->id;
            $rewardDebitTransaction->type = 'debit';
            $rewardDebitTransaction->amount = $points;
            $rewardDebitTransaction->balance_type = 'reward_points';
            $rewardDebitTransaction->source = 'reward';
            $rewardDebitTransaction->description = 'Redeemed ' . $points . ' points for wallet credit';
            $rewardDebitTransaction->approval_status = 'approved';
            $rewardDebitTransaction->save();
            
            // Add wallet credit
            $wallet->balance += $creditAmount;
            $wallet->save();
            
            // Record wallet credit
            $walletCreditTransaction = new WalletTransaction();
            $walletCreditTransaction->wallet_id = $wallet->id;
            $walletCreditTransaction->user_id = $user->id;
            $walletCreditTransaction->type = 'credit';
            $walletCreditTransaction->amount = $creditAmount;
            $walletCreditTransaction->balance_type = 'balance';
            $walletCreditTransaction->source = 'reward';
            $walletCreditTransaction->description = 'Converted ' . $points . ' reward points to wallet credit';
            $walletCreditTransaction->reference_type = 'reward_points_redemption';
            $walletCreditTransaction->reference_id = $rewardDebitTransaction->id;
            $walletCreditTransaction->approval_status = 'approved';
            $walletCreditTransaction->save();
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Successfully redeemed reward points',
                'points_redeemed' => $points,
                'credit_amount' => $creditAmount,
                'new_balance' => $wallet->balance,
                'new_reward_points' => $wallet->reward_points
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Error redeeming reward points: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Apply wallet balance to order during checkout
     */
    public function applyWalletToOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'order_id' => 'required|exists:orders,id'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        $amount = $request->input('amount');
        $orderId = $request->input('order_id');
        
        // Get user wallet
        $wallet = Wallet::where('user_id', $user->id)->first();
        
        if (!$wallet || $wallet->balance < $amount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient wallet balance'
            ], 400);
        }
        
        try {
            DB::beginTransaction();
            
            // Deduct from wallet
            $wallet->balance -= $amount;
            $wallet->save();
            
            // Record wallet transaction
            $transaction = new WalletTransaction();
            $transaction->wallet_id = $wallet->id;
            $transaction->user_id = $user->id;
            $transaction->type = 'debit';
            $transaction->amount = $amount;
            $transaction->balance_type = 'balance';
            $transaction->source = 'purchase';
            $transaction->description = 'Applied wallet balance to order #' . $orderId;
            $transaction->reference_type = 'order';
            $transaction->reference_id = $orderId;
            $transaction->approval_status = 'approved';
            $transaction->save();
            
            // Update order
            $order = \App\Models\Order::findOrFail($orderId);
            $order->wallet_amount = $amount;
            $order->grand_total -= $amount;
            $order->save();
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Wallet balance applied to order',
                'amount_applied' => $amount,
                'new_wallet_balance' => $wallet->balance,
                'new_order_total' => $order->grand_total
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Error applying wallet balance: ' . $e->getMessage()
            ], 500);
        }
    }
} 