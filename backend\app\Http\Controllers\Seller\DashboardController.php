<?php

namespace App\Http\Controllers\Seller;

use App\Models\Order;
use App\Models\User; //added by suman
use App\Models\Shop; //added by suman
use App\Models\Product;
use Auth;
use Carbon\Carbon;
use DB;

class DashboardController extends Controller
{
    public function index()
    {
    
        //dd('dd');
        
        $cID= auth()->user()->id;
   
        //dd($cID);
      
        $shopModel = Shop::where('user_id', $cID )->first();
        //dd($shopModel->verification_status);
        if($shopModel->verification_status == 0){
           return view('seller.waiting_for_approval');
        }else{
            $data['products'] = filter_products(Product::where('user_id', Auth::user()->id)->orderBy('num_of_sale', 'desc'))->limit(12)->get();
            $data['last_7_days_sales'] = Order::where('created_at', '>=', Carbon::now()->subDays(7))
                                    ->where('seller_id', '=', Auth::user()->id)
                                    ->where('delivery_status', '=', 'delivered')
                                    ->select(DB::raw("sum(grand_total) as total, DATE_FORMAT(created_at, '%d %b') as date"))
                                    ->groupBy(DB::raw("DATE_FORMAT(created_at, '%Y-%m-%d')"))
                                    ->get()->pluck('total', 'date');  
            return view('seller.dashboard', $data);
        }
    }
}
