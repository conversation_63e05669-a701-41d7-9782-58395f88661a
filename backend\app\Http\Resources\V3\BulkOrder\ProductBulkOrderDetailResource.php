<?php

namespace App\Http\Resources\V3\BulkOrder;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductBulkOrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $stock = $this->stocks->first();
        $bulkPricingTiers = [];

        $bulkPricingTiers[] = [
            'minimumQuantity' => $this->min_qty,
            'price' => $this->wholesale_price
        ];
        $price = product_price_object($this);
        $dropshipper_price =product_dropshipper_price_object($this) ;
        $wholesales_price =product_wholesales_price_object($this) ;
        return [

                'id' => $this->id,
                'name' => $this->name,
                'description' => $this->description,
                'price' => $price,
                'dropshipper_price' => $dropshipper_price,
                'wholesales_price' => $wholesales_price,
                'thumbnail' => uploaded_asset($this->thumbnail_img),
                'featuredImage' => uploaded_asset($this->featured_img),
                'images' => array_map(function($img) {
                    return uploaded_asset($img);
                }, explode(',', $this->photos)),
                'brand' => $this->brand ? $this->brand->name : null,
                'category' => $this->category ? $this->category->name : null,
                'stock' => $stock ? $stock->qty : 0,
                'discount' => $this->discount,
                'inStock' => ($stock && $stock->qty > 0),
                'rating' => $this->rating,
                'reviewCount' => (int)$this->getApprovedReviewCount(),
                'minimumOrderQuantity' => $this->min_qty,
                'stockQuantity' => $this->getTotalStockQuantity(),
                'bulkPricingTiers' => $bulkPricingTiers

        ];
    }
}
