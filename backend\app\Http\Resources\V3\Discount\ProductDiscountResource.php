<?php

namespace App\Http\Resources\V3\Discount;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductDiscountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'discount_type' => $this->discount_type,
            'discount_value' => (float) $this->discount_value,
            'start_date' => isset($this->start_date) ? $this->start_date->toIso8601String() : null,
            'end_date' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'is_active' => (bool) $this->is_active,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 