<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/backend/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/backend/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/backend/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/backend/tests/Unit" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/aiz-packages/color-code-converter" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/aiz-packages/combination-generate" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/anandsiddharth/laravel-paytm-wallet" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/authorizenet/authorizenet" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/bacon/bacon-qr-code" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/barryvdh/laravel-debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/barryvdh/laravel-ide-helper" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/barryvdh/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/dasprid/enum" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/annotations" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/common" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/event-manager" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/doctrine/persistence" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/enshrined/svg-sanitize" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/ezyang/htmlpurifier" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/genealabs/laravel-overridable-model" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/genealabs/laravel-sign-in-with-apple" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/genealabs/laravel-socialiter" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/giggsey/libphonenumber-for-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/giggsey/locale" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/instamojo/instamojo-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/iyzico/iyzipay-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/jaybizzle/crawler-detect" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/jenssegers/agent" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/kingflamez/laravelrave" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laracasts/flash" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/laravel/ui" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/lcobucci/clock" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/flysystem-aws-s3-v3" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/league/oauth1-client" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/maatwebsite/excel" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/maennchen/zipstream-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/markbaker/complex" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/markbaker/matrix" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mehedi-iitdu/core-component-repository" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mercadopago/dx-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mobiledetect/mobiledetectlib" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mpdf/mpdf" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mpdf/psr-http-message-shim" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mpdf/psr-log-aware-trait" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/myfatoorah/laravel-package" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/myfatoorah/library" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/niklasravnsborg/laravel-pdf" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/paragonie/sodium_compat" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/paypal/paypal-checkout-sdk" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/paypal/paypalhttp" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/php-debugbar/php-debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpoffice/phpspreadsheet" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/predis/predis" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/pusher/pusher-php-server" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/rap2hpoutre/laravel-log-viewer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/razorpay/razorpay" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/rmccue/requests" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebacarrasco93/laravel-payku" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/setasign/fpdi" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/simplesoftwareio/simple-qrcode" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/db-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/error-solutions" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/laravel-activitylog" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/laravel-package-tools" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/spatie/laravel-permission" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/stripe/stripe-php" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/thanks" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/twilio/sdk" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/unicodeveloper/laravel-paystack" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/backend/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>