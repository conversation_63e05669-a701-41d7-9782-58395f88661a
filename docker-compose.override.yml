# Docker Compose Override for Laravel Backend Development
# This file simplifies the setup by defining only the essential services

version: '3.8'

services:
  # Essential services for Laravel development
  workspace:
    # Workspace container with PHP 8.2, Composer, Node.js
    profiles: ["backend"]
    
  php-fpm:
    # PHP-FPM for processing PHP requests
    profiles: ["backend"]
    
  nginx:
    # Web server
    profiles: ["backend"]
    
  mysql:
    # Database server
    profiles: ["backend"]
    
  phpmyadmin:
    # Database management interface
    profiles: ["backend"]

# You can start all backend services with:
# docker-compose --profile backend up -d

# Or use the provided script:
# ./docker-start.sh
