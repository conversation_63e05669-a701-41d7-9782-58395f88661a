<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8d3ef8bc-97f6-4c1b-907f-c3d76e26edff" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/laradock/nginx/sites/default.conf" beforeDir="false" afterPath="$PROJECT_DIR$/laradock/nginx/sites/default.conf" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerConfigs">
    <option name="configs">
      <option value="$PROJECT_DIR$/laradock/workspace/composer.json" />
    </option>
  </component>
  <component name="ComposerSettings" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/backend/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="$PROJECT_DIR$/../../xampp-php-7.4/php/php.exe">
    <include_path>
      <path value="$PROJECT_DIR$/backend/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/backend/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/backend/vendor/unicodeveloper/laravel-paystack" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/backend/vendor/genealabs/laravel-sign-in-with-apple" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/backend/vendor/genealabs/laravel-socialiter" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/backend/vendor/genealabs/laravel-overridable-model" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/backend/vendor/mercadopago/dx-php" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/backend/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/backend/vendor/paypal/paypal-checkout-sdk" />
      <path value="$PROJECT_DIR$/backend/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/backend/vendor/ezyang/htmlpurifier" />
      <path value="$PROJECT_DIR$/backend/vendor/paypal/paypalhttp" />
      <path value="$PROJECT_DIR$/backend/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/backend/vendor/niklasravnsborg/laravel-pdf" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/annotations" />
      <path value="$PROJECT_DIR$/backend/vendor/mpdf/mpdf" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/common" />
      <path value="$PROJECT_DIR$/backend/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/persistence" />
      <path value="$PROJECT_DIR$/backend/vendor/doctrine/event-manager" />
      <path value="$PROJECT_DIR$/backend/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/backend/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/backend/vendor/mpdf/psr-http-message-shim" />
      <path value="$PROJECT_DIR$/backend/vendor/composer" />
      <path value="$PROJECT_DIR$/backend/vendor/mpdf/psr-log-aware-trait" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/backend/vendor/jenssegers/agent" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/ui" />
      <path value="$PROJECT_DIR$/backend/vendor/laravel/socialite" />
      <path value="$PROJECT_DIR$/backend/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/backend/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/backend/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/backend/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/backend/vendor/simplesoftwareio/simple-qrcode" />
      <path value="$PROJECT_DIR$/backend/vendor/paragonie/sodium_compat" />
      <path value="$PROJECT_DIR$/backend/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/backend/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/backend/vendor/bacon/bacon-qr-code" />
      <path value="$PROJECT_DIR$/backend/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/backend/vendor/kingflamez/laravelrave" />
      <path value="$PROJECT_DIR$/backend/vendor/twilio/sdk" />
      <path value="$PROJECT_DIR$/backend/vendor/maatwebsite/excel" />
      <path value="$PROJECT_DIR$/backend/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/backend/vendor/barryvdh/laravel-ide-helper" />
      <path value="$PROJECT_DIR$/backend/vendor/barryvdh/laravel-debugbar" />
      <path value="$PROJECT_DIR$/backend/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/backend/vendor/barryvdh/reflection-docblock" />
      <path value="$PROJECT_DIR$/backend/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/backend/vendor/lcobucci/clock" />
      <path value="$PROJECT_DIR$/backend/vendor/instamojo/instamojo-php" />
      <path value="$PROJECT_DIR$/backend/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/backend/vendor/jaybizzle/crawler-detect" />
      <path value="$PROJECT_DIR$/backend/vendor/dasprid/enum" />
      <path value="$PROJECT_DIR$/backend/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/backend/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/backend/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/backend/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/backend/vendor/predis/predis" />
      <path value="$PROJECT_DIR$/backend/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/backend/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/backend/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/backend/vendor/league/config" />
      <path value="$PROJECT_DIR$/backend/vendor/league/oauth1-client" />
      <path value="$PROJECT_DIR$/backend/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/backend/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/backend/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/backend/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/backend/vendor/laracasts/flash" />
      <path value="$PROJECT_DIR$/backend/vendor/rap2hpoutre/laravel-log-viewer" />
      <path value="$PROJECT_DIR$/backend/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/backend/vendor/mehedi-iitdu/core-component-repository" />
      <path value="$PROJECT_DIR$/backend/vendor/maennchen/zipstream-php" />
      <path value="$PROJECT_DIR$/backend/vendor/rmccue/requests" />
      <path value="$PROJECT_DIR$/backend/vendor/mobiledetect/mobiledetectlib" />
      <path value="$PROJECT_DIR$/backend/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/backend/vendor/aiz-packages/combination-generate" />
      <path value="$PROJECT_DIR$/backend/vendor/aiz-packages/color-code-converter" />
      <path value="$PROJECT_DIR$/backend/vendor/myfatoorah/library" />
      <path value="$PROJECT_DIR$/backend/vendor/brick/math" />
      <path value="$PROJECT_DIR$/backend/vendor/myfatoorah/laravel-package" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/backend/vendor/anandsiddharth/laravel-paytm-wallet" />
      <path value="$PROJECT_DIR$/backend/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/backend/vendor/setasign/fpdi" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/log" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/backend/vendor/authorizenet/authorizenet" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/backend/vendor/psr/container" />
      <path value="$PROJECT_DIR$/backend/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/backend/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/backend/vendor/iyzico/iyzipay-php" />
      <path value="$PROJECT_DIR$/backend/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/backend/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/backend/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/backend/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/backend/vendor/phpoffice/phpspreadsheet" />
      <path value="$PROJECT_DIR$/backend/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/backend/vendor/php-debugbar/php-debugbar" />
      <path value="$PROJECT_DIR$/backend/vendor/sebacarrasco93/laravel-payku" />
      <path value="$PROJECT_DIR$/backend/vendor/razorpay/razorpay" />
      <path value="$PROJECT_DIR$/backend/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/thanks" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/backend/vendor/pusher/pusher-php-server" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/backend/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/backend/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/backend/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/backend/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/backend/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/error-solutions" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/laravel-permission" />
      <path value="$PROJECT_DIR$/backend/vendor/giggsey/locale" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/backend/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/db-dumper" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/backend/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/backend/vendor/giggsey/libphonenumber-for-php" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/backend/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/backend/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/laravel-activitylog" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/backend/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/backend/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/backend/vendor/markbaker/matrix" />
      <path value="$PROJECT_DIR$/backend/vendor/enshrined/svg-sanitize" />
      <path value="$PROJECT_DIR$/backend/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/backend/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/backend/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/backend/vendor/stripe/stripe-php" />
      <path value="$PROJECT_DIR$/backend/vendor/markbaker/complex" />
    </include_path>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="30NQAg2qpwsbnBdoGQPm3kYcjCT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Main" type="PHPUnitRunConfigurationType" factoryName="PHPUnit">
      <TestRunner configuration_file="$PROJECT_DIR$/backend/phpunit.xml" scope="XML" use_alternative_configuration_file="true" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PS-251.26927.60" />
        <option value="bundled-php-predefined-a98d8de5180a-e5747d4f5a45-com.jetbrains.php.sharedIndexes-PS-251.26927.60" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8d3ef8bc-97f6-4c1b-907f-c3d76e26edff" name="Changes" comment="" />
      <created>1753465408136</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753465408136</updated>
      <workItem from="1753465409242" duration="3060000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>