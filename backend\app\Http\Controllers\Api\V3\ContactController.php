<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Subscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailManager;

class ContactController extends ApiResponse
{
    /**
     * Send contact message
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendContactMessage(Request $request)
    {
        $messages = [
            'name.required' => translate('Name is required'),
            'email.required' => translate('Email is required'),
            'email.email' => translate('Please provide a valid email address'),
            'subject.required' => translate('Subject is required'),
            'message.required' => translate('Message is required'),
        ];

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'recaptchaToken' => 'nullable|string',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid information', $validator->errors()->messages(), 400);
        }

        try {
            // Check if this is a newsletter subscription
            if ($request->subject === 'Newsletter Subscription') {
                // Check if email already exists in subscribers
                $existingSubscriber = Subscriber::where('email', $request->email)->first();
                
                if (!$existingSubscriber) {
                    $subscriber = new Subscriber;
                    $subscriber->email = $request->email;
                    $subscriber->save();
                }
                
                // Send welcome email to new subscriber
                if (env('MAIL_USERNAME') != null && !$existingSubscriber) {
                    $array['view'] = 'emails.newsletter';
                    $array['subject'] = translate('Thank you for subscribing to our newsletter');
                    $array['from'] = env('MAIL_FROM_ADDRESS');
                    $array['content'] = translate('Welcome to our newsletter! You will now receive updates on our latest products and promotions.');
                    
                    try {
                        Mail::to($request->email)->queue(new EmailManager($array));
                    } catch (\Exception $e) {
                        // Log error but continue
                        \Log::error('Failed to send newsletter welcome email: ' . $e->getMessage());
                    }
                }
                
                return $this->success(null, 'You have been subscribed to our newsletter successfully');
            }
            
            // Handle regular contact message
            if (env('MAIL_USERNAME') != null) {
                $array['view'] = 'emails.contact';
                $array['subject'] = $request->subject;
                $array['from'] = env('MAIL_FROM_ADDRESS');
                $array['content'] = 'Name: ' . $request->name . '<br>Email: ' . $request->email . '<br>Message: ' . $request->message;
                
                // Get admin email
                $adminEmail = get_setting('contact_email') ?? env('MAIL_FROM_ADDRESS');
                
                try {
                    Mail::to($adminEmail)->queue(new EmailManager($array));
                    
                    // Send acknowledgment to user
                    $array['subject'] = translate('Thank you for contacting us');
                    $array['content'] = translate('We have received your message and will get back to you shortly.');
                    Mail::to($request->email)->queue(new EmailManager($array));
                } catch (\Exception $e) {
                    return $this->error('MAIL_SEND_FAILED', 'Failed to send email', $e->getMessage(), 500);
                }
            }
            
            return $this->success(null, 'Your message has been sent successfully');
        } catch (\Throwable $th) {
            return $this->error('CONTACT_SUBMISSION_FAILED', 'Failed to send your message', $th->getMessage(), 400);
        }
    }
    
    /**
     * Subscribe to newsletter
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function subscribeToNewsletter(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide a valid email address', $validator->errors()->messages(), 400);
        }

        try {
            // Check if email already exists
            $existingSubscriber = Subscriber::where('email', $request->email)->first();
            
            if ($existingSubscriber) {
                return $this->success(null, 'You are already subscribed to our newsletter');
            }
            
            $subscriber = new Subscriber;
            $subscriber->email = $request->email;
            $subscriber->save();
            
            // Send welcome email to new subscriber
            if (env('MAIL_USERNAME') != null) {
                $array['view'] = 'emails.newsletter';
                $array['subject'] = translate('Thank you for subscribing to our newsletter');
                $array['from'] = env('MAIL_FROM_ADDRESS');
                $array['content'] = translate('Welcome to our newsletter! You will now receive updates on our latest products and promotions.');
                
                try {
                    Mail::to($request->email)->queue(new EmailManager($array));
                } catch (\Exception $e) {
                    // Log error but continue
                    \Log::error('Failed to send newsletter welcome email: ' . $e->getMessage());
                }
            }
            
            return $this->success(null, 'You have been subscribed to our newsletter successfully');
        } catch (\Throwable $th) {
            return $this->error('SUBSCRIPTION_FAILED', 'Failed to subscribe to newsletter', $th->getMessage(), 400);
        }
    }
} 