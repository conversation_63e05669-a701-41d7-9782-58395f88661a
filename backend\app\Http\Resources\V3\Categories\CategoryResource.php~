<?php

namespace App\Http\Resources\V3\Categories;

use App\Http\Resources\V2\Seller\ChildCategoriesCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'name' => $this->name,
            'slug' => $this->slug,
            'slug' => $this->slug,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'banner' => uploaded_asset($this->banner),
            'icon' => uploaded_asset($this->icon),
            'cover_image' => uploaded_asset($this->cover_image),
            'is_featured' => (boolean)$this->featured == 0 ,
            'product_count' => $this->getProductCount(),
            'subcategories' => SubCategoryResource::collection($this->childrenCategories),
        ];
    }
}
