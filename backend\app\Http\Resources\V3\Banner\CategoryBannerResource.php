<?php

namespace App\Http\Resources\V3\Banner;

use Illuminate\Http\Resources\Json\JsonResource;

class CategoryBannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'description' => $this->description,
            'bgColor' => $this->bg_color,
            'imageSrc' => $this->image_src,
            'imageAlt' => $this->image_alt,
            'linkUrl' => $this->link_url,
            'btnText' => $this->btn_text,
            'isActive' => (bool) $this->is_active,
            'displayOrder' => $this->display_order,
            'startDate' => isset($this->start_date) ? $this->start_date->toIso8601String() : null,
            'endDate' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'category' => $this->when($this->relationLoaded('category'), function() {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                ];
            }),
            'createdAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String(),
        ];
    }
} 