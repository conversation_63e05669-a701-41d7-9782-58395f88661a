<?php

namespace App\Http\Controllers\Api\V3\Customer;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Customer\CustomerOfferResource;
use App\Models\Offer;
use App\Models\OfferCategory;
use App\Models\OfferProduct;
use App\Models\UserOfferUsage;
use App\Services\OfferService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * API Controller for Customer Offers
 * Uses the offers table to provide customer offers functionality
 */
class ApiCustomerOfferController extends ApiResponse
{
    protected OfferService $offerService;

    public function __construct(OfferService $offerService)
    {
        parent::__construct();
        $this->offerService = $offerService;
    }

    /**
     * Get personalized offers for customers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPersonalizedOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'per_page' => 'integer|min:1|max:100',
                'page' => 'integer|min:1',
                'sort_by' => 'string|in:title,priority,end_date,created_at',
                'sort_direction' => 'string|in:asc,desc',
                'search' => 'string|max:255',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $perPage = $request->get('per_page', 12);
            $page = $request->get('page', 1);
            $search = $request->get('search');

            $filters = [
                'isPersonalized' => true,
                'isActive' => true,
                'perPage' => $perPage,
                'page' => $page,
                'sortBy' => $request->get('sort_by', 'priority'),
                'sortDirection' => $request->get('sort_direction', 'desc'),
                'userType' => 'customer'
            ];

            if ($search) {
                $filters['search'] = $search;
            }

            $offers = $this->offerService->getOffers($filters);

            return $this->success([
                'data' => CustomerOfferResource::collection($offers->items()),
                'pagination' => [
                    'current_page' => $offers->currentPage(),
                    'per_page' => $offers->perPage(),
                    'total' => $offers->total(),
                    'last_page' => $offers->lastPage(),
                    'from' => $offers->firstItem(),
                    'to' => $offers->lastItem(),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve personalized offers', $e->getMessage(), 500);
        }
    }

    /**
     * Get top priority offers for customers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTopPriorityOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $limit = $request->get('limit', 3);
            $offers = $this->offerService->getTopPriorityOffers($limit, 'customer');

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve top offers', $e->getMessage(), 500);
        }
    }

    /**
     * Get exclusive offers for customers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getExclusiveOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $limit = $request->get('limit', 10);
            $offers = $this->offerService->getExclusiveOffers($limit, 'customer');

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve exclusive offers', $e->getMessage(), 500);
        }
    }

    /**
     * Get offers by filter
     *
     * @param Request $request
     * @param string $filter
     * @return JsonResponse
     */
    public function getOffersByFilter(Request $request, string $filter): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $limit = $request->get('limit', 10);

            switch ($filter) {
                case 'personalized':
                    $offers = $this->offerService->getOffersByFilter('personalized', $limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));

                case 'seasonal':
                    $offers = $this->offerService->getOffersByFilter('seasonal', $limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));

                case 'expiring':
                    $offers = $this->offerService->getExpiringOffers(7, $limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));

                case 'exclusive':
                    $offers = $this->offerService->getExclusiveOffers($limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));

                case 'product_recommendation':
                    $offers = $this->offerService->getOffersByFilter('product_recommendation', $limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));

                default:
                    $offers = $this->offerService->getOffersByFilter('all', $limit, 'customer');
                    return $this->success(CustomerOfferResource::collection($offers));
            }
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve offers by filter', $e->getMessage(), 500);
        }
    }

    /**
     * Get expiring offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getExpiringOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'days' => 'integer|min:1|max:30',
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $days = $request->get('days', 7);
            $limit = $request->get('limit', 10);
            $offers = $this->offerService->getExpiringOffers($days, $limit, 'customer');

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve expiring offers', $e->getMessage(), 500);
        }
    }

    /**
     * Search offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:1|max:255',
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $query = $request->get('query');
            $limit = $request->get('limit', 10);
            $offers = $this->offerService->searchOffers($query, $limit, 'customer');

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to search offers', $e->getMessage(), 500);
        }
    }

    /**
     * Get offer statistics for customers
     *
     * @return JsonResponse
     */
    public function getOfferStats(): JsonResponse
    {
        try {
            $now = Carbon::now();
            
            $totalOffers = Offer::where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->count();
            
            $exclusiveCount = Offer::where('is_exclusive', true)
                ->where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->count();
                
            $expiringSoonCount = Offer::where('end_date', '<=', Carbon::now()->addDays(7))
                ->where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->count();

            // Get highest discount offer
            $highestDiscountOffer = Offer::where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->orderBy('discount_percentage', 'desc')
                ->first();

            // Get next expiring offer
            $nextExpiringOffer = Offer::where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->orderBy('end_date', 'asc')
                ->first();

            // Calculate total potential savings
            $totalPotentialSavings = Offer::where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->where('is_expired', false)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->sum('savings_amount') ?: 0;

            $response = [
                'totalOffers' => $totalOffers,
                'exclusiveCount' => $exclusiveCount,
                'expiringSoonCount' => $expiringSoonCount,
                'totalPotentialSavings' => (float) $totalPotentialSavings,
            ];

            if ($highestDiscountOffer) {
                $response['highestDiscountOffer'] = new CustomerOfferResource($highestDiscountOffer);
            }

            if ($nextExpiringOffer) {
                $response['nextExpiringOffer'] = new CustomerOfferResource($nextExpiringOffer);
            }

            return $this->success($response);
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve offer stats', $e->getMessage(), 500);
        }
    }

    /**
     * Get offer by ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getOfferById(string $id): JsonResponse
    {
        try {
            $offer = $this->offerService->getOfferById($id);

            if (!$offer) {
                return $this->error('NOT_FOUND', 'Offer not found', null, 404);
            }

            // Check if offer is applicable for customers
            if (!$offer->isApplicableForUserType('customer')) {
                return $this->error('FORBIDDEN', 'This offer is not available for customers', null, 403);
            }

            // Check if user has reached limit (if authenticated)
            $canApply = true;
            $reachedLimit = false;
            if (Auth::check()) {
                $reachedLimit = $offer->hasUserReachedLimit(Auth::id());
                $canApply = $offer->canBeAppliedByUser(Auth::id());
            }

            $response = new CustomerOfferResource($offer);
            $responseData = $response->toArray(request());
            $responseData['can_apply'] = $canApply;
            $responseData['reached_limit'] = $reachedLimit;

            return $this->success($responseData);
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve offer', $e->getMessage(), 500);
        }
    }

    /**
     * Apply an offer to user - creates coupon for user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyOffer(Request $request): JsonResponse
    {
        try {
            
            $validator = Validator::make($request->all(), [
                'offer_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', json_encode($validator->errors()), 400);
            }

            if (!Auth::check()) {
                return $this->error('UNAUTHORIZED', 'User must be logged in to apply offers', null, 401);
            }

            $offerId = $request->get('offer_id');
            $user = Auth::user();
            
            $result = $this->offerService->applyOfferToUser($offerId, $user);

            if (!$result['success']) {
                return $this->error('APPLY_FAILED', $result['message'], null, 400);
            }

            return $this->success([
                'message' => $result['message'],
                'coupon_code' => $result['coupon_code'],
                'offer' => new CustomerOfferResource($result['offer']),
                'user_offer_usage' => $result['user_offer_usage']
            ]);

        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to apply offer', $e->getMessage(), 500);
        }
    }

    /**
     * Use an applied offer - records actual usage
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function useOffer(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'coupon_code' => 'required|string',
                'order_total' => 'required|numeric|min:0',
                'order_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', json_encode($validator->errors()), 400);
            }

            if (!Auth::check()) {
                return $this->error('UNAUTHORIZED', 'User must be logged in to use offers', null, 401);
            }

            $couponCode = $request->get('coupon_code');
            $orderTotal = $request->get('order_total');
            $orderId = $request->get('order_id');
            $user = Auth::user();

            // Get user offer usage
            $userOfferUsage = UserOfferUsage::where('coupon_code', $couponCode)
                ->where('user_id', $user->id)
                ->first();

            if (!$userOfferUsage) {
                return $this->error('INVALID_COUPON', 'Invalid coupon code', null, 400);
            }

            $offer = $userOfferUsage->offer;

            // Check minimum order value
            if ($offer->min_order_value && $orderTotal < $offer->min_order_value) {
                return $this->error('MIN_ORDER_NOT_MET', 'Minimum order value not met', json_encode([
                    'required' => $offer->min_order_value,
                    'current' => $orderTotal
                ]), 400);
            }

            // Calculate discount
            $discountAmount = 0;
            if ($offer->discount_type === 'percentage') {
                $discountAmount = ($orderTotal * $offer->discount_percentage) / 100;
                if ($offer->discount_amount) {
                    $discountAmount = min($discountAmount, $offer->discount_amount);
                }
            } else {
                $discountAmount = $offer->discount_amount;
            }

            $result = $this->offerService->useOfferByUser($couponCode, $user, $discountAmount, $orderId);

            if (!$result['success']) {
                return $this->error('USE_FAILED', $result['message'], null, 400);
            }

            $finalTotal = $orderTotal - $discountAmount;

            return $this->success([
                'message' => $result['message'],
                'discount_amount' => $discountAmount,
                'original_total' => $orderTotal,
                'final_total' => max(0, $finalTotal),
                'is_limit_reached' => $result['is_limit_reached'],
                'user_offer_usage' => $result['user_offer_usage'],
            ]);

        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to use offer', $e->getMessage(), 500);
        }
    }

    /**
     * Get user's applied offers (coupon codes)
     *
     * @return JsonResponse
     */
    public function getUserAppliedOffers(): JsonResponse
    {
        try {
            if (!Auth::check()) {
                return $this->error('UNAUTHORIZED', 'User must be logged in', null, 401);
            }

            $userOfferCoupons = $this->offerService->getUserOfferCoupons(Auth::id());

            return $this->success($userOfferCoupons->map(function ($userOfferUsage) {
                return [
                    'coupon_code' => $userOfferUsage->coupon_code,
                    'usage_count' => $userOfferUsage->usage_count,
                    'usage_limit' => $userOfferUsage->usage_limit,
                    'can_use' => $userOfferUsage->isValid(),
                    'expires_at' => $userOfferUsage->expires_at,
                    'offer' => new CustomerOfferResource($userOfferUsage->offer)
                ];
            }));

        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve user offers', $e->getMessage(), 500);
        }
    }

    /**
     * Get seasonal offers (active campaigns)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSeasonalActive(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $limit = $request->get('limit', 10);
            $now = Carbon::now();
            
            $offers = Offer::where('is_seasonal', true)
                ->where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->where('status', 'active')
                ->where(function ($q) {
                    $q->where('user_type', 'customer')
                      ->orWhere('user_type', 'all');
                })
                ->orderBy('priority', 'desc')
                ->limit($limit)
                ->get();

            return $this->success(CustomerOfferResource::collection($offers));
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve seasonal active offers', $e->getMessage(), 500);
        }
    }

    public function getSeasonalOffers(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'per_page' => 'integer|min:1|max:100',
                'page' => 'integer|min:1',
                'active_only' => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Invalid request parameters', $validator->errors(), 400);
            }

            $perPage = $request->get('per_page', 12);
            $page = $request->get('page', 1);
            $activeOnly = $request->get('active_only', true);

            $filters = [
                'isSeasonal' => true,
                'perPage' => $perPage,
                'page' => $page,
                'sortBy' => 'priority',
                'sortDirection' => 'desc',
                'userType' => 'customer'
            ];

            if ($activeOnly) {
                $filters['isActive'] = true;
            }

            $result = $this->offerService->getOffers($filters);

            return $this->success([
                'data' => CustomerOfferResource::collection($result->items()),
                'pagination' => [
                    'current_page' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                    'from' => $result->firstItem(),
                    'to' => $result->lastItem(),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->error('SERVER_ERROR', 'Failed to retrieve seasonal offers', $e->getMessage(), 500);
        }
    }
} 