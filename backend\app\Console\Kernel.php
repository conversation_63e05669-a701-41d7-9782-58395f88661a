<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\PendingOrderReminderCommand::class,
        Commands\CanceledOrderCommand::class,
        Commands\OrderDeliveryDelay::class,
        Commands\PendingOrderCommand::class,
        Commands\OrderReviewEmailCommand::class,
        Commands\FixCartTables::class,
        Commands\CheckPriceAlerts::class,
        Commands\CleanupEmailVerificationRecords::class,
        Commands\OptimizeEmailVerificationPerformance::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('PendingOrderReminderCommand')->everyMinute()->timezone('America/Chicago')->runInBackground();
        $schedule->command('CanceledOrderCommand')->everyMinute()->timezone('America/Chicago')->runInBackground();
        $schedule->command('OrderDeliveryDelay')->everyMinute()->timezone('America/Chicago')->runInBackground();
        $schedule->command('PendingOrderCommand')->everyMinute()->timezone('America/Chicago')->runInBackground();
        $schedule->command('price-alerts:check')->daily();
        
        // Email verification cleanup - run every hour to clean expired OTPs
        $schedule->command('email-verification:cleanup')->hourly()->runInBackground();
        
        // Email verification integrity check - run daily at 2 AM
        $schedule->command('email-verification:cleanup --integrity')->dailyAt('02:00')->runInBackground();
        
        // Email verification performance optimization - run every 6 hours
        $schedule->command('email-verification:optimize --cleanup --warm-cache')->everySixHours()->runInBackground();
        
        //$schedule->command('OrderReviewEmailCommand')->everyMinute()->timezone('America/Chicago')->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
