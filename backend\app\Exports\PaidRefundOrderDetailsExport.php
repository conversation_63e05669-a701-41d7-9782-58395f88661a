<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PaidRefundOrderDetailsExport implements FromQuery, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function query()
    {
        return DB::table('refund_requests as r')
            ->join('orders as o', 'r.order_id', '=', 'o.id')
            ->join('users as u', 'o.user_id', '=', 'u.id')
            ->join('order_details as od', 'r.order_detail_id', '=', 'od.id')
            ->join('products as p', 'od.product_id', '=', 'p.id')
            ->selectRaw('
        r.created_at AS `Refund Date`,
        o.code AS `Order Code`,
        u.name AS `Seller Name`,
        p.name AS `Product Name`,
        od.price AS `Price`
    ')
            ->where('r.refund_status', 1)
            ->groupBy('r.id', 'o.code', 'u.name', 'p.name', 'od.price') // Ensure all selected columns are included in GROUP BY
            ->orderBy('r.created_at', 'desc');
    }
    public function headings(): array
    {
        return [
            'Refund Date',
            'Order Code',
            'Seller Name',
            'Product Name',
            'Price',
        ];
    }
}
