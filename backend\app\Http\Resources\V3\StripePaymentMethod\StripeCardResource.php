<?php

namespace App\Http\Resources\V3\StripePaymentMethod;

use Illuminate\Http\Resources\Json\JsonResource;

class StripeCardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $billingDetails = $this->billing_details ?? [];
        
        return [
            'id' => $this->stripe_card_id,
            'type' => $this->type,
            'is_default' => (boolean) $this->isDefault,
            'details' => [
                'brand' => $this->brand,
                'last4' => $this->last4,
                'expiry_month' => str_pad($this->exp_month, 2, '0', STR_PAD_LEFT),
                'expiry_year' => $this->exp_year,
                'cardholder_name' => $billingDetails['name'] ?? '',
            ],
            'billing_details' => [
                'name' => $billingDetails['name'] ?? '',
                'email' => $billingDetails['email'] ?? '',
                'phone' => $billingDetails['phone'] ?? '',
                'address' => [
                    'line1' => $billingDetails['address']['line1'] ?? '',
                    'line2' => $billingDetails['address']['line2'] ?? '',
                    'city' => $billingDetails['address']['city'] ?? '',
                    'state' => $billingDetails['address']['state'] ?? '',
                    'postal_code' => $billingDetails['address']['postal_code'] ?? '',
                    'country' => $billingDetails['address']['country'] ?? '',
                ]
            ],
            'created_at' => $this->created_at != null ? $this->created_at->toIso8601String() : null,
            'updated_at' => $this->updated_at != null ? $this->updated_at->toIso8601String() : null,
        ];
    }
}
