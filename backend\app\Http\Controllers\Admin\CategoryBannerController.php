<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Promotions\CategoryBanner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CategoryBannerController extends Controller
{
    /**
     * Display a listing of the category banners.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = CategoryBanner::with('category');
        
        // Apply filters
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->input('is_active'));
        }
        
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }
        
        // Apply search
        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('subtitle', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        // Apply sorting
        $sortField = $request->input('sort_field', 'display_order');
        $sortDirection = $request->input('sort_direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
        
        $banners = $query->paginate(15);
        $categories = Category::all();
        
        return view('admin.category_banners.index', compact('banners', 'categories'));
    }

    /**
     * Show the form for creating a new category banner.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $categories = Category::all();
        return view('admin.category_banners.create', compact('categories'));
    }

    /**
     * Store a newly created category banner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'bg_color' => 'nullable|string|max:50',
            'image_src' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'image_alt' => 'nullable|string|max:255',
            'link_url' => 'required|string|max:255',
            'btn_text' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle image upload
        $imagePath = null;
        if ($request->hasFile('image_src')) {
            $image = $request->file('image_src');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $imagePath = $image->storeAs('category_banners', $imageName, 'public');
        }

        // Create banner
        CategoryBanner::create([
            'title' => $request->input('title'),
            'subtitle' => $request->input('subtitle'),
            'description' => $request->input('description'),
            'bg_color' => $request->input('bg_color', 'bg-gray-100'),
            'image_src' => asset('storage/' . $imagePath),
            'image_alt' => $request->input('image_alt', $request->input('title')),
            'link_url' => $request->input('link_url'),
            'btn_text' => $request->input('btn_text', 'View More'),
            'is_active' => $request->has('is_active'),
            'display_order' => $request->input('display_order', 0),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'category_id' => $request->input('category_id'),
        ]);

        return redirect()->route('admin.category-banners.index')
            ->with('success', 'Category banner created successfully.');
    }

    /**
     * Show the form for editing the specified category banner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $banner = CategoryBanner::findOrFail($id);
        $categories = Category::all();
        
        return view('admin.category_banners.edit', compact('banner', 'categories'));
    }

    /**
     * Update the specified category banner in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $banner = CategoryBanner::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'bg_color' => 'nullable|string|max:50',
            'image_src' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'image_alt' => 'nullable|string|max:255',
            'link_url' => 'required|string|max:255',
            'btn_text' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Handle image upload if new image is provided
        if ($request->hasFile('image_src')) {
            // Delete old image if it exists
            $oldImagePath = str_replace(asset('storage/'), '', $banner->image_src);
            if (Storage::disk('public')->exists($oldImagePath)) {
                Storage::disk('public')->delete($oldImagePath);
            }
            
            // Store new image
            $image = $request->file('image_src');
            $imageName = time() . '_' . $image->getClientOriginalName();
            $imagePath = $image->storeAs('category_banners', $imageName, 'public');
            $banner->image_src = asset('storage/' . $imagePath);
        }

        // Update banner fields
        $banner->title = $request->input('title');
        $banner->subtitle = $request->input('subtitle');
        $banner->description = $request->input('description');
        $banner->bg_color = $request->input('bg_color');
        $banner->image_alt = $request->input('image_alt');
        $banner->link_url = $request->input('link_url');
        $banner->btn_text = $request->input('btn_text');
        $banner->is_active = $request->has('is_active');
        $banner->display_order = $request->input('display_order');
        $banner->start_date = $request->input('start_date');
        $banner->end_date = $request->input('end_date');
        $banner->category_id = $request->input('category_id');
        $banner->save();

        return redirect()->route('admin.category-banners.index')
            ->with('success', 'Category banner updated successfully.');
    }

    /**
     * Remove the specified category banner from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $banner = CategoryBanner::findOrFail($id);
        
        // Delete the image file if it exists
        $imagePath = str_replace(asset('storage/'), '', $banner->image_src);
        if (Storage::disk('public')->exists($imagePath)) {
            Storage::disk('public')->delete($imagePath);
        }
        
        $banner->delete();
        
        return redirect()->route('admin.category-banners.index')
            ->with('success', 'Category banner deleted successfully.');
    }

    /**
     * Update the order of multiple banners.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'banners' => 'required|array',
            'banners.*.id' => 'required|exists:category_banners,id',
            'banners.*.display_order' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        foreach ($request->input('banners') as $bannerData) {
            CategoryBanner::where('id', $bannerData['id'])
                ->update(['display_order' => $bannerData['display_order']]);
        }

        return response()->json(['message' => 'Banner order updated successfully']);
    }

    /**
     * Toggle the active status of a category banner.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function toggleStatus($id)
    {
        $banner = CategoryBanner::findOrFail($id);
        $banner->is_active = !$banner->is_active;
        $banner->save();
        
        return redirect()->route('admin.category-banners.index')
            ->with('success', 'Banner status updated successfully.');
    }
} 