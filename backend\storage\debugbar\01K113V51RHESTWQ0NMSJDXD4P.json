{"__meta": {"id": "01K113V51RHESTWQ0NMSJDXD4P", "datetime": "2025-07-25 08:39:48", "utime": **********.664977, "method": "GET", "uri": "/buzfi-new-backend/api/v3/coupons", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": 1753457987.931889, "end": **********.664987, "duration": 0.733098030090332, "duration_str": "733ms", "measures": [{"label": "Booting", "start": 1753457987.931889, "relative_start": 0, "end": **********.096057, "relative_end": **********.096057, "duration": 0.****************, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.096065, "relative_start": 0.*****************, "end": **********.664988, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "569ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.105567, "relative_start": 0.*****************, "end": **********.11075, "relative_end": **********.11075, "duration": 0.005182981491088867, "duration_str": "5.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.662582, "relative_start": 0.****************, "end": **********.662817, "relative_end": **********.662817, "duration": 0.00023508071899414062, "duration_str": "235μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.663853, "relative_start": 0.****************, "end": **********.663888, "relative_end": **********.663888, "duration": 3.504753112792969e-05, "duration_str": "35μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.028980000000000002, "accumulated_duration_str": "28.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select count(*) as aggregate from `coupons`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ApiCouponController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\ApiCouponController.php", "line": 73}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.463468, "duration": 0.02109, "duration_str": "21.09ms", "memory": 0, "memory_str": null, "filename": "ApiCouponService.php:65", "source": {"index": 16, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FApiCouponService.php&line=65", "ajax": false, "filename": "ApiCouponService.php", "line": "65"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 72.774}, {"sql": "select * from `coupons` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ApiCouponController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\ApiCouponController.php", "line": 73}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.485691, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ApiCouponService.php:65", "source": {"index": 16, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FApiCouponService.php&line=65", "ajax": false, "filename": "ApiCouponService.php", "line": "65"}, "connection": "buzfi", "explain": null, "start_percent": 72.774, "width_percent": 1.725}, {"sql": "select * from `coupon_usages` where `coupon_usages`.`coupon_id` in (2, 3, 4, 5, 6, 7, 8, 9, 10, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/ApiCouponController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\ApiCouponController.php", "line": 73}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.503385, "duration": 0.00739, "duration_str": "7.39ms", "memory": 0, "memory_str": null, "filename": "ApiCouponService.php:65", "source": {"index": 21, "namespace": null, "name": "app/Services/ApiCouponService.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Services\\ApiCouponService.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FServices%2FApiCouponService.php&line=65", "ajax": false, "filename": "ApiCouponService.php", "line": "65"}, "connection": "buzfi", "explain": null, "start_percent": 74.5, "width_percent": 25.5}]}, "models": {"data": {"App\\Models\\Coupon": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCoupon.php&line=1", "ajax": false, "filename": "Coupon.php", "line": "?"}}, "App\\Models\\CouponUsage": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCouponUsage.php&line=1", "ajax": false, "filename": "CouponUsage.php", "line": "?"}}}, "count": 16, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 16}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/coupons", "action_name": null, "controller_action": "App\\Http\\Controllers\\ApiCouponController@index", "uri": "GET api/v3/coupons", "controller": "App\\Http\\Controllers\\ApiCouponController@index<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApiCouponController.php&line=39\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/coupons", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApiCouponController.php&line=39\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ApiCouponController.php:39-95</a>", "middleware": "api, app_language, app_language", "duration": "733ms", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-72663634 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-72663634\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1111567494 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1111567494\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1793506257 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=300</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"412 characters\">XSRF-TOKEN=eyJpdiI6Ii8xVGE3aTNKVEFqaE9wWFF3RklIYVE9PSIsInZhbHVlIjoic0ZHVGRIajVISGRHaDY3TFgxTFUydWZ1eFhsL3NEWk82Nmgwd3p6QmdLUHZxNm1OUFFDalJCWlJPZTkyVWpvRUkwN1BNTjkvRi8yYjZ3OXgxOEgzemlXdDRaTEwvUlZGNW5NMVJvM0FKZkRhTEIvdEZwUnIvK2xQdSs4NGZwU1kiLCJtYWMiOiJjNjY3MjhlODQ4MWUzNWFjNDExN2EyMjFlYjI5ZjFhZTdlMzAzYTg0MjAzMzZhMjA0M2VkNmY2ZjczODJmNDY0IiwidGFnIjoiIn0%3D; buzficom_session=JBkDQetPU77FEmv10Lai9Mvhi6r9aDCsCuD1ENS8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793506257\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-536873792 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-536873792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1205554381 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:39:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">597</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikx0M012Q2V4YS9qT0VmV09SRTFiTFE9PSIsInZhbHVlIjoiL3hleEd4MlY4elhDbHJ6QjdBVTlSTWxYZUpHamFJd2JTVjF3TXplZ0FuU2RtN2ZxK0xYTmRxUFRLV1ZlR21GbmxweXBYZDZhUlUwZFhTM0RwOFNiZWtTT1ZsSW53QlpBaXMwclNHNm9STlliTm5QZnRUU2RYMjl1UmZhTHh1cmciLCJtYWMiOiIzOGMwZjFiZTgzMDlhNzgyNDcyMzFhM2Q2YjM5NDg3YjMyNjQ2MmQ1MzVmZThlNjNkMGE0NGI0NDlmZGQzY2E3IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:48 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IkIzMFd1TWtBZmJHa2JUdDlJMFdTQ1E9PSIsInZhbHVlIjoiM2IwSGJJbHNnVnJER1JrcndjdVQ5eklEdmcrT01MVDJlODZaWittMXZDTEkxbTU4ZVNvVnZ3ai96eGJNY1RGQ3JGb2FsenFjZmdjTUNDd3BrN2dnYkd3RzdmdngzRjVNNjdoOWx2SkRKTE9Rb2E3RVNRaVRzT2NNL3gyWEdteVkiLCJtYWMiOiIzNGU1MGNiNjc2YTlmNGQ0ZWJjNTljYTJjZTMyOGYzNDBkYjgzZjMxYjAxNGJmZTEyNTY1NWQxNThmZjRlMjMxIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:48 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikx0M012Q2V4YS9qT0VmV09SRTFiTFE9PSIsInZhbHVlIjoiL3hleEd4MlY4elhDbHJ6QjdBVTlSTWxYZUpHamFJd2JTVjF3TXplZ0FuU2RtN2ZxK0xYTmRxUFRLV1ZlR21GbmxweXBYZDZhUlUwZFhTM0RwOFNiZWtTT1ZsSW53QlpBaXMwclNHNm9STlliTm5QZnRUU2RYMjl1UmZhTHh1cmciLCJtYWMiOiIzOGMwZjFiZTgzMDlhNzgyNDcyMzFhM2Q2YjM5NDg3YjMyNjQ2MmQ1MzVmZThlNjNkMGE0NGI0NDlmZGQzY2E3IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IkIzMFd1TWtBZmJHa2JUdDlJMFdTQ1E9PSIsInZhbHVlIjoiM2IwSGJJbHNnVnJER1JrcndjdVQ5eklEdmcrT01MVDJlODZaWittMXZDTEkxbTU4ZVNvVnZ3ai96eGJNY1RGQ3JGb2FsenFjZmdjTUNDd3BrN2dnYkd3RzdmdngzRjVNNjdoOWx2SkRKTE9Rb2E3RVNRaVRzT2NNL3gyWEdteVkiLCJtYWMiOiIzNGU1MGNiNjc2YTlmNGQ0ZWJjNTljYTJjZTMyOGYzNDBkYjgzZjMxYjAxNGJmZTEyNTY1NWQxNThmZjRlMjMxIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1205554381\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-103602557 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://localhost/buzfi-new-backend/api/v3/coupons</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-103602557\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/coupons", "controller_action": "App\\Http\\Controllers\\ApiCouponController@index"}, "badge": null}}