<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\LiveChat;
use App\Models\LiveChatMessage;
use App\Models\LiveChatMessageAttachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Pusher\Pusher;

class ApiLiveChatController extends ApiResponse
{
    protected $allowedFileTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/webm', 'video/quicktime',
        'audio/mpeg', 'audio/wav', 'audio/ogg',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    protected $maxFileSize = 10 * 1024 * 1024; // 10MB

    public function getMessages(Request $request)
    {
        $chat = LiveChat::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();

        if (!$chat) {
            return $this->error('404', 'No active chat session found');
        }

        $messages = $chat->messages()
            ->with(['user', 'attachments'])
            ->orderBy('created_at', 'asc')
            ->get();

        $formattedMessages = $messages->map(function ($message) {
            return $message->toApiArray();
        });

        return $this->success($formattedMessages);
    }

    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required_without:attachment|string|max:1000',
            'attachment' => 'nullable|file|max:' . ($this->maxFileSize / 1024)
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid message or attachment', $validator->errors()->messages(), 400);
        }

        $chat = LiveChat::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();

        if (!$chat) {
            return $this->error('404', 'No active chat session found');
        }

        // Create the message
        $message = $chat->messages()->create([
            'user_id' => auth()->id(),
            'message' => $request->message ?? '',
            'type' => 'user'
        ]);

        // Handle file attachment if present
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            
            // Validate file type
            if (!in_array($file->getMimeType(), $this->allowedFileTypes)) {
                $message->delete();
                return $this->validation_error('Invalid File', 'File type not allowed', null, 400);
            }

            // Validate file size
            if ($file->getSize() > $this->maxFileSize) {
                $message->delete();
                return $this->validation_error('File Too Large', 'File size exceeds 10MB limit', null, 400);
            }

            // Store the file
            $fileName = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('live_chat_attachments', $fileName, 'public');

            // Create attachment record
            $message->attachments()->create([
                'name' => $file->getClientOriginalName(),
                'url' => Storage::url($filePath),
                'type' => $file->getMimeType(),
                'size' => $file->getSize()
            ]);
        }

        $chat->updateLastMessageTime();

        // Load relationships for response
        $message->load(['user', 'attachments']);

        // Broadcast message via Pusher
        $this->broadcastMessage($chat, $message);

        return $this->success($message->toApiArray());
    }

    public function getStatus()
    {
        $chat = LiveChat::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();

        return $this->success([
            'is_active' => (bool)$chat,
            'chat_id' => $chat ? $chat->id : null,
            'last_message_at' => $chat ? $chat->last_message_at : null
        ]);
    }

    public function startChat()
    {
        $existingChat = LiveChat::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();

        if ($existingChat) {
            return $this->success($existingChat, 'Chat session already exists');
        }

        $chat = LiveChat::create([
            'user_id' => auth()->id(),
            'status' => 'active'
        ]);

        $chat->markAsActive();

        return $this->success($chat, 'Chat session started successfully');
    }

    public function endChat()
    {
        $chat = LiveChat::where('user_id', auth()->id())
            ->where('status', 'active')
            ->first();

        if (!$chat) {
            return $this->error('404', 'No active chat session found');
        }

        $chat->markAsEnded();

        return $this->success(null, 'Chat session ended successfully');
    }

    public function markMessageAsRead($messageId)
    {
        $message = LiveChatMessage::whereHas('chat', function ($query) {
            $query->where('user_id', auth()->id());
        })->findOrFail($messageId);

        $message->markAsRead();

        return $this->success(null, 'Message marked as read');
    }

    /**
     * Broadcast message via Pusher
     */
    private function broadcastMessage(LiveChat $chat, LiveChatMessage $message)
    {
        try {
            $pusher = new Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id'),
                [
                    'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                    'useTLS' => true
                ]
            );

            $pusher->trigger(
                "private-live-chat.{$chat->id}",
                'LiveChatMessageSent',
                [
                    'message' => $message->toApiArray()
                ]
            );
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::error('Failed to broadcast message: ' . $e->getMessage());
        }
    }

    /**
     * Download attachment
     */
    public function downloadAttachment($attachmentId)
    {
        $attachment = LiveChatMessageAttachment::whereHas('message.chat', function ($query) {
            $query->where('user_id', auth()->id());
        })->findOrFail($attachmentId);

        $filePath = str_replace('/storage/', '', $attachment->url);
        
        if (!Storage::disk('public')->exists($filePath)) {
            return $this->error('404', 'File not found');
        }

        return Storage::disk('public')->download($filePath, $attachment->name);
    }
} 