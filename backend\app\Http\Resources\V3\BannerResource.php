<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;

class BannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($item) {
            $wizardInfo = $item->wizard_info;
            return [
                "id" => $wizardInfo->slug ?? null,
                "title" => $item->wizard_detail_title ?? null,
                "subtitle" => $item->wizard_detail_sub_title ?? null,
                "buttonText" => $item->wizard_detail_button_text ?? null,
                "buttonLink" => $item->wizard_detail_button_link ?? null,
                "image" => $item->wizard_detail_images?uploaded_asset($item->wizard_detail_images) : null,
                "backgroundColor" => $item->wizard_detail_background_color ?? null,
                "mobileImage" => $item->wizard_detail_mobile_images?uploaded_asset($item->wizard_detail_mobile_images) : null,
                "startDate" => $wizardInfo->start_date ? date('Y-m-d\TH:i:s\Z', strtotime($wizardInfo->start_date)) : null,
                "endDate" => $wizardInfo->end_date ? date('Y-m-d\TH:i:s\Z', strtotime($wizardInfo->end_date)) : null,
                "position" => $item->wizard_detail_position ?? 0
            ];
        });
    }
}
