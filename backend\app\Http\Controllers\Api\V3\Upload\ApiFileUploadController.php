<?php

namespace App\Http\Controllers\Api\V3\Upload;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ApiFileUploadController extends Controller
{
    /**
     * Maximum file size by type (in KB)
     */
    protected $maxSizes = [
        'image' => 5120, // 5MB
        'document' => 10240, // 10MB
        'audio' => 20480, // 20MB
        'video' => 51200, // 50MB
    ];

    /**
     * Allowed file types by category
     */
    protected $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv', 'ppt', 'pptx'],
        'audio' => ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
        'video' => ['mp4', 'mov', 'avi', 'webm', 'mkv'],
    ];

    /**
     * Upload an image file
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        return $this->handleFileUpload($request, 'image');
    }

    /**
     * Upload a document file
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadDocument(Request $request)
    {
        return $this->handleFileUpload($request, 'document');
    }

    /**
     * Upload an audio file
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAudio(Request $request)
    {
        return $this->handleFileUpload($request, 'audio');
    }

    /**
     * Upload a video file
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadVideo(Request $request)
    {
        return $this->handleFileUpload($request, 'video');
    }

    /**
     * Delete an uploaded file
     *
     * @param Request $request
     * @param string $fileId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteUploadedFile(Request $request, $fileId)
    {
        $user = Auth::user();
        
        // Find the file in the database
        $file = \App\Models\UploadedFile::where('id', $fileId)
            ->where('user_id', $user->id)
            ->first();
        
        if (!$file) {
            return response()->json([
                'status' => false,
                'message' => 'File not found or you do not have permission to delete it',
            ], 404);
        }
        
        // Delete the file from storage
        Storage::disk('public')->delete($file->path);
        
        // Delete the file record
        $file->delete();
        
        return response()->json([
            'status' => true,
            'message' => 'File deleted successfully',
        ]);
    }

    /**
     * Handle file upload for different file types
     *
     * @param Request $request
     * @param string $type File type (image, document, audio, video)
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleFileUpload(Request $request, $type)
    {
        $request->validate([
            'file' => 'required|file|max:' . $this->maxSizes[$type],
            'folder' => 'nullable|string',
        ]);
        
        $file = $request->file('file');
        $extension = strtolower($file->getClientOriginalExtension());
        
        // Check if the file extension is allowed
        if (!in_array($extension, $this->allowedTypes[$type])) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid file type. Allowed types: ' . implode(', ', $this->allowedTypes[$type]),
            ], 400);
        }
        
        $user = Auth::user();
        $folder = $request->folder ?? $type;
        
        // Generate a unique filename
        $fileName = Str::uuid() . '.' . $extension;
        
        // Path inside the storage/app/public folder
        $path = 'uploads/' . $folder . '/' . date('Y/m/d') . '/' . $fileName;
        
        // Store the file
        $file->storeAs('public/' . dirname($path), $fileName);
        
        // Record in database
        $uploadedFile = new \App\Models\UploadedFile();
        $uploadedFile->user_id = $user->id;
        $uploadedFile->name = $file->getClientOriginalName();
        $uploadedFile->path = $path;
        $uploadedFile->type = $type;
        $uploadedFile->size = $file->getSize();
        $uploadedFile->mime_type = $file->getMimeType();
        $uploadedFile->extension = $extension;
        $uploadedFile->save();
        
        return response()->json([
            'status' => true,
            'message' => 'File uploaded successfully',
            'data' => [
                'id' => $uploadedFile->id,
                'name' => $uploadedFile->name,
                'url' => asset('storage/' . $path),
                'size' => $uploadedFile->size,
                'type' => $uploadedFile->type,
                'mime_type' => $uploadedFile->mime_type,
                'extension' => $uploadedFile->extension,
                'created_at' => $uploadedFile->created_at->toIso8601String(),
            ]
        ]);
    }
} 