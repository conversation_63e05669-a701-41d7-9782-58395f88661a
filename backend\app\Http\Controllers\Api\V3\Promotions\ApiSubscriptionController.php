<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Subscription\SubscriptionResource;
use App\Models\Promotions\Subscription;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ApiSubscriptionController extends ApiResponse
{
    /**
     * Subscribe a user to a newsletter
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function subscribe(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:255',
                'name' => 'nullable|string|max:255',
                'subscription_type' => 'required|string|in:newsletter,product_updates,promotions,all',
                'preferences' => 'nullable|array',
                'source' => 'nullable|string|max:100',
                'frequency' => 'nullable|string|in:daily,weekly,monthly'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $email = $request->input('email');
            $subscriptionType = $request->input('subscription_type');
            
            // Check if already subscribed
            $existingSubscription = Subscription::where('email', $email)
                ->where('subscription_type', $subscriptionType)
                ->where('status', 'active')
                ->first();
                
            if ($existingSubscription) {
                return $this->success([
                    'message' => 'Already subscribed to this type of subscription',
                    'subscription' => new SubscriptionResource($existingSubscription)
                ]);
            }
            
            // Get user if authenticated
            $userId = Auth::id();
            
            // Create new subscription
            $subscription = new Subscription();
            $subscription->email = $email;
            $subscription->name = $request->input('name');
            $subscription->subscription_type = $subscriptionType;
            $subscription->preferences = $request->input('preferences');
            $subscription->source = $request->input('source', 'website');
            $subscription->frequency = $request->input('frequency', 'weekly');
            $subscription->user_id = $userId;
            $subscription->status = 'pending';
            $subscription->subscription_date = now();
            $subscription->is_confirmed = false;
            $subscription->confirmation_token = Str::random(40);
            $subscription->ip_address = $request->ip();
            $subscription->user_agent = $request->userAgent();
            $subscription->save();
            
            // TODO: Send confirmation email (implement in a real application)
            
            return $this->success([
                'message' => 'Subscription created successfully! Please check your email to confirm.',
                'subscription' => new SubscriptionResource($subscription)
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Confirm a subscription
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirm(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string|max:100'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $token = $request->input('token');
            
            $subscription = Subscription::where('confirmation_token', $token)
                ->where('status', 'pending')
                ->where('is_confirmed', false)
                ->first();
                
            if (!$subscription) {
                return $this->error('Invalid token', 'The confirmation token is invalid or expired', 400);
            }
            
            $subscription->confirm();
            
            return $this->success([
                'message' => 'Subscription confirmed successfully!',
                'subscription' => new SubscriptionResource($subscription)
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Unsubscribe from a newsletter
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function unsubscribe(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|max:255',
                'token' => 'nullable|string',
                'subscription_type' => 'nullable|string|in:newsletter,product_updates,promotions,all',
                'reason' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $email = $request->input('email');
            $token = $request->input('token');
            $subscriptionType = $request->input('subscription_type');
            $reason = $request->input('reason');
            
            $query = Subscription::where('email', $email)
                ->where('status', 'active');
                
            if ($token) {
                $query->where('confirmation_token', $token);
            }
            
            if ($subscriptionType) {
                $query->where('subscription_type', $subscriptionType);
            }
            
            $subscriptions = $query->get();
            
            if ($subscriptions->isEmpty()) {
                return $this->error('Not found', 'No active subscriptions found for this email', 404);
            }
            
            foreach ($subscriptions as $subscription) {
                $subscription->unsubscribe($reason);
            }
            
            return $this->success([
                'message' => 'Successfully unsubscribed',
                'unsubscribed_count' => $subscriptions->count()
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get user subscriptions
     *
     * @return JsonResponse
     */
    public function getUserSubscriptions(): JsonResponse
    {
        try {
            $userId = Auth::id();
            
            $subscriptions = Subscription::where('user_id', $userId)->get();
            
            return $this->success(SubscriptionResource::collection($subscriptions));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Update subscription preferences
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updatePreferences(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'preferences' => 'required|array',
                'frequency' => 'nullable|string|in:daily,weekly,monthly'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $subscription = Subscription::find($id);
            
            if (!$subscription) {
                return $this->error('Not found', 'Subscription not found', 404);
            }
            
            // Check if user owns this subscription
            $userId = Auth::id();
            if ($subscription->user_id !== $userId) {
                return $this->error('Forbidden', 'You do not have permission to update this subscription', 403);
            }
            
            $subscription->preferences = $request->input('preferences');
            
            if ($request->has('frequency')) {
                $subscription->frequency = $request->input('frequency');
            }
            
            $subscription->save();
            
            return $this->success([
                'message' => 'Subscription preferences updated successfully',
                'subscription' => new SubscriptionResource($subscription)
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 