<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\Category;
use App\Models\Shop;
use App\Models\SellerCommission;
use App\Models\Customer;
use Carbon\Carbon;

class ApiSellerController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get seller profile
     */
    public function getProfile(Request $request)
    {
        try {
            $user = auth()->user();
            $shop = Shop::where('user_id', $user->id)->first();
            
            if (!$shop) {
                return $this->error(
                    'NOT_FOUND',
                    'Seller profile not found',
                    404
                );
            }
            
            $totalSales = Order::where('seller_id', $user->id)
                ->where('payment_status', 'paid')
                ->sum('grand_total');
                
            $totalOrders = Order::where('seller_id', $user->id)->count();
            
            $rating = $shop->rating ?? 0;

            $sellerProfile = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'company' => $shop->name,
                'status' => $user->banned ? 'inactive' : 'active',
                'joinedDate' => $user->created_at->toIso8601String(),
                'rating' => $rating,
                'avatar' => $user->avatar ?? null
            ];
            
            return $this->success($sellerProfile);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller profile',
                500
            );
        }
    }

    /**
     * Get seller orders
     */
    public function getOrders(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $page = $request->input('page', 1);
            $limit = min($request->input('limit', 50), 100);
            $status = $request->input('status');
            
            $ordersQuery = Order::where('seller_id', auth()->id())
                ->with(['orderDetails.product']);
                
            if ($status && $status !== 'all') {
                $ordersQuery->where('delivery_status', $status);
            }
            
            $total = $ordersQuery->count();
            $orders = $ordersQuery->skip(($page - 1) * $limit)
                ->take($limit)
                ->orderBy('created_at', 'desc')
                ->get();
                
            $formattedOrders = $orders->map(function($order) {
                $items = $order->orderDetails->map(function($detail) {
                    return [
                        'id' => $detail->id,
                        'productId' => $detail->product_id,
                        'name' => $detail->product ? $detail->product->name : 'Unknown Product',
                        'price' => $detail->price,
                        'quantity' => $detail->quantity
                    ];
                });
                
                return [
                    'id' => $order->id,
                    'orderNumber' => $order->code,
                    'customerName' => $order->user ? $order->user->name : 'Guest',
                    'date' => $order->created_at->toIso8601String(),
                    'total' => $order->grand_total,
                    'status' => $order->delivery_status,
                    'items' => $items
                ];
            });
            
            $meta = [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ];
            
            return $this->success([
                'data' => $formattedOrders,
                'meta' => $meta
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller orders',
                500
            );
        }
    }

    /**
     * Get seller inventory
     */
    public function getInventory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $page = $request->input('page', 1);
            $limit = min($request->input('limit', 50), 100);
            $status = $request->input('status');
            
            $productsQuery = Product::where('user_id', auth()->id())
                ->with('category');
                
            if ($status && $status !== 'all') {
                $productsQuery->where('published', $status === 'active' ? 1 : 0);
            }
            
            $total = $productsQuery->count();
            $products = $productsQuery->skip(($page - 1) * $limit)
                ->take($limit)
                ->orderBy('created_at', 'desc')
                ->get();
                
            $formattedProducts = $products->map(function($product) {
                return [
                    'id' => $product->id,
                    'productId' => $product->id,
                    'name' => $product->name,
                    'price' => $product->unit_price,
                    'quantity' => $product->current_stock,
                    'status' => $product->published ? 'active' : 'inactive',
                    'sku' => $product->sku ?? '',
                    'category' => $product->category ? $product->category->name : 'Uncategorized',
                    'lastUpdated' => $product->updated_at->toIso8601String()
                ];
            });
            
            $meta = [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ];
            
            return $this->success([
                'data' => $formattedProducts,
                'meta' => $meta
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller inventory',
                500
            );
        }
    }

    /**
     * Get seller analytics
     */
    public function getAnalytics(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:day,week,month,year'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $period = $request->input('period', 'month');
            
            // Determine date range
            $startDate = null;
            $endDate = Carbon::now();
            
            switch ($period) {
                case 'day':
                    $startDate = Carbon::now()->subDay();
                    break;
                case 'week':
                    $startDate = Carbon::now()->subWeek();
                    break;
                case 'month':
                    $startDate = Carbon::now()->subMonth();
                    break;
                case 'year':
                    $startDate = Carbon::now()->subYear();
                    break;
                default:
                    $startDate = Carbon::now()->subMonth();
            }
            
            // Get sales data
            $sales = Order::where('seller_id', auth()->id())
                ->where('payment_status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->get();
                
            $totalSales = $sales->sum('grand_total');
            $totalOrders = $sales->count();
            $averageOrderValue = $totalOrders > 0 ? $totalSales / $totalOrders : 0;
            
            // Get revenue data grouped by day
            $revenueData = [];
            $daysToLoop = $startDate->diffInDays($endDate) + 1;
            
            for ($i = 0; $i < $daysToLoop; $i++) {
                $date = $startDate->copy()->addDays($i);
                $dayRevenue = $sales->filter(function($sale) use ($date) {
                    return $sale->created_at->startOfDay()->equalTo($date->startOfDay());
                })->sum('grand_total');
                
                $revenueData[] = [
                    'date' => $date->format('Y-m-d'),
                    'amount' => round($dayRevenue, 2)
                ];
            }
            
            // Get orders data grouped by day
            $ordersData = [];
            
            for ($i = 0; $i < $daysToLoop; $i++) {
                $date = $startDate->copy()->addDays($i);
                $dayOrders = $sales->filter(function($sale) use ($date) {
                    return $sale->created_at->startOfDay()->equalTo($date->startOfDay());
                })->count();
                
                $ordersData[] = [
                    'date' => $date->format('Y-m-d'),
                    'count' => $dayOrders
                ];
            }
            
            // Get top products
            $topProducts = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->where('orders.seller_id', auth()->id())
                ->where('orders.payment_status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->selectRaw('products.id, products.name, SUM(order_details.quantity) as sold, SUM(order_details.price * order_details.quantity) as revenue')
                ->groupBy('products.id', 'products.name')
                ->orderBy('revenue', 'desc')
                ->limit(5)
                ->get()
                ->map(function($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sold' => $product->sold,
                        'revenue' => round($product->revenue, 2)
                    ];
                });
                
            // Calculate conversion rate (mock data)
            $conversionRate = rand(2, 10);
            
            $analyticsData = [
                'revenueData' => $revenueData,
                'ordersData' => $ordersData,
                'topProducts' => $topProducts,
                'summary' => [
                    'totalRevenue' => round($totalSales, 2),
                    'totalOrders' => $totalOrders,
                    'avgOrderValue' => round($averageOrderValue, 2),
                    'conversionRate' => $conversionRate
                ]
            ];
            
            return $this->success($analyticsData);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller analytics',
                500
            );
        }
    }

    /**
     * Get seller customers
     */
    public function getCustomers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $page = $request->input('page', 1);
            $limit = min($request->input('limit', 50), 100);
            $status = $request->input('status');
            
            // Get unique customer IDs who have ordered from this seller
            $customerIds = Order::where('seller_id', auth()->id())
                ->pluck('user_id')
                ->unique()
                ->filter();
                
            $customersQuery = User::whereIn('id', $customerIds);
            
            if ($status && $status !== 'all') {
                $customersQuery->where('banned', $status === 'inactive');
            }
            
            $total = $customersQuery->count();
            $customers = $customersQuery->skip(($page - 1) * $limit)
                ->take($limit)
                ->orderBy('created_at', 'desc')
                ->get();
                
            $formattedCustomers = $customers->map(function($customer) {
                $customerOrders = Order::where('seller_id', auth()->id())
                    ->where('user_id', $customer->id)
                    ->get();
                    
                $totalSpent = $customerOrders->where('payment_status', 'paid')->sum('grand_total');
                $totalOrders = $customerOrders->count();
                $lastOrder = $customerOrders->sortByDesc('created_at')->first();
                
                return [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone ?? 'N/A',
                    'avatar' => $customer->avatar ?? null,
                    'status' => $customer->banned ? 'inactive' : 'active',
                    'totalOrders' => $totalOrders,
                    'totalSpent' => round($totalSpent, 2),
                    'lastOrderDate' => $lastOrder ? $lastOrder->created_at->toIso8601String() : null,
                    'createdAt' => $customer->created_at->toIso8601String()
                ];
            });
            
            $meta = [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ];
            
            return $this->success([
                'data' => $formattedCustomers,
                'meta' => $meta
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller customers',
                500
            );
        }
    }

    /**
     * Get seller commissions
     */
    public function getCommissions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $page = $request->input('page', 1);
            $limit = min($request->input('limit', 50), 100);
            $status = $request->input('status');
            
            $commissionsQuery = OrderDetail::join('orders', 'order_details.order_id', '=', 'orders.id')
                ->join('products', 'order_details.product_id', '=', 'products.id')
                ->where('orders.seller_id', auth()->id())
                ->where('order_details.seller_commission', '>', 0);
                
            if ($status && $status !== 'all') {
                $commissionsQuery->where('orders.payment_status', $status);
            }
            
            $total = $commissionsQuery->count();
            $commissions = $commissionsQuery->skip(($page - 1) * $limit)
                ->take($limit)
                ->select([
                    'order_details.id',
                    'order_details.seller_commission as amount',
                    'orders.payment_status as status',
                    'orders.created_at as date',
                    'orders.code as order_number',
                    'products.name as product_name'
                ])
                ->orderBy('orders.created_at', 'desc')
                ->get();
                
            $formattedCommissions = $commissions->map(function($commission) {
                return [
                    'id' => $commission->id,
                    'amount' => round($commission->amount, 2),
                    'status' => $commission->status,
                    'date' => Carbon::parse($commission->date)->toIso8601String(),
                    'orderNumber' => $commission->order_number,
                    'productName' => $commission->product_name
                ];
            });
            
            $meta = [
                'total' => $total,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => ceil($total / $limit)
            ];
            
            return $this->success([
                'data' => $formattedCommissions,
                'meta' => $meta
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch seller commissions',
                500
            );
        }
    }
} 