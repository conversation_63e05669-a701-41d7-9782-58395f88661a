<?php

namespace App\Http\Resources\V3\Invoice;

use Illuminate\Http\Resources\Json\JsonResource;

class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this['id'],
            'invoiceNumber' => $this['invoiceNumber'],
            'orderId' => $this['orderId'],
            'orderNumber' => $this['orderNumber'],
            'date' => $this['date'],
            'dueDate' => $this['dueDate'],
            'status' => $this['status'],
            'items' => $this['items'],
            'subtotal' => $this['subtotal'],
            'shipping' => $this['shipping'],
            'discount' => $this['discount'],
            'tax' => $this['tax'],
            'total' => $this['total'],
            'paymentMethod' => $this['paymentMethod'],
            'billingAddress' => $this['billingAddress'],
            'seller' => $this['seller'],
            'invoiceDate' => $this['invoiceDate'],
            'formattedDate' => $this['formattedDate'],
            'customerNotes' => $this['customerNotes'],
            'downloadUrl' => $this['downloadUrl']
        ];
    }
}
