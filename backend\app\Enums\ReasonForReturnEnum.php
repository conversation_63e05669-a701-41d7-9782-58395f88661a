<?php

namespace App\Enums;

class ReasonForReturnEnum
{
    const DAMAGED_OR_DEFECTIVE = 'Defective/Damaged';
    const WRONG_ITEM= 'Wrong Item';
    const ITEM_NOT_AS_DESCRIBED = 'Item Not as Described';
    const SIZE_OR_FIT_ISSUE = 'Size/Fit Issue';
    const NO_LONGER_NEEDED = 'No Longer Needed';
    const CHANGED_MIND = 'Changed Mind';

    public static function getValues(): array
    {
        return [
            self::DAMAGED_OR_DEFECTIVE,
            self::WRONG_ITEM,
            self::ITEM_NOT_AS_DESCRIBED,
            self::SIZE_OR_FIT_ISSUE,
            self::NO_LONGER_NEEDED,
        ];
    }

    public static function getLabels(): array
    {
        return [
            self::DAMAGED_OR_DEFECTIVE => 'Defective or damaged item',
            self::WRONG_ITEM => 'Received a different item than ordered',
            self::ITEM_NOT_AS_DESCRIBED => 'Item does not match the description',
            self::SIZE_OR_FIT_ISSUE => 'Issue with size or fit',
            self::NO_LONGER_NEEDED => 'Changed mind or no longer needed',
        ];
    }

    public static function getDetails(): array
    {
        return [
            self::DAMAGED_OR_DEFECTIVE => 'The item was damaged or defective when received.',
            self::WRONG_ITEM => 'You received the wrong item from what you ordered.',
            self::ITEM_NOT_AS_DESCRIBED => 'The product received is different from what was described.',
            self::SIZE_OR_FIT_ISSUE => 'The item does not fit as expected or has sizing issues.',
            self::NO_LONGER_NEEDED => 'You decided not to keep the item for personal reasons.',
        ];
    }

    public static function getLabel(string $status): string
    {
        return self::getLabels()[$status] ?? 'Unknown';
    }

    public static function getDetail(string $status): string
    {
        return self::getDetails()[$status] ?? 'No additional details available.';
    }
}
