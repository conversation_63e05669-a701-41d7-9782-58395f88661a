<?php

namespace App\Http\Resources\V3\Rerurn;

use App\Models\OrderDetail;
use App\Models\Upload;
use Illuminate\Http\Resources\Json\JsonResource;

class RerurnRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $commonFields = [
            'id' => $this->return_code,
            'orderCode' => $this->order->code,
            'orderDate' => date('d-m-Y h:i A', $this->order->date),
            'requestDate' => date('d-m-Y h:i A', $this->date),
            'status' => $this->return_status instanceof \App\Enums\ReturnStatus 
                ? ucfirst($this->return_status->value) 
                : ucfirst($this->return_status),
            'returnMethod' => $this->returnMethod ?? "",
            'returnCondition' => $this->returnCondition ?? "",
            'originalPackaging' => $this->originalPackaging ?? '',
            'description' => $this->user_note,
            'refundAmount' => (float) $this->amount,
            'refundMethod' => "",
            'reason' => $this->reason_for_return,
            'preferredResolution' => $this->preferred_resolution,
            'products' => $this->return_request_products->map(function ($item) {
                $orderDetail = OrderDetail::where('id', $item->order_detail_id)->first();
                return [
                    'id' => $item->product->id,
                    'slug' => $item->product->slug,
                    'name' => $item->product->name,
                    'thumbnail' => $item->product->thumbnail_img ? uploaded_asset($item->product->thumbnail_img) : "",
                    'quantity' => (float) $item->quantity,
                    'price' => (float) $item->unit_price,
                    'amount' => (float) $item->amount,
                    'reason' => $this->reason_for_return,
                    'condition' => $this->returnCondition ?? '',
                    'sku' => (string) product_sku_by_product_id($item->product, $orderDetail->variation) ?? null,
                ];
            }),
        ];

        // If this is an index request, return basic fields
        if ($request->route() && $request->route()->getActionMethod() === 'index') {
            return $commonFields;
        }

        // For detailed view, include attachments and timeline
        return array_merge($commonFields, [
            'attachments' => $this->attachments ? array_map(function ($image) {
                $upload = Upload::where('id', $image)->first();
                return [
                    'type' => $upload->type ?? 'image',
                    'url' => uploaded_asset($image),
                    'filename' => $upload->file_original_name ?? 'attachment',
                ];
            }, explode(',', $this->attachments)) : [],
            'timeline' => $this->getReturnRequestTimeline($this->activityLogs ?? collect())
        ]);
    }

    private function getReturnRequestTimeline($activityLogs)
    {
        return $activityLogs->map(function ($log) {
            return [
                'status' => ucfirst($log->new_status),
                'timestamp' => $log->created_at->toIso8601String(),
                'description' => $log->description,
            ];
        });
    }
}
