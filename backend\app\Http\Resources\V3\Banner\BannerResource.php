<?php

namespace App\Http\Resources\V3\Banner;

use Illuminate\Http\Resources\Json\JsonResource;

class BannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'subtitle' => $this->subtitle,
            'position' => $this->position,
            'image_url' => $this->image_url,
            'mobile_image_url' => $this->mobile_image_url,
            'background_color' => $this->background_color,
            'text_color' => $this->text_color,
            'button_text' => $this->button_text,
            'button_url' => $this->button_url,
            'overlay_opacity' => $this->overlay_opacity,
            'start_date' => isset($this->start_date) ? $this->start_date->toIso8601String() : null,
            'end_date' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'is_active' => (bool) $this->is_active,
            'page_location' => $this->page_location,
            'priority' => $this->priority,
            'width' => $this->width,
            'height' => $this->height,
            'campaign' => $this->when($this->relationLoaded('campaign'), function() {
                return [
                    'id' => $this->campaign->id,
                    'title' => $this->campaign->title,
                ];
            }),
            'tracking_code' => $this->tracking_code,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 