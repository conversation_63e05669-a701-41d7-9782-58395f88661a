<?php

namespace App\Imports;

use DB;
use Artisan;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Upload;
use App\Models\Cart;
use App\Models\Wishlist;
use App\Models\ImportStatus;
use App\Models\Brand;
use App\Models\BrandTranslation;
use App\Services\ProductService;
use App\Services\ProductTaxService;
use App\Services\ProductFlashDealService;
use App\Services\ProductStockService;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;

class BrandBulkImport implements ToCollection, WithHeadingRow, WithChunkReading, WithCalculatedFormulas, SkipsEmptyRows
{
    private $importStatus;
    private $userId;
    private $rows = 0;
    private $successCount = 0;
    private $errorCount = 0;
    public $errors = [];
    public $rejectItem = [];

    public function __construct(ImportStatus $importStatus, $userId)
    {
        $this->userId = $userId;
        $this->importStatus = $importStatus;
    }

    public function transformHeader(array $header): array
    {
        return array_map(function($item) {
            return Str::snake(strtolower($item));
        }, $header);
    }

    public function collection(Collection $rows)
    {
        $user = User::where('id', $this->userId)->first();
        $canImport = true;

        if (!$user) {
            Log::error('Error in : User Not Found.');
            $this->importStatus->update([
                    'status' => 'failed',
                    'error_messages' => ['Unauthorized.'],
                ]);
            return back();
        }

        Log::info('User type for brand bulk: ' . $user->user_type);
        $totalRows = count($rows);
        $this->importStatus->update(['status' => 'in_progress']);

        if ($user->user_type == 'seller' && addon_is_activated('seller_subscription')) {
            if ((count($rows) + $user->products()->count()) > $user->shop->product_upload_limit
                || $user->shop->package_invalid_at == null
                || now()->diffInDays($user->shop->package_invalid_at, false) < 0
            ) {
                $canImport = false;
                flash()->warning(trans('Please upgrade your package.'));
                $this->importStatus->update([
                    'status' => 'failed',
                    'error_messages' => ['Please upgrade your package.'],
                ]);
                return redirect()->back();
            }
        }

        if ($canImport) {
            foreach ($rows as $row) {
                if (($this->importStatus->purpose === 'insert' && $row->has('id')) || (($this->importStatus->purpose === 'update' or $this->importStatus->purpose === 'delete') && !$row->has('id'))) {
                        $this->importStatus->update([
                            'status' => 'failed',
                            'error_messages' => ['Data Format is wrong, Please check your xlsx file.'],
                        ]);
                    return back();
                }


                if ($this->importStatus->purpose === 'insert') {

                    $validator = Validator::make($row->toArray(), $this->rules());
                    if ($validator->fails()) {
                        $this->errors[] = $validator->errors()->all();
                        $this->rejectItem[] = $row['name'];
                        Log::warning('Validation failed for row, Errors: ' . json_encode($validator->errors()));
                        $this->errorCount++;
                    } else {
                        $this->processRow($row);
                        $this->successCount++;
                    }
                }else if($this->importStatus->purpose === 'update'){
                    $validator = Validator::make($row->toArray(), $this->rulesForUpdate());
                    if ($validator->fails()) {
                        $this->errors[] = $validator->errors()->all();
                        $this->rejectItem[] = $row['id'];
                        Log::warning('Validation failed for row, Errors: ' . json_encode($validator->errors()));
                        $this->errorCount++;
                    } else {
                        $this->processRow($row);
                        $this->successCount++;
                    }
                }else {
                    $this->deleteBrand($row['id']);
                    $this->successCount++;
                }
                $this->rows++;
            }

            // Update the import status
            $this->importStatus->update([
                'reject_item' => $this->rejectItem,
                'success' => $this->successCount,
                'errors' => $this->errorCount,
                'pending' => $this->importStatus->total - ($this->successCount + $this->errorCount),
                'status' => $this->rows === $this->importStatus->total ? 'completed' : 'in_progress',
                'progress' => (($this->successCount + $this->errorCount) / $this->importStatus->total) * 100,
                'error_messages' => $this->errors,
            ]);
        }
        Log::info('Finished chunk import of collection');
    }

    private function processRow($row)
    {
        try {
            if ($row->has('id') && $this->importStatus->purpose === 'update') {
                $this->updateBrand($row);
            } else {
                $this->createBrand($row);
            }
        } catch (\Exception $e) {
            Log::error('Error in processRow: ' .$this->errorCount. ' '. $e->getMessage());
            $this->errorCount++;
        }
    }

    public function createBrand($data)
    {
        $collection = collect($data);
        $user = User::find($this->userId);

        $collection['top'] = $collection['top'] ?? 0;

        Log::info('test for data: ' .$data['name']);
        
        try {
            DB::beginTransaction(); // Start transaction

            $brand = new Brand;
            $brand->name = $collection['name'];
            $brand->meta_title = $collection['meta_title'];
            $brand->meta_description = $collection['meta_description'];
            if ($collection['slug'] != null) {
                $brand->slug = str_replace(' ', '-', $collection['slug']);
            }
            else {
                $brand->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $collection['name'])).'-'.Str::random(5);
            }

            $brand->logo = $collection['logo'];
            $brand->added_by = $this->userId;
            $brand->save();

            $brand_translation = BrandTranslation::firstOrNew(['lang' => env('DEFAULT_LANGUAGE'), 'brand_id' => $brand->id]);
            $brand_translation->name = $collection['name'];
            $brand_translation->save();

            DB::commit();
            Log::info('Brand successfully stored');
        }catch (\Exception $e){
            $this->errors[] = $e->getMessage();
            $this->rejectItem[] = $data['name'];
            Log::error('Error for data store: ' . $data['name']);
            Log::error('Error message: ' . $e->getMessage());
            DB::rollBack();
        }
    }


    public function updateBrand($data)
    {
       $collection = collect($data);
       $user = User::find($this->userId);
        
        
        try{
            DB::beginTransaction(); // Start transaction
            $brand = Brand::findOrFail($collection['id']);

            // Update common attributes if provided
            if ($collection['lang'] == env("DEFAULT_LANGUAGE") && isset($collection['name'])) {
                $brand->name = $collection['name'];
            }
            if (isset($collection['meta_title'])) {
                $brand->meta_title = $collection['meta_title'];
            }
            if (isset($collection['meta_description'])) {
                $brand->meta_description = $collection['meta_description'];
            }
            if (isset($collection['slug'])) {
                $brand->slug = strtolower($collection['slug']);
            } else {
                $brand->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $collection['name'])) . '-' . Str::random(5);
            }
            if (isset($collection['logo'])) {
                $brand->logo = $collection['logo'];
            }
            $brand->added_by = $this->userId;
            $brand->save();

            // Update or create BrandTranslation if name is provided
            if (isset($collection['name'])) {
                $brand_translation = BrandTranslation::updateOrCreate(
                    ['lang' => $collection['lang'], 'brand_id' => $brand->id],
                    ['name' => $collection['name']]
                );
            }
            DB::commit();
        }catch(\Exception $e) {
            DB::rollBack();
            $this->errors[] = $e->getMessage();
            $this->rejectItem[] = $data['name'];
            Log::error('Error for data store: ' . $e->getMessage());
        }
    }

    public function deleteBrand($id)
    {
        Log::info('value data: '.$id);

        try{
            DB::beginTransaction(); // Start transaction
            $brand = Brand::findOrFail($id);
            Product::where('brand_id', $brand->id)->delete();
            foreach ($brand->brand_translations as $key => $brand_translation) {
                $brand_translation->delete();
            }
            Brand::destroy($id);
            DB::commit();
        }catch(\Exception $e) {
            DB::rollBack();
            $this->errors[] = $e->getMessage();
            Log::error('Error for data delete: ' . $e->getMessage());
            $this->rejectItem[] = ''.$id;
        }
    }


    public function model(array $row)
    {
        ++$this->rows;
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string','unique:brands', 'max:200'],
        ];
    }
    public function rulesForUpdate(): array
    {
        return [
            'name' => ['string', 'max:200'],
        ];
    }
    public function chunkSize(): int
    {
        return 100;
    }

    public function headingRow(): int
    {
        return 1;
    }
}
