<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class RowsCountImport implements ToCollection, WithChunkReading
{
    public $rowCount = 0;

    public function collection(Collection $rows)
    {
        $this->rowCount += $rows->count();
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
