<?php

namespace App\Http\Controllers\dropshipper;

use App\Http\Requests\dropshipperUserRequest;
use App\Models\User;
use App\Models\B2BProfile;// dropshipper shop
use Auth;
use Hash;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        //dd('dd');
        $cID= auth()->user()->id;
        //dd($cID);
        $data['shopModel'] = B2BProfile::where('user_id', $cID )->first();
        //dd($shopModel->verification_status);

        //$cID= auth()->user()->id;
        $user = User::where('id', $cID )->first();
        //dd($user);
        if($user->email_verified_at === NULL){
            //dd('dd1');
            return view('dropshipper.waiting_for_verification');
        }else{
            //dd('dd2');
            if($data['shopModel']->verification_status == 0){
                $cID= auth()->user()->id;
                //dd($cID);
                $data['user'] = User::where('id', $cID )->first();
                return view('dropshipper.waiting_for_approval', $data);
            }else{
                $data['user'] = Auth::user();
                //dd($data['user']);
                $data['addresses'] = $data['user']->addresses;
                $data['shopModel'] =  B2BProfile::where('user_id',$data['user']->id)->first();
                return view('dropshipper.profile.index', $data);
            }
        }


    }

    public function profileModify()
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $data['addresses'] = $data['user']->addresses;
        $data['shopModel'] =  B2BProfile::where('user_id',$data['user']->id)->first();
        return view('dropshipper.profile.profile-modify', $data);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(dropshipperUserRequest $request , $id)
    {
        if(env('DEMO_MODE') == 'On'){
            flash(translate('Sorry! the action is not permitted in demo '))->error();
            return back();
        }

        $user = User::findOrFail($id);
        $user->name = $request->name;
        $user->last_name = $request->last_name;
        $user->phone = $request->phone;
        //dd($request->confirm_password, $request->new_password);
        if($request->new_password != null && ($request->new_password == $request->confirm_password)){
            $user->password = Hash::make($request->new_password);
        }else{
            flash(translate('Password Does not match'))->error();
            return back();
        }

       // $user->avatar_original = $request->photo;

        //$shop = $user->shop;
        $shop = B2BProfile::where('user_id', $user->id)->first();
        if($shop){
            $shop->cash_on_delivery_status = $request->cash_on_delivery_status;
            $shop->bank_payment_status = $request->bank_payment_status;
            $shop->bank_name = $request->bank_name;
            $shop->bank_acc_name = $request->bank_acc_name;
            $shop->bank_acc_no = $request->bank_acc_no;
            $shop->bank_routing_no = $request->bank_routing_no;

            $shop->save();
        }

        $user->save();

        flash(translate('Your Profile has been updated successfully!'))->success();
        return back();
    }
    public function dropshipper2PasswordChangeView()
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $data['addresses'] = $data['user']->addresses;
        $data['shopModel'] =  B2BProfile::where('user_id',$data['user']->id)->first();
        return view('dropshipper.profile.update-pass', $data);
    }
    public function dropshipperUserPassChangeStore(Request $request)
    {
        //dd('dropshipper');
        //dd($request->all());
        $user = User::findOrFail($request->id);
        //dd($user);
        $this->validate($request, [
            //'current_password' => ['required','current_password'],
            //'password' => 'required|confirmed|string|min:8',
            'current_password' => ['required', function ($attr, $password, $validation) use ($user) {
                if (!\Hash::check($password, $user->password)) {
                    return $validation(__('The current password is incorrect.'));
                    flash(translate('The current password is incorrect.!'))->error();
                    return back();
                }
            }],
        ]);
        if($request->new_password != null && ($request->new_password == $request->confirm_password)){
            $user->password = Hash::make($request->new_password);
            $user->save();
            flash(translate('Your Password has been updated successfully!'))->success();
            return back();
        }else{
            flash(translate('The password does not match.!'))->error();
            return back();
        }


    }
}
