<?php

namespace App\Http\Controllers\Api\V3\Promotions\Admin;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\FlashSale\FlashSaleResource;
use App\Models\Product;
use App\Models\Promotions\FlashSale;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AdminFlashSaleController extends ApiResponse
{
    /**
     * Get all flash sales
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'integer|min:1',
                'limit' => 'integer|min:1|max:100',
                'status' => 'nullable|string|in:active,upcoming,expired,all',
                'sort_by' => 'nullable|string|in:created_at,start_date,end_date,title',
                'sort_order' => 'nullable|string|in:asc,desc',
                'search' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $status = $request->input('status', 'all');
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $search = $request->input('search');

            $query = FlashSale::query();

            // Apply status filter
            if ($status !== 'all') {
                $now = now();
                
                switch ($status) {
                    case 'active':
                        $query->where('is_active', true)
                            ->where('start_date', '<=', $now)
                            ->where('end_date', '>=', $now);
                        break;
                    case 'upcoming':
                        $query->where('is_active', true)
                            ->where('start_date', '>', $now);
                        break;
                    case 'expired':
                        $query->where(function($q) use ($now) {
                            $q->where('is_active', false)
                              ->orWhere('end_date', '<', $now);
                        });
                        break;
                }
            }

            // Apply search
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', '%' . $search . '%')
                      ->orWhere('slug', 'like', '%' . $search . '%')
                      ->orWhere('description', 'like', '%' . $search . '%');
                });
            }

            // Apply sorting
            $query->orderBy($sortBy, $sortOrder);

            // Get paginated results
            $flashSales = $query->paginate($limit, ['*'], 'page', $page);

            return $this->success([
                'flash_sales' => FlashSaleResource::collection($flashSales),
                'pagination' => [
                    'total' => $flashSales->total(),
                    'per_page' => $flashSales->perPage(),
                    'current_page' => $flashSales->currentPage(),
                    'last_page' => $flashSales->lastPage(),
                    'from' => $flashSales->firstItem(),
                    'to' => $flashSales->lastItem()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Create a new flash sale
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after:start_date',
                'is_active' => 'boolean',
                'banner_image' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'countdown_style' => 'nullable|string|max:50',
                'max_discount_percentage' => 'nullable|integer|min:0|max:100',
                'product_limit' => 'nullable|integer|min:0',
                'priority' => 'nullable|integer|min:0',
                'sale_type' => 'nullable|string|max:50',
                'terms_conditions' => 'nullable|string',
                'is_featured' => 'boolean',
                'timer_position' => 'nullable|string|max:50',
                'products' => 'nullable|array',
                'products.*.id' => 'required|exists:products,id',
                'products.*.flash_discount' => 'required|numeric|min:0',
                'products.*.discount_type' => 'required|string|in:percentage,fixed',
                'products.*.featured' => 'boolean',
                'products.*.total_allowed_quantity' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Create flash sale
            $flashSale = new FlashSale();
            $flashSale->title = $request->input('title');
            $flashSale->slug = Str::slug($request->input('title')) . '-' . Str::random(5);
            $flashSale->description = $request->input('description');
            $flashSale->start_date = $request->input('start_date');
            $flashSale->end_date = $request->input('end_date');
            $flashSale->is_active = $request->input('is_active', true);
            $flashSale->banner_image = $request->input('banner_image');
            $flashSale->background_color = $request->input('background_color');
            $flashSale->text_color = $request->input('text_color');
            $flashSale->countdown_style = $request->input('countdown_style');
            $flashSale->max_discount_percentage = $request->input('max_discount_percentage');
            $flashSale->product_limit = $request->input('product_limit');
            $flashSale->priority = $request->input('priority', 0);
            $flashSale->sale_type = $request->input('sale_type');
            $flashSale->terms_conditions = $request->input('terms_conditions');
            $flashSale->is_featured = $request->input('is_featured', false);
            $flashSale->timer_position = $request->input('timer_position', 'top');
            $flashSale->save();

            // Attach products if provided
            if ($request->has('products') && is_array($request->input('products'))) {
                $products = $request->input('products');
                $attachData = [];

                foreach ($products as $product) {
                    $productDetails = Product::find($product['id']);
                    if (!$productDetails) {
                        continue;
                    }

                    $discount = $product['flash_discount'];
                    $discountType = $product['discount_type'];
                    $flashPrice = 0;

                    // Calculate flash price based on discount
                    if ($discountType === 'percentage') {
                        $flashPrice = $productDetails->price * (1 - ($discount / 100));
                    } else {
                        $flashPrice = $productDetails->price - $discount;
                    }

                    // Ensure flash price isn't negative
                    $flashPrice = max(0, $flashPrice);

                    $attachData[$product['id']] = [
                        'flash_discount' => $discount,
                        'discount_type' => $discountType,
                        'featured' => $product['featured'] ?? false,
                        'flash_price' => $flashPrice,
                        'sold_items' => 0,
                        'total_allowed_quantity' => $product['total_allowed_quantity'] ?? null
                    ];
                }

                $flashSale->products()->attach($attachData);
            }

            DB::commit();
            return $this->success(new FlashSaleResource($flashSale), 'Flash sale created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a flash sale by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $flashSale = FlashSale::with('products')->find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            return $this->success(new FlashSaleResource($flashSale));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Update a flash sale
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $flashSale = FlashSale::find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after:start_date',
                'is_active' => 'boolean',
                'banner_image' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'countdown_style' => 'nullable|string|max:50',
                'max_discount_percentage' => 'nullable|integer|min:0|max:100',
                'product_limit' => 'nullable|integer|min:0',
                'priority' => 'nullable|integer|min:0',
                'sale_type' => 'nullable|string|max:50',
                'terms_conditions' => 'nullable|string',
                'is_featured' => 'boolean',
                'timer_position' => 'nullable|string|max:50',
                'products' => 'nullable|array',
                'products.*.id' => 'required|exists:products,id',
                'products.*.flash_discount' => 'required|numeric|min:0',
                'products.*.discount_type' => 'required|string|in:percentage,fixed',
                'products.*.featured' => 'boolean',
                'products.*.total_allowed_quantity' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Update flash sale fields if they are provided
            if ($request->has('title')) {
                $flashSale->title = $request->input('title');
                $flashSale->slug = Str::slug($request->input('title')) . '-' . Str::random(5);
            }
            
            if ($request->has('description')) {
                $flashSale->description = $request->input('description');
            }
            
            if ($request->has('start_date')) {
                $flashSale->start_date = $request->input('start_date');
            }
            
            if ($request->has('end_date')) {
                $flashSale->end_date = $request->input('end_date');
            }
            
            if ($request->has('is_active')) {
                $flashSale->is_active = $request->input('is_active');
            }
            
            if ($request->has('banner_image')) {
                $flashSale->banner_image = $request->input('banner_image');
            }
            
            if ($request->has('background_color')) {
                $flashSale->background_color = $request->input('background_color');
            }
            
            if ($request->has('text_color')) {
                $flashSale->text_color = $request->input('text_color');
            }
            
            if ($request->has('countdown_style')) {
                $flashSale->countdown_style = $request->input('countdown_style');
            }
            
            if ($request->has('max_discount_percentage')) {
                $flashSale->max_discount_percentage = $request->input('max_discount_percentage');
            }
            
            if ($request->has('product_limit')) {
                $flashSale->product_limit = $request->input('product_limit');
            }
            
            if ($request->has('priority')) {
                $flashSale->priority = $request->input('priority');
            }
            
            if ($request->has('sale_type')) {
                $flashSale->sale_type = $request->input('sale_type');
            }
            
            if ($request->has('terms_conditions')) {
                $flashSale->terms_conditions = $request->input('terms_conditions');
            }
            
            if ($request->has('is_featured')) {
                $flashSale->is_featured = $request->input('is_featured');
            }
            
            if ($request->has('timer_position')) {
                $flashSale->timer_position = $request->input('timer_position');
            }
            
            $flashSale->save();

            // Update products if provided
            if ($request->has('products') && is_array($request->input('products'))) {
                $products = $request->input('products');
                $syncData = [];

                foreach ($products as $product) {
                    $productDetails = Product::find($product['id']);
                    if (!$productDetails) {
                        continue;
                    }

                    $discount = $product['flash_discount'];
                    $discountType = $product['discount_type'];
                    $flashPrice = 0;

                    // Calculate flash price based on discount
                    if ($discountType === 'percentage') {
                        $flashPrice = $productDetails->price * (1 - ($discount / 100));
                    } else {
                        $flashPrice = $productDetails->price - $discount;
                    }

                    // Ensure flash price isn't negative
                    $flashPrice = max(0, $flashPrice);

                    $syncData[$product['id']] = [
                        'flash_discount' => $discount,
                        'discount_type' => $discountType,
                        'featured' => $product['featured'] ?? false,
                        'flash_price' => $flashPrice,
                        'sold_items' => 0, // Keep existing value or reset to 0
                        'total_allowed_quantity' => $product['total_allowed_quantity'] ?? null
                    ];
                }

                // Sync will remove products not in the array
                $flashSale->products()->sync($syncData);
            }

            DB::commit();
            return $this->success(new FlashSaleResource($flashSale), 'Flash sale updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Delete a flash sale
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $flashSale = FlashSale::find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            // Detach products
            $flashSale->products()->detach();
            
            // Delete flash sale
            $flashSale->delete();

            DB::commit();
            return $this->success(null, 'Flash sale deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Add products to a flash sale
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function addProducts(Request $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $flashSale = FlashSale::find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'products' => 'required|array',
                'products.*.id' => 'required|exists:products,id',
                'products.*.flash_discount' => 'required|numeric|min:0',
                'products.*.discount_type' => 'required|string|in:percentage,fixed',
                'products.*.featured' => 'boolean',
                'products.*.total_allowed_quantity' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $products = $request->input('products');
            $attachData = [];

            foreach ($products as $product) {
                $productDetails = Product::find($product['id']);
                if (!$productDetails) {
                    continue;
                }

                $discount = $product['flash_discount'];
                $discountType = $product['discount_type'];
                $flashPrice = 0;

                // Calculate flash price based on discount
                if ($discountType === 'percentage') {
                    $flashPrice = $productDetails->price * (1 - ($discount / 100));
                } else {
                    $flashPrice = $productDetails->price - $discount;
                }

                // Ensure flash price isn't negative
                $flashPrice = max(0, $flashPrice);

                $attachData[$product['id']] = [
                    'flash_discount' => $discount,
                    'discount_type' => $discountType,
                    'featured' => $product['featured'] ?? false,
                    'flash_price' => $flashPrice,
                    'sold_items' => 0,
                    'total_allowed_quantity' => $product['total_allowed_quantity'] ?? null
                ];
            }

            // Attach new products (will ignore duplicates)
            $flashSale->products()->attach($attachData);

            DB::commit();
            return $this->success(new FlashSaleResource($flashSale->fresh(['products'])), 'Products added to flash sale successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Remove products from a flash sale
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function removeProducts(Request $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $flashSale = FlashSale::find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'product_ids' => 'required|array',
                'product_ids.*' => 'required|exists:products,id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $productIds = $request->input('product_ids');

            // Detach products
            $flashSale->products()->detach($productIds);

            DB::commit();
            return $this->success(new FlashSaleResource($flashSale->fresh(['products'])), 'Products removed from flash sale successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get flash sale statistics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getStats(int $id): JsonResponse
    {
        try {
            $flashSale = FlashSale::with('products')->find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            // Calculate stats
            $totalProducts = $flashSale->products->count();
            $totalSales = $flashSale->products->sum('pivot.sold_items');
            $totalRevenue = 0;

            foreach ($flashSale->products as $product) {
                $totalRevenue += $product->pivot->flash_price * $product->pivot->sold_items;
            }

            // Top selling products
            $topSellingProducts = $flashSale->products()
                ->orderByPivot('sold_items', 'desc')
                ->take(5)
                ->get()
                ->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'sold_items' => $product->pivot->sold_items,
                        'revenue' => $product->pivot->flash_price * $product->pivot->sold_items,
                        'discount' => $product->pivot->flash_discount,
                        'discount_type' => $product->pivot->discount_type
                    ];
                });

            // Time remaining
            $timeRemaining = null;
            if ($flashSale->isActive()) {
                $now = now();
                $timeRemaining = [
                    'seconds' => $now->diffInSeconds($flashSale->end_date, false),
                    'formatted' => $flashSale->end_date->diffForHumans(['parts' => 2])
                ];
            }

            return $this->success([
                'total_products' => $totalProducts,
                'total_sales' => $totalSales,
                'total_revenue' => $totalRevenue,
                'progress_percentage' => $flashSale->getProgressPercentage(),
                'time_remaining' => $timeRemaining,
                'top_selling_products' => $topSellingProducts,
                'status' => $flashSale->isActive() ? 'active' : ($flashSale->start_date > now() ? 'upcoming' : 'expired')
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Change flash sale status (activate/deactivate)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $flashSale = FlashSale::find($id);

            if (!$flashSale) {
                return $this->error('Flash sale not found', 'The requested flash sale does not exist', 404);
            }

            $flashSale->is_active = $request->input('is_active');
            $flashSale->save();

            return $this->success(new FlashSaleResource($flashSale), 'Flash sale status updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 