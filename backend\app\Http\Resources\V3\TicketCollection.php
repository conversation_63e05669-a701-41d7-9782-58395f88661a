<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\ResourceCollection;

class TicketCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'tickets' => $this->collection->map(function ($ticket) {
                // For collection listings, we'll use a simplified version of the ticket resource
                // to avoid loading all messages and attachments for each ticket
                $data = [
                    'id' => $ticket->code,
                    'code' => $ticket->code,
                    'subject' => $ticket->subject,
                    'description' => $ticket->details,
                    'status' => $this->formatStatus($ticket->status),
                    'priority' => $this->determinePriority($ticket->status),
                    'category' => optional($ticket->ticketCategory)->name ?? 'General',
                    'userId' => $ticket->user->name,
                    'userEmail' => $ticket->user->email,
                    'userName' => $ticket->user->name,
                    'createdAt' => $ticket->created_at->toIso8601String(),
                    'updatedAt' => $ticket->updated_at->toIso8601String(),
                    'lastResponseAt' => $this->getLastResponseTime($ticket),

                ];

                // Add attachments if present
                $data['attachments'] = [];
                if (!empty($ticket->files)) {
                    $fileIds = explode(',', $ticket->files);
                    foreach ($fileIds as $fileId) {
                        $file = \App\Models\Upload::find($fileId);
                        if ($file) {
                            $data['attachments'][] = [
                                'id' => $fileId,
                                'ticketId' => $ticket->id,
                                'fileName' => $file->file_original_name . '.' . $file->extension,
                                'fileSize' => $file->file_size,
                                'fileType' => $file->type,
                                'url' => uploaded_asset($fileId),
                                'uploadedAt' => $file->created_at->toIso8601String(),
                            ];
                        }
                    }
                }

                return $data;
            }),
            'meta' => [
                'currentPage' => $this->currentPage(),
                'from' => $this->firstItem(),
                'lastPage' => $this->lastPage(),
                'path' => $request->url(),
                'perPage' => $this->perPage(),
                'to' => $this->lastItem(),
                'total' => $this->total(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
        ];
    }

    /**
     * Format the ticket status for API response
     *
     * @param string $status
     * @return string
     */
    private function formatStatus($status)
    {
        if ($status === 'pending') {
            return 'pending';
        } elseif ($status === 'solved') {
            return 'resolved';
        } else {
            return 'in_progress';
        }
    }

    /**
     * Determine priority based on status
     *
     * @param string $status
     * @return string
     */
    private function determinePriority($status)
    {
        if ($status === 'pending') {
            return 'high';
        } elseif ($status === 'solved') {
            return 'low';
        } else {
            return 'medium';
        }
    }

    /**
     * Get the timestamp of the last response
     *
     * @param \App\Models\Ticket $ticket
     * @return string
     */
    private function getLastResponseTime($ticket)
    {
        if ($ticket->ticketreplies && $ticket->ticketreplies->count() > 0) {
            return $ticket->ticketreplies->sortByDesc('created_at')->first()->created_at->toIso8601String();
        }

        return $ticket->updated_at->toIso8601String();
    }
}
