<?php

namespace App\Http\Controllers;

use App\Http\Requests\B2BRegistrationRequest; 
use Illuminate\Http\Request;
use App\Models\B2B;
use App\Models\B2BProfile;
use App\Models\User;
use App\Models\BusinessSetting;
use Auth;
use Hash;
use App\Notifications\EmailVerificationNotification;
use App\Notifications\OTPEmailVerificationNotification;
use Illuminate\Support\Facades\Notification;

class B2BProfileController extends Controller
{

    public function __construct()
    {
        $this->middleware('user', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $shop = Auth::user()->shop;
        return view('seller.shop', compact('shop'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (Auth::check()) {
			if((Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'customer' || Auth::user()->user_type == 'seller')) {
				flash(translate('Admin or Customer or seller cannot be a dropshipper'))->error();
				return back();
			} if(Auth::user()->user_type == 'b2b'){
				flash(translate('This user already a dropshipper'))->error();
				return back();
			}

        } else {
            return view('frontend.b2b_form');
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(B2BRegistrationRequest $request)
    {
        //dd($request->all());
        $user = new User;
        $user->user_name = $request->user_name;
        $user->name = $request->name; // first name
        $user->last_name = $request->last_name;
        // $user->country_code = $request->country_code;
        // $user->phone = $request->phone;
        $user->email = $request->email;
        $user->user_type = "b2b";
        $user->city = $request->state_province;
        $user->state= $request->state_name;
        //$user->news_letter= $request->news_letter;
        //$user->terms_conditions= $request->terms_conditions;
        $user->password = Hash::make($request->password);

        if ($user->save()) {
           $dropshipperProfile = new B2BProfile;
            $dropshipperProfile->user_id = $user->id;
            $dropshipperProfile->name = $request->shop_name;
            // $dropshipperProfile->country_code = $request->country_code;
            // $dropshipperProfile->phone = $request->phone;
            $dropshipperProfile->address = $request->shop_address;
            //$dropshipperProfile->website_url = $request->website_url;
            $dropshipperProfile->address = $request->address;
            //$dropshipperProfile->slug = preg_replace('/\s+/', '-', str_replace("/", " ", $request->website_url));
            $dropshipperProfile->save();
            auth()->login($user, false);

            if (BusinessSetting::where('type', 'email_verification')->first()->value == 0) {
                $user->email_verified_at = date('Y-m-d H:m:s');
                $user->save();
            } else {
                //$user->notify(new EmailVerificationNotification());
                $user->notify(new OTPEmailVerificationNotification());
            }

            flash(translate('Please Verify Your Mail Now!'))->success();
            //return redirect()->route('dropshipper.shop.index');
            return redirect()->route('b2b.dashboard');


        }

        flash(translate('Sorry! Something went wrong.'))->error();
        return back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    public function destroy($id)
    {
        //
    }
}
