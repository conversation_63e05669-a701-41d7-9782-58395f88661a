<?php

namespace App\Http\Resources\V3\ReturnMethods;

use Illuminate\Http\Resources\Json\JsonResource;

class ShippingLabelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'labelUrl' => $this['labelUrl'],
            'expirationDate' => $this['expirationDate'],
            'instructions' => $this['instructions']
        ];
    }
}
