<?php

namespace App\Events\Messaging;

use App\Models\Messaging\MessageThread;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ThreadUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The thread instance.
     *
     * @var \App\Models\Messaging\MessageThread
     */
    public $thread;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\Messaging\MessageThread  $thread
     * @return void
     */
    public function __construct(MessageThread $thread)
    {
        $this->thread = $thread;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to the thread channel and to all participants' private channels
        $channels = [
            new PrivateChannel('thread.' . $this->thread->id),
        ];
        
        // Get all thread participants
        $participants = $this->thread->participants;
        
        foreach ($participants as $participant) {
            $channels[] = new PrivateChannel('user.' . $participant->user_id . '.messages');
        }
        
        return $channels;
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'thread.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        // Load relationships to include in the broadcast
        $this->thread->load(['participants.user', 'lastMessage.sender']);
        
        return [
            'thread' => $this->thread,
        ];
    }
} 