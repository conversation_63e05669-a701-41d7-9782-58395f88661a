<?php

namespace App\Listeners;

use App\Enums\NotificationType;
use App\Enums\OrderStatus;
use App\Models\CancelReason;
use App\Models\Order;
use App\Events\OrderCreated;
use App\Events\OrderUpdated;
use App\Models\OrderDetail;
use App\Models\User;
use App\Models\UserNotification;
use App\Notifications\order\DeliveredEmailNotification;
use App\Notifications\order\OnTheWayEmailNotification;
use App\Notifications\order\OrderCanceledEmailNotification;
use App\Notifications\order\ReadyToBeShippedEmailNotification;
use App\Notifications\order\ReshippedEmailNotification;
use App\Services\ActivityLogService;
use App\Services\NotificationService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class OrderEventListener
{
    protected ActivityLogService $activityLogService;
    protected NotificationService $notificationService;

    public function __construct(ActivityLogService $activityLogService, NotificationService $notificationService)
    {
        $this->activityLogService = $activityLogService;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Order "created" event.
     */
    public function handleCreated(OrderCreated $event): void
    {
        $order = $event->order;

       /* $this->activityLogService->log(
            'order_created',
            'Order created successfully',
            $order->id,
            Order::class,
            auth()->user()->id,
            get_class(auth()->user()),
            Null,
            OrderStatus::PENDING,
            null,
            Null,
            null,
            null
        );
       */
    }

    /**
     * Handle the Order "updated" event.
     */
    public function handleUpdated(OrderUpdated $event): void
    {
        $order = $event->order;
        $user = $order->user; // Use relationship instead of querying again

        if ($order->isDirty('delivery_status')) {
            $this->handleDeliveryStatusChange($order, $user);
        }
    }

    /**
     * Handle delivery status changes and send appropriate notifications
     */
    private function handleDeliveryStatusChange(Order $order, User $user): void
    {
        $oldStatus = $order->getOriginal('delivery_status');
        $email_end_time = null;

        if ($order->delivery_status == 'on_the_way') {
            $email_end_time = Carbon::now()->addMinutes(env("DELIVERY_DALY_EMAIL_MINUTES", 2880));
        }

        // Log the status change
        $this->activityLogService->log(
            'order_status_changed',
            'Order delivery status changed',
            $order->id,
            Order::class,
            auth()->user()->id,
            get_class(auth()->user()),
            $oldStatus,
            $order->delivery_status,
            null,
            $email_end_time,
            null,
            null
        );

        // Send notification to user
        $this->notificationService->sendNotification(
            $user,
            Order::class,
            $order->id,
            'Order Status Updated',
            'The delivery status of your order has been updated from ' . OrderStatus::getLabel($oldStatus) . ' to ' . OrderStatus::getLabel($order->delivery_status) . '.',
            NotificationType::ORDER->value,
            'high',
            null,
            null
        );

        // Send appropriate email based on status
        $this->sendStatusEmail($order, $user);
    }

    /**
     * Send appropriate email based on order status
     */
    private function sendStatusEmail(Order $order, User $user): void
    {
        $array = [
            'order' => $order,
            'user_name' => $user->name
        ];

        try {
            switch ($order->delivery_status) {
                case 'picked_up':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' is Ready to Be Shipped! ';
                    $user->notify(new ReadyToBeShippedEmailNotification($array));
                    break;
                case 'reshipped':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . '  Has Been Resent – New Tracking Details ';
                    $user->notify(new ReshippedEmailNotification($array));
                    break;
                case 'on_the_way':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' is On Its Way! ';
                    $user->notify(new OnTheWayEmailNotification($array));
                    break;
                case 'delivered':
                    $array['subject'] = translate('Your Order') . ' - ' . $order->code . ' ' . ' Has Been Delivered! ';
                    $order->review_email = Carbon::now()->addMinutes(env("REVIEW_EMAIL_MINUTES", 4320));
                    $order->save();
                    $user->notify(new DeliveredEmailNotification($array));
                    break;
                case 'cancelled':
                    $array['subject'] = translate('Update on Your Order ') . ' - ' . $order->code . ' ' . ' Canceled';
                    $cancel_reason_id = OrderDetail::where('order_id', $order->id)->first()->cancel_reason;
                    $array['cancel_reason'] = CancelReason::where('id', $cancel_reason_id)->first()->reason_code;
                    $array['cancel_by'] = 'Admin';
                    $user->notify(new OrderCanceledEmailNotification($array));
                    break;
            }

            $this->queueEmailWorker();
        } catch (\Exception $e) {
            Log::channel('email_logs')->error('Error sending email for status ' . $order->delivery_status . ': ' . $e->getMessage());
        }
    }

    /**
     * Queue email worker to process emails
     */
    private function queueEmailWorker(): void
    {
        $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
        exec($command);
    }
}
