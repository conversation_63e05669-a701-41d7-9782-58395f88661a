<?php

namespace App\Http\Resources\V3\Notifications;

use Illuminate\Http\Resources\Json\ResourceCollection;

class NotificationsCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'notifications' => $this->collection->map(function ($notification) {
                return [
                    'id' => (string) $notification->id,
                    'type' => $notification->type,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'date' => $notification->created_at->toIso8601String(),
                    'read' => (bool) $notification->read,
                    'priority' => $notification->priority,
                    'link' => $notification->link,
                    'linkText' => $notification->link_text,
                ];
            }),
            'meta' => [
                'current_page' => $this->currentPage(),
                'from' => $this->firstItem(),
                'last_page' => $this->lastPage(),
                'per_page' => $this->perPage(),
                'to' => $this->lastItem(),
                'total' => $this->total()
            ]
        ];
    }
}
