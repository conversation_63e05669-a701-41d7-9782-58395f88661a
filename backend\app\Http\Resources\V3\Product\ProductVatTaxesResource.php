<?php

namespace App\Http\Resources\V3\Product;

use App\Models\Tax;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductVatTaxesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($data) {
            return [
                'id'=> $data->id,
                'tax_id' => $data->tax_id,
                'tax_name' => optional(Tax::find($data->tax_id))->name,
                'tax' => $data->tax,
                'tax_type' => $data->tax_type
            ];
        });
    }
}
