<?php

namespace App\Http\Resources\V3\Orders;

use App\Enums\OrderStatus;
use App\Models\Coupon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class OrderResource extends JsonResource
{
    /**
     * Flag to indicate if we should include returnable flags
     *
     * @var bool
     */
    protected $includeReturnableFlags = false;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     * @param  bool  $includeReturnableFlags
     * @return void
     */
    public function __construct($resource, $includeReturnableFlags = false)
    {
        parent::__construct($resource);
        $this->includeReturnableFlags = $includeReturnableFlags;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        
        $couponDetails = [];
        if ($this->coupon_discount > 0) {
            $coupon = Coupon::find($this->coupon_id);
            if ($coupon) {
                $couponDetails['code'] = $coupon->code;
                $couponDetails['discount'] = $this->coupon_discount;
            }
        }
        $shippingAddress = json_decode($this->shipping_address);
        // return parent::toArray($request);
        return [
            'id' => (string) $this->code,
            'orderNumber' => (string) $this->code,
            'number' => (string) $this->code,
            'date' => $this->created_at->toDateString(),
            'customerName' => (string) optional($this->user)->name ?? 'Guest',
            'customerEmail' => (string) optional($this->user)->email ?? '',
            'customer' => [
                'name' => (string) optional($this->user)->name ?? Null,
                'email' => (string) optional($this->user)->email ?? Null
            ],
            'orderStatus' => OrderStatus::getLabel($this->delivery_status),
            'paymentStatus' => $this->payment_status,
            'pricing' => [
                'subtotal' => (float) $this->orderDetails->sum('price'),
                'shipping' => (float) $this->orderDetails->sum('shipping_cost'),
                'tax' => (float) $this->orderDetails->sum('tax'),
                'discount' => (float) $this->coupon_discount,
                'total' => (float) $this->grand_total,
            ],
            'payment' => $this->getReadablePaymentMethodDetails($this->payment_details, $this->payment_status),
            'paymentMethod' => $this->getReadablePaymentMethod($this->payment_details),
            'shipping' => [
                'method' => "Standard Shipping",
                'tracking_number' => $this->carrier_tracking_code,
                'carrier' => optional($this->carrier)->name ?? 'Standard Carrier',
                'estimated_delivery' => $this->estimated_delivery_date ? $this->estimated_delivery_date : null,
                'tracking_link' => $this->tracking_link,
                'formatted_tracking' => $this->carrier_tracking_code 
                    ? (optional($this->carrier)->name ?? 'Carrier') . ' - ' . $this->carrier_tracking_code
                    : null,
                'delivery_status' => OrderStatus::getLabel($this->delivery_status),
                'shipped_at' => $this->shipped_at ? $this->shipped_at->toIso8601String() : null,
                'delivered_at' => $this->delivered_at ? $this->delivered_at->toIso8601String() : null,
            ],
            'total' => (float) $this->grand_total,
            'shipping_cost' => (float) $this->orderDetails->sum('shipping_cost'),
            'tax' => (float) $this->orderDetails->sum('tax'),
            'discount' => (float) $this->coupon_discount,
            'items' => $this->orderDetails->map(function ($detail) {
                // Check if user has already reviewed this product
                // Note: reviews are already filtered for current user in the controller
                $userReview = $detail->product->reviews->first();
                $hasReviewed = $userReview !== null;
                
                $item = [
                    'id' => (string) $detail->id,
                    'order_details_id' => $detail->id,//(string)  Crypt::encryptString($detail->id),
                    'productId' => (string) $detail->product->id,
                    'slug' => (string) $detail->product->slug,
                    'variation' => (string) $detail->variation,
                    'title' => $detail->product->name,
                    'thumbnail' => $detail->product->thumbnail_img ? uploaded_asset($detail->product->thumbnail_img) : "",
                    'image' => $detail->product->thumbnail_img ? uploaded_asset($detail->product->thumbnail_img) : "",
                    'quantity' => $detail->quantity,
                    'unit_price' => (string) $detail->unit_price,
                    'price' => (float) $detail->unit_price,
                    'commission' => '',
                    'sku' => (string) product_sku_by_product_id($detail->product, $detail->variation) ?? Null,
                    'isReturnable' => (boolean) optional($detail->product)->refundable,
                    'isReviewed' => $hasReviewed,
                    'reviewStatus' => $hasReviewed ? 'reviewed' : 'not_reviewed',
                    'review' => $hasReviewed && $userReview ? [
                        'id' => $userReview->id,
                        'rating' => $userReview->rating,
                        'comment' => $userReview->comment,
                        'title' => $userReview->review_title,
                        'photos' => $userReview->photos ?? [],
                        'videos' => $userReview->review_videos ?? [],
                        'tags' => $userReview->tags ?? [],
                        'created_at' => $userReview->created_at->toIso8601String(),
                    ] : null,
                ];

                // Add returnable flag if requested
                if ($this->includeReturnableFlags) {
                    // Check if the product is refundable
                    $isReturnable = $detail->product->refundable && $detail->delivery_status === 'delivered';

                    // Add returnable information to the item
                    $item['isReturnable'] = $isReturnable;
                    $item['returnReason'] = $isReturnable ? null : 'Product is not eligible for return';
                    $item['returnableQuantity'] = $isReturnable ? $detail->quantity : 0;
                }

                return $item;
            }),
            'shippingAddress' => [
                'name' => (string) optional($shippingAddress)->name ?? Null,
                'email' => (string) optional($shippingAddress)->email ?? Null,
                'phone' => (string) optional($shippingAddress)->phone ?? Null,
                'address' => (string) optional($shippingAddress)->street_address ?? Null,
                'city' => (string) optional($shippingAddress)->city ?? Null,
                'state' => (string) optional($shippingAddress)->state ?? Null,
                'country' => (string) optional($shippingAddress)->country ?? Null,
                'postalCode' => (string) optional($shippingAddress)->postal_code ?? Null,
            ],
            'billingAddress' => $this->getReadableBillingAddress($this->payment_details),

            'commission' => '',
            'commissionStatus' => '',
            'commissionPayout' => [
                "estimatedDate" => "",
                "method" => ""
            ],

            'shippingMethod' => optional($this->carrier)->name ?? "Standard Shipping",
            'trackingNumber' => $this->carrier_tracking_code,
            'trackingUrl' => $this->tracking_link,
            'referralSource' => '',
            'coupon' => $couponDetails,

            'placedAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String(),
            'timeline' => $this->getOrderTimeline($this->activityLogs),

            'shippingInfo' => [
                'tracking_number' => $this->carrier_tracking_code,
                'carrier' => optional($this->carrier)->name ?? null,
                'estimated_delivery' => $this->estimated_delivery_date ? $this->estimated_delivery_date : null,
            ],
        ];
    }
    private function getReadablePaymentMethodDetails($payment_details, $status)
    {
        try {
            $details = json_decode($payment_details, true);

            // Handle COD or missing payment details
            if (!$details || !isset($details['card_details']) || $details['payment_type'] !== 'stripe') {
                return [
                    'method' => 'Cash on Delivery',
                    'last4' => null,
                    'brand' => null,
                    'status' => ucfirst($status),
                    'formatted_display' => 'Cash on Delivery',
                    'payment_type' => 'cod'
                ];
            }

            $card = json_decode($details['card_details'], true);

            if (!$card || !isset($card['brand']) || !isset($card['last4'])) {
                return [
                    'method' => 'Stripe',
                    'last4' => null,
                    'brand' => null,
                    'status' => ucfirst($status),
                    'formatted_display' => 'Stripe Payment',
                    'payment_type' => 'stripe'
                ];
            }

            $brand = ucfirst($card['brand']);
            $last4 = $card['last4'];
            
            return [
                'method' => $brand,
                'last4' => $last4,
                'brand' => $brand,
                'status' => ucfirst($status),
                'formatted_display' => $brand . ' ending in ' . $last4,
                'card_type' => $brand,
                'payment_type' => 'stripe',
                'stripe_card_id' => $card['stripe_card_id'] ?? null,
                'expiry_month' => $card['exp_month'] ?? null,
                'expiry_year' => $card['exp_year'] ?? null,
                'formatted_expiry' => isset($card['exp_month'], $card['exp_year']) 
                    ? sprintf('%02d/%s', $card['exp_month'], substr($card['exp_year'], -2))
                    : null
            ];

        } catch (\Exception $e) {
            Log::error('Error parsing payment details: ' . $e->getMessage());
            return [
                'method' => 'Unknown',
                'last4' => null,
                'brand' => null,
                'status' => ucfirst($status),
                'formatted_display' => 'Payment method not available',
                'payment_type' => 'unknown'
            ];
        }
    }
    private function getReadablePaymentMethod($payment_details)
    {
        try {
            $details = json_decode($payment_details, true);

            if (!$details || !isset($details['card_details']) || $details['payment_type'] !== 'stripe') {
                return 'Cash on Delivery';
            }

            $card = json_decode($details['card_details'], true);

            if (!$card || !isset($card['brand']) || !isset($card['last4'])) {
                return 'Stripe Payment';
            }

            return 'Card (' . ucfirst($card['brand']) . ' ending in ' . $card['last4'] . ')';

        } catch (\Exception $e) {
            Log::error('Error parsing payment method: ' . $e->getMessage());
            return 'Payment method not available';
        }
    }
    private function getReadableBillingAddress($payment_details)
    {
        try {
            $details = json_decode($payment_details, true);

            if (!$details || !isset($details['card_details']) || $details['payment_type'] !== 'stripe') {
                return [
                    'name' => null,
                    'email' => null,
                    'phone' => null,
                    'address' => null,
                    'city' => null,
                    'state' => null,
                    'country' => null,
                    'postalCode' => null,
                ];
            }

            $card = json_decode($details['card_details'], true);

            if (!$card || !isset($card['billing_details'])) {
                return [
                    'name' => null,
                    'email' => null,
                    'phone' => null,
                    'address' => null,
                    'city' => null,
                    'state' => null,
                    'country' => null,
                    'postalCode' => null,
                ];
            }

            $billingDetails = $card['billing_details'];
            $address = $billingDetails['address'] ?? [];

            return [
                'name' => $billingDetails['name'] ?? null,
                'email' => $billingDetails['email'] ?? null,
                'phone' => $billingDetails['phone'] ?? null,
                'address' => $address['line1'] ?? null,
                'address2' => $address['line2'] ?? null,
                'city' => $address['city'] ?? null,
                'state' => $address['state'] ?? null,
                'country' => $address['country'] ?? null,
                'postalCode' => $address['postal_code'] ?? null,
                'formatted_address' => $this->formatBillingAddress($billingDetails)
            ];

        } catch (\Exception $e) {
            Log::error('Error parsing billing address: ' . $e->getMessage());
            return [
                'name' => null,
                'email' => null,
                'phone' => null,
                'address' => null,
                'city' => null,
                'state' => null,
                'country' => null,
                'postalCode' => null,
            ];
        }
    }

    /**
     * Format billing address for display
     */
    private function formatBillingAddress($billingDetails)
    {
        try {
            $address = $billingDetails['address'] ?? [];
            $parts = [];

            if (!empty($billingDetails['name'])) {
                $parts[] = $billingDetails['name'];
            }

            if (!empty($address['line1'])) {
                $parts[] = $address['line1'];
            }

            if (!empty($address['line2'])) {
                $parts[] = $address['line2'];
            }

            $cityStateZip = [];
            if (!empty($address['city'])) {
                $cityStateZip[] = $address['city'];
            }
            if (!empty($address['state'])) {
                $cityStateZip[] = $address['state'];
            }
            if (!empty($address['postal_code'])) {
                $cityStateZip[] = $address['postal_code'];
            }
            
            if (!empty($cityStateZip)) {
                $parts[] = implode(', ', $cityStateZip);
            }

            if (!empty($address['country'])) {
                $parts[] = $address['country'];
            }

            return implode("\n", array_filter($parts));

        } catch (\Exception $e) {
            return '';
        }
    }
    private function getOrderTimeline($activityLogs)
    {
        return $activityLogs->map(function ($log) {
            return [
                'status' => OrderStatus::getLabel($log->new_status),
                'timestamp' => $log->created_at->toIso8601String(),
                'description' => $log->description,
            ];
        });
    }
}