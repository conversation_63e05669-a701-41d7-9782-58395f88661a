<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Tag;
use App\Models\ProductTag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ApiTagsController extends Controller
{
    /**
     * Get all tags with optional pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 50);
        $page = $request->input('page', 1);
        $search = $request->input('search', '');
        
        $query = Tag::query();
        
        if ($search) {
            $query->where('name', 'like', "%{$search}%");
        }
        
        $totalTags = $query->count();
        $tags = $query->orderBy('name')
            ->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Tags retrieved successfully',
            'data' => [
                'tags' => $tags,
                'pagination' => [
                    'total' => $totalTags,
                    'per_page' => (int) $limit,
                    'current_page' => (int) $page,
                    'last_page' => ceil($totalTags / $limit)
                ]
            ]
        ]);
    }
    
    /**
     * Get popular tags
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPopularTags(Request $request)
    {
        $limit = $request->input('limit', 15);
        
        $popularTags = Tag::select('tags.*', DB::raw('COUNT(product_tags.product_id) as count'))
            ->join('product_tags', 'tags.id', '=', 'product_tags.tag_id')
            ->groupBy('tags.id')
            ->orderBy('count', 'desc')
            ->limit($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Popular tags retrieved successfully',
            'data' => [
                'tags' => $popularTags
            ]
        ]);
    }
    
    /**
     * Get trending tags (most used in recent time period)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrendingTags(Request $request)
    {
        $limit = $request->input('limit', 15);
        $days = $request->input('days', 30); // Default to last 30 days
        
        $trendingTags = Tag::select('tags.*', DB::raw('COUNT(product_tags.product_id) as count'))
            ->join('product_tags', 'tags.id', '=', 'product_tags.tag_id')
            ->join('products', 'products.id', '=', 'product_tags.product_id')
            ->where('products.created_at', '>=', now()->subDays($days))
            ->groupBy('tags.id')
            ->orderBy('count', 'desc')
            ->limit($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Trending tags retrieved successfully',
            'data' => [
                'tags' => $trendingTags
            ]
        ]);
    }
    
    /**
     * Search tags
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchTags(Request $request)
    {
        $query = $request->input('query', '');
        $limit = $request->input('limit', 10);
        
        if (empty($query) || strlen($query) < 2) {
            return response()->json([
                'success' => true,
                'message' => 'Please provide a search query with at least 2 characters',
                'data' => [
                    'tags' => []
                ]
            ]);
        }
        
        $tags = Tag::where('name', 'like', "%{$query}%")
            ->orderBy('name')
            ->limit($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Tags search results',
            'data' => [
                'tags' => $tags
            ]
        ]);
    }
    
    /**
     * Get tag by slug
     *
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTagBySlug($slug)
    {
        $tag = Tag::where('slug', $slug)->first();
        
        if (!$tag) {
            return response()->json([
                'success' => false,
                'message' => 'Tag not found'
            ], 404);
        }
        
        // Get product count
        $productCount = ProductTag::where('tag_id', $tag->id)->count();
        
        $tagData = $tag->toArray();
        $tagData['product_count'] = $productCount;
        
        return response()->json([
            'success' => true,
            'message' => 'Tag retrieved successfully',
            'data' => [
                'tag' => $tagData
            ]
        ]);
    }
    
    /**
     * Get related tags for a given tag
     *
     * @param string $slug
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRelatedTags($slug, Request $request)
    {
        $limit = $request->input('limit', 10);
        
        $tag = Tag::where('slug', $slug)->first();
        
        if (!$tag) {
            return response()->json([
                'success' => false,
                'message' => 'Tag not found'
            ], 404);
        }
        
        // Get products with this tag
        $productIds = ProductTag::where('tag_id', $tag->id)
            ->pluck('product_id')
            ->toArray();
        
        if (empty($productIds)) {
            return response()->json([
                'success' => true,
                'message' => 'No related tags found',
                'data' => [
                    'tags' => []
                ]
            ]);
        }
        
        // Find tags that are used with the same products
        $relatedTags = Tag::select('tags.*', DB::raw('COUNT(product_tags.product_id) as count'))
            ->join('product_tags', 'tags.id', '=', 'product_tags.tag_id')
            ->whereIn('product_tags.product_id', $productIds)
            ->where('tags.id', '!=', $tag->id)
            ->groupBy('tags.id')
            ->orderBy('count', 'desc')
            ->limit($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Related tags retrieved successfully',
            'data' => [
                'tags' => $relatedTags
            ]
        ]);
    }
    
    /**
     * Get products by tag
     *
     * @param string $slug
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductsByTag($slug, Request $request)
    {
        $limit = $request->input('limit', 20);
        $page = $request->input('page', 1);
        $sortBy = $request->input('sort_by', 'newest');
        
        $tag = Tag::where('slug', $slug)->first();
        
        if (!$tag) {
            return response()->json([
                'success' => false,
                'message' => 'Tag not found'
            ], 404);
        }
        
        // Get product IDs with this tag
        $query = Product::whereHas('tags', function($q) use ($tag) {
            $q->where('tags.id', $tag->id);
        })->where('published', 1);
        
        // Apply sorting
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('unit_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('unit_price', 'desc');
                break;
            case 'popular':
                $query->orderBy('num_of_sale', 'desc');
                break;
            case 'rating':
                $query->orderBy('rating', 'desc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }
        
        $totalProducts = $query->count();
        
        $products = $query->skip(($page - 1) * $limit)
            ->take($limit)
            ->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Products by tag retrieved successfully',
            'data' => [
                'tag' => $tag,
                'products' => $products,
                'pagination' => [
                    'total' => $totalProducts,
                    'per_page' => (int) $limit,
                    'current_page' => (int) $page,
                    'last_page' => ceil($totalProducts / $limit)
                ]
            ]
        ]);
    }
} 