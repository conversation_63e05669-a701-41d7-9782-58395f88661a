<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

class ApiAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string[]  ...$guards
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next, ...$guards)
    {
        try {
            // Check for JWT token in Authorization header first, then in cookie
            $token = null;
            
            if ($request->hasHeader('Authorization')) {
                $authHeader = $request->header('Authorization');
                if (str_starts_with($authHeader, 'Bearer ')) {
                    $token = substr($authHeader, 7);
                }
            } elseif ($jwt = $request->cookie('jwt')) {
                $token = $jwt;
                // Set header from cookie for Sanctum
                $request->headers->set('Authorization', 'Bearer ' . $token);
            }
            
            if (!$token) {
                throw new AuthenticationException('No authentication token provided');
            }
            
            // Use Sanctum to authenticate the token
            $personalAccessToken = PersonalAccessToken::findToken($token);
            
            if (!$personalAccessToken) {
                throw new AuthenticationException('Invalid token');
            }
            
            $user = $personalAccessToken->tokenable;
            
            if (!$user) {
                throw new AuthenticationException('Token user not found');
            }
            
            // Check if user is banned
            if ($user->banned) {
                throw new AuthenticationException('Account is banned');
            }
            
            // Set the user on both guards to ensure compatibility
            Auth::guard('sanctum')->setUser($user);
            Auth::setUser($user); // Set on default guard as well
            
            // Also set the current access token for the user
            $user->withAccessToken($personalAccessToken);
            
        } catch (AuthenticationException $e) {
            // For API requests, return JSON response instead of redirect
            return response()->json([
                'success' => false,
                'status' => 'error',
                'message' => 'Unauthenticated',
                'error' => [
                    'code' => 'UNAUTHENTICATED',
                    'message' => 'Authentication required to access this resource. Please login first.',
                    'details' => $e->getMessage()
                ]
            ], 401);
        }

        return $next($request);
    }

    /**
     * Get the path the user should be redirected to when they are not authenticated.
     * For API middleware, this should never be called since we handle auth failures above.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        // Always return null for API requests to prevent redirects
        return null;
    }
}
