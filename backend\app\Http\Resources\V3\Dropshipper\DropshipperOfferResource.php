<?php

namespace App\Http\Resources\V3\Dropshipper;

use Illuminate\Http\Resources\Json\JsonResource;

class DropshipperOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->offer_id,
            'title' => $this->title,
            'description' => $this->description,
            'discountPercentage' => $this->discount_percentage,
            'discountAmount' => $this->discount_amount,
            'minOrderValue' => $this->min_order_value,
            'promoCode' => $this->promo_code,
            'expirationDate' => $this->expiration_date->toIso8601String(),
            'startDate' => $this->start_date->toIso8601String(),
            'isExclusive' => $this->is_exclusive,
            'isSeasonal' => $this->is_seasonal,
            'isPersonalized' => $this->is_personalized,
            'categories' => $this->categories,
            'products' => $this->products,
            'usageLimit' => $this->usage_limit,
            'usedCount' => $this->used_count,
            'isRedeemed' => $this->is_redeemed,
            'isExpired' => $this->isActive() ? false : true,
            'priority' => $this->priority,
            'tags' => $this->tags,
            'termsAndConditions' => $this->terms_and_conditions,
            'isdropshipperOnly' => $this->is_dropshipper_only,
            'bulkDiscountThresholds' => $this->bulk_discount_thresholds,
            'savingsAmount' => $this->savings_amount,
            'expirationTime' => $this->expiration_time,
            'timeRemaining' => $this->time_remaining,
            'createdAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String(),
        ];
    }
} 