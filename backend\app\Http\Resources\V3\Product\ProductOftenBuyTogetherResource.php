<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductOftenBuyTogetherResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $price = product_price_object($this);
        $dropshipper_price =product_dropshipper_price_object($this) ;
        $wholesales_price =product_wholesales_price_object($this) ;

        return [
            'id' => (string)$this->slug,
            'name' => $this->getTranslation('name'),
            'category' => product_category_object($this),
            'brand' => product_brand_object($this),
           'image' => $this->thumbnail_img ? uploaded_asset($this->thumbnail_img) : "",
            'price' => $price,
            'dropshipper_price' => $dropshipper_price,
            'wholesales_price' => $wholesales_price,
        ];
    }
}
