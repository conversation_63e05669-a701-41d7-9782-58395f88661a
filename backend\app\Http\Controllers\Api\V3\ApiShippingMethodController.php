<?php

namespace App\Http\Controllers\Api\V3;


use App\Http\Resources\V3\ShippingMethod\ShippingCostResource;
use App\Http\Resources\V3\ShippingMethod\ShippingMethodResource;
use App\Services\ShippingMethod\ApiShippingMethodService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Address;

class ApiShippingMethodController extends ApiResponse
{
    protected $shippingMethodService;

    /**
     * Create a new controller instance.
     *
     * @param ApiShippingMethodService $shippingMethodService
     * @return void
     */
    public function __construct(ApiShippingMethodService $shippingMethodService)
    {
        $this->shippingMethodService = $shippingMethodService;
    }

    /**
     * Get available shipping methods
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableShippingMethods(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'addressId' => 'nullable|integer|exists:addresses,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Get available shipping methods
            $shippingMethods = $this->shippingMethodService->getAvailableShippingMethods(
                $user,
                $request->input('addressId')
            );

            // Return the response
            return $this->success(
                ShippingMethodResource::collection($shippingMethods)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get shipping methods',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Calculate shipping cost for a specific shipping method
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateShippingCost(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'shippingMethodId' => 'required|string|exists:shipping_methods,id',
                'addressId' => 'nullable|integer|exists:addresses,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Calculate shipping cost
            $shippingCost = $this->shippingMethodService->calculateShippingCost(
                $user,
                $request->input('shippingMethodId'),
                $request->input('addressId')
            );

            // Return the response
            return $this->success(
                new ShippingCostResource($shippingCost)
            );
        } catch (\Exception $e) {
            // Handle specific error cases
            if (in_array($e->getMessage(), ['Shipping method not found', 'Shipping address not found', 'Cart is empty'])) {
                return $this->error(
                    'INVALID_REQUEST',
                    $e->getMessage(),
                    null,
                    400
                );
            }

            // Handle general errors
            return $this->error(
                'SERVER_ERROR',
                'Failed to calculate shipping cost',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Validate a shipping address
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateShippingAddress(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'addressId' => 'required|integer|exists:addresses,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the address
            $address = Address::find($request->input('addressId'));
            
            if (!$address) {
                return $this->error(
                    'NOT_FOUND',
                    'Address not found',
                    null,
                    404
                );
            }
            
            // Get the authenticated user and verify ownership
            $user = Auth::user();
            
            if ($address->user_id !== $user->id) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You do not have permission to access this address',
                    null,
                    403
                );
            }

            // Validate the address
            $validationResult = $this->shippingMethodService->validateShippingAddress($address);

            // Return the response
            return $this->success($validationResult);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to validate shipping address',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Get tracking information for an order
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function getOrderTracking(Request $request, string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();
            
            // Get tracking information
            $trackingInfo = $this->shippingMethodService->getOrderTracking($orderId);

            // Return the response
            return $this->success($trackingInfo);
        } catch (\Exception $e) {
            // Handle not found errors
            if ($e->getMessage() === 'Order not found') {
                return $this->error(
                    'NOT_FOUND',
                    'Order not found',
                    null,
                    404
                );
            }
            
            return $this->error(
                'SERVER_ERROR',
                'Failed to get tracking information',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Get shipping rates for an address
     *
     * @param Request $request
     * @param string $addressId
     * @return JsonResponse
     */
    public function getShippingRates(Request $request, string $addressId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();
            
            // Get shipping rates
            $rates = $this->shippingMethodService->getShippingRates($addressId);

            // Return the response
            return $this->success($rates);
        } catch (\Exception $e) {
            // Handle not found errors
            if ($e->getMessage() === 'Address not found') {
                return $this->error(
                    'NOT_FOUND',
                    'Address not found',
                    null,
                    404
                );
            }
            
            return $this->error(
                'SERVER_ERROR',
                'Failed to get shipping rates',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Check if delivery is available for a specific address and items
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDeliveryAvailability(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'addressId' => 'required|integer|exists:addresses,id',
                'items' => 'required|array',
                'items.*.productId' => 'required|string',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.variantId' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Check delivery availability
            $availability = $this->shippingMethodService->checkDeliveryAvailability(
                $request->input('addressId'),
                $request->input('items')
            );

            // Return the response
            return $this->success($availability);
        } catch (\Exception $e) {
            // Handle specific error cases
            if (in_array($e->getMessage(), ['Address not found', 'No items provided'])) {
                return $this->error(
                    'INVALID_REQUEST',
                    $e->getMessage(),
                    null,
                    400
                );
            }

            // Handle general errors
            return $this->error(
                'SERVER_ERROR',
                'Failed to check delivery availability',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Select a shipping method for checkout
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function selectShippingMethod(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'shippingMethodId' => 'required|string|exists:shipping_methods,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Select the shipping method
            $result = $this->shippingMethodService->selectShippingMethod(
                $user,
                $request->input('shippingMethodId')
            );

            // Return the response
            return $this->success([
                'selected' => $result,
                'message' => 'Shipping method selected successfully'
            ]);
        } catch (\Exception $e) {
            // Handle specific error cases
            if ($e->getMessage() === 'Shipping method not found or inactive') {
                return $this->error(
                    'INVALID_REQUEST',
                    $e->getMessage(),
                    null,
                    400
                );
            }

            // Handle general errors
            return $this->error(
                'SERVER_ERROR',
                'Failed to select shipping method',
                $e->getMessage(),
                500
            );
        }
    }
}
