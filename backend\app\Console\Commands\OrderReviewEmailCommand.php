<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
class OrderReviewEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'OrderReviewEmailCommand';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $endRange = Carbon::now();
        $orders = Order::where('delivery_status', 'delivered')
            ->whereNotNull('review_email')
            ->where('review_email', '<=', $endRange)
            ->get();
        if ($orders) {
            foreach ($orders as $order) {
                try {
                    $order->review_email = null;
                    $order->followup_email =  Carbon::now()->addMinutes(env("FOLLOWUP_EMAIL_MINUTES",4320));
                    $order->save();
                    $array = array();
                    $array['order'] = $order;
                    $array['user_name'] = $order->user->name;
                    $array['subject'] = translate('Just a Little Nudge - Secure Your Order Soon');
                    $order->user->notify(new OrderReviewEmailNotification($array));
                } catch (\Exception $e) {
                    Log::channel('command_logs')->error('Error occurred while sending  Order Delivery Delay Notification email in OrderDeliveryDelay command : ' . $e->getMessage());
                }
            }
        }
        return Command::SUCCESS;
    }
}
