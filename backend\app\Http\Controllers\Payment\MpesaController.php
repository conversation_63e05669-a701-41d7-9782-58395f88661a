<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use App\Models\CustomerPackagePayment;
use App\Models\SellerPackagePayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;

class MpesaController extends Controller
{
    private $consumer_key;
    private $consumer_secret;
    private $shortcode;
    private $passkey;
    private $environment;
    private $callback_url;

    public function __construct()
    {
        $this->consumer_key = get_setting('mpesa_consumer_key');
        $this->consumer_secret = get_setting('mpesa_consumer_secret');
        $this->shortcode = get_setting('mpesa_shortcode');
        $this->passkey = get_setting('mpesa_passkey');
        $this->environment = get_setting('mpesa_environment') ?? 'sandbox';
        $this->callback_url = route('mpesa.pay');
    }

    /**
     * Initiate M-Pesa payment
     */
    public function mpesa_pay(Request $request)
    {
        try {
            $amount = $request->amount ?? Session::get('payment_data.amount') ?? 0;
            $phone = $request->phone;
            $account_reference = $request->account_reference ?? 'ORDER';
            $transaction_desc = $request->transaction_desc ?? 'Payment';

            if (!$phone || !$amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phone number and amount are required'
                ], 400);
            }

            // Format phone number
            $phone = $this->formatPhoneNumber($phone);
            
            // Get access token
            $access_token = $this->generateAccessToken();
            
            if (!$access_token) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to get M-Pesa access token'
                ], 500);
            }

            // Generate timestamp and password
            $timestamp = date('YmdHis');
            $password = base64_encode($this->shortcode . $this->passkey . $timestamp);

            // Prepare STK Push request
            $url = $this->getApiUrl() . '/mpesa/stkpush/v1/processrequest';
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type' => 'application/json',
            ])->post($url, [
                'BusinessShortCode' => $this->shortcode,
                'Password' => $password,
                'Timestamp' => $timestamp,
                'TransactionType' => 'CustomerPayBillOnline',
                'Amount' => $amount,
                'PartyA' => $phone,
                'PartyB' => $this->shortcode,
                'PhoneNumber' => $phone,
                'CallBackURL' => $this->callback_url,
                'AccountReference' => $account_reference,
                'TransactionDesc' => $transaction_desc
            ]);

            $response_data = $response->json();

            if ($response->successful() && isset($response_data['ResponseCode']) && $response_data['ResponseCode'] == '0') {
                return response()->json([
                    'success' => true,
                    'message' => 'STK Push sent successfully',
                    'checkout_request_id' => $response_data['CheckoutRequestID']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response_data['errorMessage'] ?? 'STK Push failed'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('M-Pesa payment error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed'
            ], 500);
        }
    }

    /**
     * Handle payment completion callback
     */
    public function payment_complete(Request $request)
    {
        try {
            $callback_data = $request->all();
            Log::info('M-Pesa callback received: ', $callback_data);

            if (isset($callback_data['Body']['stkCallback'])) {
                $stk_callback = $callback_data['Body']['stkCallback'];
                $result_code = $stk_callback['ResultCode'];
                $checkout_request_id = $stk_callback['CheckoutRequestID'];

                if ($result_code == 0) {
                    // Payment successful
                    $callback_metadata = $stk_callback['CallbackMetadata']['Item'];
                    $amount = null;
                    $mpesa_receipt_number = null;
                    $phone_number = null;

                    foreach ($callback_metadata as $item) {
                        switch ($item['Name']) {
                            case 'Amount':
                                $amount = $item['Value'];
                                break;
                            case 'MpesaReceiptNumber':
                                $mpesa_receipt_number = $item['Value'];
                                break;
                            case 'PhoneNumber':
                                $phone_number = $item['Value'];
                                break;
                        }
                    }

                    // Process the successful payment
                    $this->processSuccessfulPayment($checkout_request_id, $amount, $mpesa_receipt_number, $phone_number);

                    return response()->json(['success' => true]);
                } else {
                    // Payment failed
                    Log::warning('M-Pesa payment failed: ' . $stk_callback['ResultDesc']);
                    return response()->json(['success' => false, 'message' => $stk_callback['ResultDesc']]);
                }
            }

            return response()->json(['success' => false, 'message' => 'Invalid callback data']);

        } catch (\Exception $e) {
            Log::error('M-Pesa callback processing error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Callback processing failed']);
        }
    }

    /**
     * Validation endpoint
     */
    public function validation(Request $request)
    {
        return response()->json([
            'ResultCode' => 0,
            'ResultDesc' => 'Accept the service request successfully.'
        ]);
    }

    /**
     * Confirmation endpoint
     */
    public function confirmation(Request $request)
    {
        try {
            $confirmation_data = $request->all();
            Log::info('M-Pesa confirmation received: ', $confirmation_data);

            // Process confirmation data
            // This is where you would update your database with the transaction details

            return response()->json([
                'ResultCode' => 0,
                'ResultDesc' => 'Accept the service request successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('M-Pesa confirmation error: ' . $e->getMessage());
            return response()->json([
                'ResultCode' => 1,
                'ResultDesc' => 'Failed to process confirmation.'
            ]);
        }
    }

    /**
     * Results endpoint
     */
    public function results(Request $request)
    {
        try {
            $results_data = $request->all();
            Log::info('M-Pesa results received: ', $results_data);

            return response()->json([
                'ResultCode' => 0,
                'ResultDesc' => 'Accept the service request successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('M-Pesa results error: ' . $e->getMessage());
            return response()->json([
                'ResultCode' => 1,
                'ResultDesc' => 'Failed to process results.'
            ]);
        }
    }

    /**
     * Register URLs
     */
    public function register(Request $request)
    {
        try {
            $access_token = $this->generateAccessToken();
            
            if (!$access_token) {
                return response()->json(['success' => false, 'message' => 'Failed to get access token']);
            }

            $url = $this->getApiUrl() . '/mpesa/c2b/v1/registerurl';
            
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $access_token,
                'Content-Type' => 'application/json',
            ])->post($url, [
                'ShortCode' => $this->shortcode,
                'ResponseType' => 'Completed',
                'ConfirmationURL' => route('lnmo.confirm'),
                'ValidationURL' => route('lnmo.validate')
            ]);

            return response()->json($response->json());

        } catch (\Exception $e) {
            Log::error('M-Pesa URL registration error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'URL registration failed']);
        }
    }

    /**
     * Handle timeout
     */
    public function timeout(Request $request)
    {
        Log::info('M-Pesa timeout: ', $request->all());
        
        return response()->json([
            'ResultCode' => 0,
            'ResultDesc' => 'Timeout processed successfully.'
        ]);
    }

    /**
     * Reconcile transactions
     */
    public function reconcile(Request $request)
    {
        try {
            // Implementation for transaction reconciliation
            Log::info('M-Pesa reconciliation: ', $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Reconciliation completed'
            ]);

        } catch (\Exception $e) {
            Log::error('M-Pesa reconciliation error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Reconciliation failed']);
        }
    }

    /**
     * Generate M-Pesa access token
     */
    private function generateAccessToken()
    {
        try {
            $url = $this->getApiUrl() . '/oauth/v1/generate?grant_type=client_credentials';
            $credentials = base64_encode($this->consumer_key . ':' . $this->consumer_secret);

            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . $credentials,
                'Content-Type' => 'application/json',
            ])->get($url);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('M-Pesa token generation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get API URL based on environment
     */
    private function getApiUrl()
    {
        return $this->environment === 'sandbox' 
            ? 'https://sandbox.safaricom.co.ke' 
            : 'https://api.safaricom.co.ke';
    }

    /**
     * Format phone number for M-Pesa
     */
    private function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/\D/', '', $phone);
        
        // Add country code if not present
        if (strlen($phone) == 9 && substr($phone, 0, 1) == '7') {
            $phone = '254' . $phone;
        } elseif (strlen($phone) == 10 && substr($phone, 0, 1) == '0') {
            $phone = '254' . substr($phone, 1);
        }
        
        return $phone;
    }

    /**
     * Process successful payment
     */
    private function processSuccessfulPayment($checkout_request_id, $amount, $receipt_number, $phone_number)
    {
        try {
            $payment_type = Session::get('payment_type');
            $payment_data = Session::get('payment_data');

            switch ($payment_type) {
                case 'order_payment':
                    $this->processOrderPayment($payment_data, $receipt_number);
                    break;
                case 'wallet_recharge':
                    $this->processWalletRecharge($payment_data, $receipt_number);
                    break;
                case 'seller_package_payment':
                    $this->processSellerPackagePayment($payment_data, $receipt_number);
                    break;
                case 'customer_package_payment':
                    $this->processCustomerPackagePayment($payment_data, $receipt_number);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage());
        }
    }

    /**
     * Process order payment
     */
    private function processOrderPayment($payment_data, $receipt_number)
    {
        // Implementation for order payment processing
        Log::info('Processing order payment via M-Pesa: ' . $receipt_number);
    }

    /**
     * Process wallet recharge
     */
    private function processWalletRecharge($payment_data, $receipt_number)
    {
        // Implementation for wallet recharge processing
        Log::info('Processing wallet recharge via M-Pesa: ' . $receipt_number);
    }

    /**
     * Process seller package payment
     */
    private function processSellerPackagePayment($payment_data, $receipt_number)
    {
        // Implementation for seller package payment processing
        Log::info('Processing seller package payment via M-Pesa: ' . $receipt_number);
    }

    /**
     * Process customer package payment
     */
    private function processCustomerPackagePayment($payment_data, $receipt_number)
    {
        // Implementation for customer package payment processing
        Log::info('Processing customer package payment via M-Pesa: ' . $receipt_number);
    }
} 