<?php

namespace App\Http\Resources\V3;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Upload;
use Illuminate\Support\Facades\Crypt;

class TicketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $ticketId = $this->id;
        
        // Get the last response timestamp
        $lastResponseAt = $this->updated_at;
        if ($this->ticketMessages && $this->ticketMessages->count() > 0) {
            $lastResponseAt = $this->ticketMessages->sortByDesc('created_at')->first()->created_at;
        }

        // Format status and priority
        $status = strtolower($this->status);
        $priority = strtolower($this->priority);

        // Get assigned support agent if available
        $assignedToId = null;
        $assignedToName = null;

        // Process messages
        $messages = [];
        if ($this->ticketMessages) {
            foreach ($this->ticketMessages as $message) {
                $messageData = [
                    'id' => $message->id,
                    'content' => $message->content,
                    'createdAt' => $message->created_at->toIso8601String(),
                    'sender' => [
                        'id' => $message->user->id,
                        'name' => $message->user->name,
                        'role' => $message->user_role,
                    ],
                    'attachments' => [],
                ];

                // Add attachments if any
                if ($message->attachments) {
                    foreach ($message->attachments as $attachment) {
                        $messageData['attachments'][] = [
                            'id' => $attachment->id,
                            'url' => $attachment->file_url,
                            'filename' => $attachment->file_name,
                            'filesize' => $attachment->file_size,
                        ];
                    }
                }

                $messages[] = $messageData;
            }
        }

        return [
            'id' => $ticketId,
            'subject' => $this->subject,
            'status' => $status,
            'priority' => $priority,
            'createdAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String(),
            'messages' => $messages,
            'category' => [
                'id' => $this->ticketCategory->id,
                'name' => $this->ticketCategory->name,
            ],
            'timeline' => [
                'createdAt' => $this->created_at->toIso8601String(),
                'updatedAt' => $this->updated_at->toIso8601String(),
                'closedAt' => $this->closed_at ? $this->closed_at->toIso8601String() : null,
                'reopenedAt' => $this->reopened_at ? $this->reopened_at->toIso8601String() : null,
            ],
        ];
    }
}
