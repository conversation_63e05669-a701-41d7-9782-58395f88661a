<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SearchController;
use App\Http\Resources\V2\ProductMiniCollection;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Search;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ApiSearchController extends ApiResponse
{
    public function search(Request $request){
        $keyword = $request->keyword;

        $products = Product::with(['stocks', 'product_translations', 'brand', 'category', 'thumbnail']);
        $products = $products->where('published', '1')->where('auction_product', 0)->where('approved', '1');

        if ($keyword) {
            $products->where(function ($q) use ($keyword) {
                $q->where('name', 'like', '%' . $keyword . '%')
                  ->orWhere('unit_price', 'like', '%' . $keyword . '%');
            });

            // Prioritize exact matches
            $case1 = addslashes($keyword) . '%';
            $case2 = '%' . addslashes($keyword) . '%';

            $products->orderByRaw("CASE
                WHEN name LIKE '$case1' THEN 1

                WHEN name LIKE '$case2' THEN 3

                ELSE 5
                END");
        }

        $products = $products->take(8)->get();

        $categories = Category::where('name', 'like', '%' . $keyword . '%')
            ->orWhere('slug', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'name', 'slug', 'banner as image', DB::raw('(SELECT COUNT(*) FROM products WHERE category_id = categories.id) as product_count')]);

        $brands = Brand::where('name', 'like', '%' . $keyword . '%')
            ->orWhere('slug', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'name', 'slug', 'logo', DB::raw('(SELECT COUNT(*) FROM products WHERE brand_id = brands.id) as product_count')]);

        $tags = Product::where('tags', 'like', '%' . $keyword . '%')
            ->where('published', '1')
            ->where('approved', '1')
            ->pluck('tags')
            ->filter()
            ->map(function($tags) {
                return explode(',', $tags);
            })
            ->flatten()
            ->unique()
            ->take(10)
            ->values();

        $related_searches = Search::where('query', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'query as text']);

        $popular_searches = Search::query()//where('query', 'like', '%' . $keyword . '%')
            ->orderBy('count', 'desc')
            ->take(5)
            ->get(['id', 'query as text', 'count']);

        // Update search count
        if ($keyword) {
            $search = Search::firstOrCreate(
                ['query' => $keyword],
                ['count' => 0]
            );
            $search->increment('count');
        }

        $formattedProducts = $products->map(function ($product) {
            return [
                'id' => $product->id,
                'title' => $product->name,
                'slug' => $product->slug,
                'price' => $product->unit_price,
                'original_price' => $product->unit_price + ($product->unit_price * ($product->discount / 100)),
                'discount_percent' => $product->discount,
                'image' => uploaded_asset($product->thumbnail_img),
                'category' => [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                    'slug' => $product->category->slug,
                ],
                'brand' => $product->brand ? [
                    'id' => $product->brand->id,
                    'name' => $product->brand->name,
                    'slug' => $product->brand->slug,
                ] : null,
            ];
        });

        return $this->success([
            'products' => $formattedProducts,
            'categories' => $categories,
            'brands' => $brands,
            'tags' => $tags,
            'related_searches' => $related_searches,
            'popular_searches' => $popular_searches
        ]);
    }
    public function suggestions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $keyword = $request->input('query'); // Fixed: Use input() method to get the query parameter
        $per_page = min((int)$request->input('limit', 10), 50);

        // Get search suggestions
        $searches = Search::where('query', 'like', '%' . $keyword . '%')
            ->orderBy('count', 'desc')
            ->take($per_page)
            ->get(['id', 'query as text'])
            ->map(function($item) {
                return [
                    'id' => 'srch-' . $item->id,
                    'text' => $item->text,
                    'type' => 'search'
                ];
            });



        return $this->success($searches);
    }
    public function popular(Request $request)
    {
        /*$validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }*/
        $keyword = $request->input('query'); // Fixed: Use input() method to get the query parameter
        $per_page = min((int)$request->input('limit', 10), 50);

        // Get search suggestions
        $searches = Search::query()//where('query', 'like', '%' . $keyword . '%')
            ->orderBy('count', 'desc')
            ->take($per_page)
            ->get(['id', 'query as text'])
            ->map(function($item) {
                return [
                    'id' => 'srch-' . $item->id,
                    'text' => $item->text,
                    'type' => 'search'
                ];
            });



        return $this->success($searches);
    }

    public function search_products(Request $request) {


        
    } // search_products


    public function index(Request $request, $category_id = null, $brand_id = null)
    {
        $query = $request->keyword;
        $sort_by = $request->sort_by;
        $min_price = $request->min_price;
        $max_price = $request->max_price;
        $seller_id = $request->seller_id;
        $filters = $request->filters;
        $selected_attribute_values = array();
        $selected_color = null;

        $products = Product::with(['stocks', 'product_translations', 'brand', 'category', 'thumbnail']);
        $products = $products->where('published', '1')->where('auction_product', 0)->where('approved', '1');

        if ($request->brands != null) {
            $brandIds = Brand::whereIn('slug', $request->brands)->pluck('id');
            $products = $products->whereIn('brand_id', $brandIds);
        }
        if ($request->categories != null) {
            $category_ids = Category::whereIn('slug', $request->categories)->pluck('id');
            $products = $products->whereIn('category_id', $category_ids);
        }
        if ($min_price != null && $max_price != null) {
            $products->where('unit_price', '>=', $min_price)->where('unit_price', '<=', $max_price);
        }
        if ($query != null) {
            $searchController = new SearchController;
            $searchController->store($request);


            $products->where(function ($q) use ($query) {
                $words = array_filter(explode(' ', trim($query)));
                foreach ($words as $word) {
                    $searchTerm = '%' . $word . '%';
                    $q->where(function ($subQuery) use ($searchTerm) {
                        $subQuery->where('name', 'like', $searchTerm)
                            ->orWhere('tags', 'like', $searchTerm);
                          /*  ->orWhereHas('product_translations', function ($q) use ($searchTerm) {
                                $q->where('name', 'like', $searchTerm);
                            }, '>', 0)
                          ->orWhereHas('stocks', function ($q) use ($searchTerm) {
                                $q->where('sku', 'like', $searchTerm);
                            }, '>', 0)
                           */
                    });
                }
            });

            $case1 = addslashes($query) . '%';
            $case2 = '%' . addslashes($query) . '%';

            $products->orderByRaw("CASE
                WHEN name LIKE '$case1' THEN 1
                WHEN name LIKE '$case2' THEN 2
                ELSE 3
                END");
        }
        switch ($sort_by) {
            case 'newest':
                $products->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $products->orderBy('created_at', 'asc');
                break;
            case 'price-asc':
                $products->orderBy('unit_price', 'asc');
                break;
            case 'price-desc':
                $products->orderBy('unit_price', 'desc');
                break;
            default:
                $products->orderBy('id', 'desc');
                break;
        }
        if ($request->has('colors')) {
            $colors = is_array($request->colors) ? $request->colors : [$request->colors];
            $products->where(function($query) use ($colors) {
                foreach($colors as $color) {
                    $str = '"' . $color . '"';
                    $query->orWhere('colors', 'like', '%' . $str . '%');
                }
            });

        }

        if ($request->has('selected_attribute_values')) {
            $selected_attribute_values = $request->selected_attribute_values;
            $products->where(function ($query) use ($selected_attribute_values) {
                foreach ($selected_attribute_values as $key => $value) {
                    $str = '"' . $value . '"';

                    $query->orWhere('choice_options', 'like', '%' . $str . '%');
                }
            });
        }
        $per_page = $request->per_page ?? 50;
        $page = $request->page ?? 1;

        $products = filter_products($products);

        $filter_values =array();
        if($filters){
            // Get Unique Categories
            $unique_category_ids = $products->pluck('category_id')->unique()->values();
            $unique_categories = Category::whereIn('id', $unique_category_ids)->get(['id', 'name', 'slug']);

            // Get Unique Brands
            $unique_brand_ids = $products->pluck('brand_id')->unique()->values();
            $unique_brands = Brand::whereIn('id', $unique_brand_ids)->get(['id', 'name', 'slug']);

            // Get Min & Max Price
            $min_price = $products->min('unit_price');
            $max_price = $products->max('unit_price');

            // Get Unique Colors
            $unique_colors = $products->pluck('colors')->filter()->flatten()->unique()->values();

            // Get Unique Attributes
            $unique_attributes = $products->pluck('choice_options')->filter()->flatten()->unique()->values();
            $filter_values = [
                'categories' => $unique_categories,
                'brands' => $unique_brands,
                'min_price' => $min_price,
                'max_price' => $max_price,
                'colors' => $unique_colors,
                'attributes' => $unique_attributes,
            ];
        }



        // Cache search results for 5 minutes

        $paginatedProducts = $products->paginate($per_page);

        // Pagination details
        $total_items = $paginatedProducts->total();
        $total_pages = $paginatedProducts->lastPage();

        $data = [
            'products' => new ProductsResource($paginatedProducts),
            'filter_values' => $filter_values,
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];

        return $this->success($data);



    }
}
