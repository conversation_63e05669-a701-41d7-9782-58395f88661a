{"__meta": {"id": "01K113VEJPB3GGKGJNKXJXSYR2", "datetime": "2025-07-25 08:39:58", "utime": **********.423302, "method": "GET", "uri": "/buzfi-new-backend/api/v3/brands/top", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": 1753457997.892173, "end": **********.423312, "duration": 0.5311388969421387, "duration_str": "531ms", "measures": [{"label": "Booting", "start": 1753457997.892173, "relative_start": 0, "end": **********.039043, "relative_end": **********.039043, "duration": 0.*****************, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.039051, "relative_start": 0.*****************, "end": **********.423313, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.046701, "relative_start": 0.*****************, "end": **********.049524, "relative_end": **********.049524, "duration": 0.0028231143951416016, "duration_str": "2.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.420546, "relative_start": 0.****************, "end": **********.420821, "relative_end": **********.420821, "duration": 0.00027489662170410156, "duration_str": "275μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.422212, "relative_start": 0.****************, "end": **********.422249, "relative_end": **********.422249, "duration": 3.719329833984375e-05, "duration_str": "37μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.24731, "accumulated_duration_str": "247ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wizard_infos` where `slug` = 'home-page-top-brands' and `wizard_info_status` = 1 order by `wizard_info_position` asc limit 1", "type": "query", "params": [], "bindings": ["home-page-top-brands", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0819051, "duration": 0.01221, "duration_str": "12.21ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 4.937}, {"sql": "select * from `wizard_details` where `wizard_details`.`wizard_info_id` in (10) order by `wizard_detail_position` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.096058, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 4.937, "width_percent": 0.21}, {"sql": "select * from `products` where `products`.`id` in (0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 396}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 379}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0974882, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:391", "source": {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 391}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=391", "ajax": false, "filename": "ApiHomePageController.php", "line": "391"}, "connection": "buzfi", "explain": null, "start_percent": 5.147, "width_percent": 0.198}, {"sql": "select * from `brands` where `brands`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.126117, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.346, "width_percent": 0.214}, {"sql": "select * from `brands` where `brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.127214, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.56, "width_percent": 0.077}, {"sql": "select * from `brands` where `brands`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1278849, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.637, "width_percent": 0.085}, {"sql": "select * from `brands` where `brands`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.128532, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.722, "width_percent": 0.069}, {"sql": "select * from `brands` where `brands`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.129041, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.79, "width_percent": 0.061}, {"sql": "select * from `brands` where `brands`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.129509, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.851, "width_percent": 0.057}, {"sql": "select * from `brands` where `brands`.`id` = 55 limit 1", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.129959, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.908, "width_percent": 0.057}, {"sql": "select * from `brands` where `brands`.`id` = 71 limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.130408, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 5.964, "width_percent": 0.057}, {"sql": "select * from `brands` where `brands`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.130864, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.021, "width_percent": 0.065}, {"sql": "select * from `brands` where `brands`.`id` = 57 limit 1", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.131332, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "TopBrandsResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/TopBrandsResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\TopBrandsResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FTopBrandsResource.php&line=18", "ajax": false, "filename": "TopBrandsResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 6.085, "width_percent": 0.061}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 1 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.132103, "duration": 0.0235, "duration_str": "23.5ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 6.146, "width_percent": 9.502}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028386' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028386"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.156727, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 15.648, "width_percent": 0.194}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 3 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.199486, "duration": 0.02283, "duration_str": "22.83ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 15.842, "width_percent": 9.231}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028380' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028380"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.223095, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 25.074, "width_percent": 0.105}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 13 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.224098, "duration": 0.02308, "duration_str": "23.08ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 25.179, "width_percent": 9.332}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028381' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028381"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.248075, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 34.511, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 14 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.249077, "duration": 0.023469999999999998, "duration_str": "23.47ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 34.629, "width_percent": 9.49}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028382' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028382"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.273355, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 44.119, "width_percent": 0.113}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 18 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.274398, "duration": 0.02283, "duration_str": "22.83ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 44.232, "width_percent": 9.231}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028383' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028383"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.298002, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 53.463, "width_percent": 0.109}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 34 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.298909, "duration": 0.022600000000000002, "duration_str": "22.6ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 53.572, "width_percent": 9.138}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028379' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028379"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.322225, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 62.711, "width_percent": 0.117}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 55 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [55], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.323197, "duration": 0.022350000000000002, "duration_str": "22.35ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 62.828, "width_percent": 9.037}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028384' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028384"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.346374, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 71.865, "width_percent": 0.113}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 71 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3473299, "duration": 0.022850000000000002, "duration_str": "22.85ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 71.978, "width_percent": 9.239}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028378' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028378"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3710601, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 81.218, "width_percent": 0.129}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 38 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.372254, "duration": 0.02302, "duration_str": "23.02ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 81.347, "width_percent": 9.308}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028450' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028450"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3960772, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 90.655, "width_percent": 0.113}, {"sql": "select count(*) as aggregate from `products` where `products`.`brand_id` = 57 and `products`.`brand_id` is not null", "type": "query", "params": [], "bindings": [57], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/BrandResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BrandResource.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.397035, "duration": 0.02283, "duration_str": "22.83ms", "memory": 0, "memory_str": null, "filename": "Brand.php:33", "source": {"index": 19, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=33", "ajax": false, "filename": "Brand.php", "line": "33"}, "connection": "buzfi", "explain": null, "start_percent": 90.769, "width_percent": 9.231}]}, "models": {"data": {"App\\Models\\WizardDetail": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardDetail.php&line=1", "ajax": false, "filename": "WizardDetail.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Upload": {"retrieved": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\WizardInfo": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardInfo.php&line=1", "ajax": false, "filename": "WizardInfo.php", "line": "?"}}}, "count": 30, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 30}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/brands/top", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands", "uri": "GET api/v3/brands/top", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=370\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=370\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:370-411</a>", "middleware": "api", "duration": "532ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1963112224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1963112224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-586064797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-586064797\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1166834583 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"526 characters\">__stripe_mid=59bbba41-0bc6-4270-beba-c08eceb34850d377ea; __stripe_sid=2c04ab5e-db33-44eb-b794-7ff52d70f8f186a81f; XSRF-TOKEN=eyJpdiI6Iml3ZEdmZ1VEVnE5MlZTTExvY1IvK2c9PSIsInZhbHVlIjoia1dmYUtIN0lFUXM3QnJONldPZ0tuRDhtWDZDMGRTRVp1MStCeFJRTm1SSFJqaDM1TTBYNld0SEFLOThNMlo0WU5DRmVnQldpQStvb1FzMUhRb0tPRGZ0OHZjYldkKy83K3lCK3BQV3N1OGdYL3VQM1duTmtEYlN5RkVTYk45VFIiLCJtYWMiOiJkZmQ3ZjEwZWU4YTM3ZWM2MDIxMmE4ZDE3YjE4NDI1N2VkMDEyZmZhMWYyOWY1ZjBiNWFiODM3NGU1Nzc2YTMwIiwidGFnIjoiIn0%3D; buzficom_session=aXruQBwO7fbEwQbNk6qQyzsvWaPEf0Er1BLUmpJ7</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166834583\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1093235171 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093235171\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-575956795 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">max-age=604800, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:39:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-cache</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">MISS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">588</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikx1a0JQNDIxU2F4SFRhanlRNkZnQVE9PSIsInZhbHVlIjoidnpjNGlLZFUzNE00VFRFUmdoalZ5cGMrbnFMSVloQ01jakhDQ29XVitsQXd5YmVPZkFjRzk1YjZRbjJGK2xBdll4eTVQTDU3YXlibFd0ZXhiZDRhUkZDUEdNV1VLR1UyVlVJVzhDZ1NWTmwyVWlaN0FXSSt4anY0U0NGbmJkVEEiLCJtYWMiOiI4NzJlMzRmM2U4MTljYWZhZGE5NjZkMzE5ODg5MGEzM2U0ZmRlMTZhZDIwZTMzZjM3YmEyODhjZTg0ODNjZjg2IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6ImpPbmhvV1FEREdvWVFUNUE5bHBCS3c9PSIsInZhbHVlIjoiVzBQcmdBck0vMlVaWW1IM2NtV2ppR29tZGtTQUl0a3NjTGFMMXpTbU9uRFVLbnRaWEx6WXNpc0FQNFdUQWN2aDJMRlUwREs3QXY3RGtnOEkwbXlCN2cxVTV1N0VWaXpsb2pMR3AvQUZDdTdxUTJTZEw0Uk00RXZMMnVOenNZVVciLCJtYWMiOiIyNjAwOTQwODliOTRkYjRjM2Q5NDMyMzk0YjUzOGFjMTgxMDBhMzEyMTdjMDQ4MjJiMTNlODU0MDAxMWM4OTBiIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikx1a0JQNDIxU2F4SFRhanlRNkZnQVE9PSIsInZhbHVlIjoidnpjNGlLZFUzNE00VFRFUmdoalZ5cGMrbnFMSVloQ01jakhDQ29XVitsQXd5YmVPZkFjRzk1YjZRbjJGK2xBdll4eTVQTDU3YXlibFd0ZXhiZDRhUkZDUEdNV1VLR1UyVlVJVzhDZ1NWTmwyVWlaN0FXSSt4anY0U0NGbmJkVEEiLCJtYWMiOiI4NzJlMzRmM2U4MTljYWZhZGE5NjZkMzE5ODg5MGEzM2U0ZmRlMTZhZDIwZTMzZjM3YmEyODhjZTg0ODNjZjg2IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6ImpPbmhvV1FEREdvWVFUNUE5bHBCS3c9PSIsInZhbHVlIjoiVzBQcmdBck0vMlVaWW1IM2NtV2ppR29tZGtTQUl0a3NjTGFMMXpTbU9uRFVLbnRaWEx6WXNpc0FQNFdUQWN2aDJMRlUwREs3QXY3RGtnOEkwbXlCN2cxVTV1N0VWaXpsb2pMR3AvQUZDdTdxUTJTZEw0Uk00RXZMMnVOenNZVVciLCJtYWMiOiIyNjAwOTQwODliOTRkYjRjM2Q5NDMyMzk0YjUzOGFjMTgxMDBhMzEyMTdjMDQ4MjJiMTNlODU0MDAxMWM4OTBiIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575956795\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-27026866 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://localhost/buzfi-new-backend/api/v3/brands/top</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27026866\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/brands/top", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@top_brands"}, "badge": null}}