{"__meta": {"id": "01K113VCFJ8XEM8WEVS1TNY9KS", "datetime": "2025-07-25 08:39:56", "utime": **********.275197, "method": "GET", "uri": "/buzfi-new-backend/api/v3/banners-middle/hero", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": 1753457995.940263, "end": **********.275207, "duration": 0.3349440097808838, "duration_str": "335ms", "measures": [{"label": "Booting", "start": 1753457995.940263, "relative_start": 0, "end": **********.134069, "relative_end": **********.134069, "duration": 0.*****************, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.13408, "relative_start": 0.****************, "end": **********.275209, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.145311, "relative_start": 0.****************, "end": **********.148349, "relative_end": **********.148349, "duration": 0.0030379295349121094, "duration_str": "3.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.272404, "relative_start": 0.****************, "end": **********.272609, "relative_end": **********.272609, "duration": 0.00020503997802734375, "duration_str": "205μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.273728, "relative_start": 0.****************, "end": **********.273768, "relative_end": **********.273768, "duration": 4.00543212890625e-05, "duration_str": "40μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.031490000000000004, "accumulated_duration_str": "31.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `id`, `slug`, `wizard_info_status`, `wizard_info_position`, `start_date`, `end_date` from `wizard_infos` where `wizard_info_status` = 1 and `slug` in ('home-page-middle-hero-banner-one', 'home-page-middle-hero-banner-two', 'home-page-middle-hero-banner-three') order by `wizard_info_position` asc", "type": "query", "params": [], "bindings": [1, "home-page-middle-hero-banner-one", "home-page-middle-hero-banner-two", "home-page-middle-hero-banner-three"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 110}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 84}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.179554, "duration": 0.026510000000000002, "duration_str": "26.51ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:110", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=110", "ajax": false, "filename": "ApiHomePageController.php", "line": "110"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 84.185}, {"sql": "select `id`, `wizard_info_id`, `wizard_detail_title`, `wizard_detail_sub_title`, `wizard_detail_button_text`, `wizard_detail_button_link`, `wizard_detail_images`, `wizard_detail_mobile_images`, `wizard_detail_background_color`, `wizard_detail_position` from `wizard_details` where `wizard_details`.`wizard_info_id` in (12, 13, 14) order by `wizard_detail_position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 84}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.209317, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:110", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=110", "ajax": false, "filename": "ApiHomePageController.php", "line": "110"}, "connection": "buzfi", "explain": null, "start_percent": 84.185, "width_percent": 3.334}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V4/WizardDetailResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V4\\WizardDetailResource.php", "line": 13}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 116}], "start": **********.213583, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 87.52, "width_percent": 2.35}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028429' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028429"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.2155318, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 89.87, "width_percent": 1.937}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V4/WizardDetailResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V4\\WizardDetailResource.php", "line": 13}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 120}], "start": **********.262417, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 91.807, "width_percent": 1.62}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028432' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028432"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.2636821, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 93.426, "width_percent": 1.048}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V4/WizardDetailResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V4\\WizardDetailResource.php", "line": 13}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 120}], "start": **********.264645, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 94.474, "width_percent": 1.048}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028430' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028430"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.265558, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 95.522, "width_percent": 0.826}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V4/WizardDetailResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V4\\WizardDetailResource.php", "line": 13}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 120}], "start": **********.2663748, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 96.348, "width_percent": 0.762}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028431' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028431"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.2671678, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 97.11, "width_percent": 0.762}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V4/WizardDetailResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V4\\WizardDetailResource.php", "line": 13}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 124}], "start": **********.268082, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 97.872, "width_percent": 1.207}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028398' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028398"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceCollection.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php", "line": 102}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}], "start": **********.2690961, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 99.079, "width_percent": 0.921}]}, "models": {"data": {"App\\Models\\WizardInfo": {"retrieved": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardInfo.php&line=1", "ajax": false, "filename": "WizardInfo.php", "line": "?"}}, "App\\Models\\WizardDetail": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardDetail.php&line=1", "ajax": false, "filename": "WizardDetail.php", "line": "?"}}, "App\\Models\\Upload": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}}, "count": 18, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 18}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/banners-middle/hero", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners_tow", "uri": "GET api/v3/banners-middle/hero", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners_tow<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:79-130</a>", "middleware": "api", "duration": "335ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-714420547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-714420547\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-707202678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-707202678\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1209829244 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=300</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"526 characters\">__stripe_mid=59bbba41-0bc6-4270-beba-c08eceb34850d377ea; __stripe_sid=2c04ab5e-db33-44eb-b794-7ff52d70f8f186a81f; XSRF-TOKEN=eyJpdiI6IittUTZpZXVZWUs5a0Jrb3psQUd2aUE9PSIsInZhbHVlIjoiTWdZR2pKZ05vS0ZSNnRiaFBDRTh6OXRSK3NqVXRqWDEwanAwNm1Cc29UMW5lTEp5eG1WSDNzdWU4T2hTTVFsZmNWZXZITVBweWVKZGs5OXNmbTVJSlJibEpxZlRyQXFKcmtCb056cE1qTjAzQW1RTlFtSmNtdnRmNlRhN0xucUQiLCJtYWMiOiJkYjg0ZWFkODAwYjlkNDYzZDMyMTlhYTFmMjZkNTE1OGNmYzhjODBkMGJkMDRjYjFjODc5NjdiNTAzZDM1NGVhIiwidGFnIjoiIn0%3D; buzficom_session=JWjtPJUDA82SnGvCjllfx5YsYXf54vltIGDIOjXB</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209829244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1779475111 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779475111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2011354529 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">max-age=2592000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:39:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">592</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjRwN0FxL3N2MUtBZDFlM3ZtQ3p3QkE9PSIsInZhbHVlIjoiRnd1RkI2Wm5Jejk4YzlxK1AvVlVGb1R4SDY2RVdZNEViMkNPbGw2ZGhaU0N2K3lWVHVrTHNPOUVSZEJOMkV4WXJxNXUxOFBuMzA1eW5NZjZ6SzJsNzJYQ1Z2RFl3Y25lZFc5eG1RQ3BwcUJaR1E0NHVhTllDeEJQY0xCSkpGaFkiLCJtYWMiOiI5NzRhMWNhY2M3ZWE1ZGEwMWM5MzUxOWExNzAzOTczYmFhNDE4MWVjYzYxOTc3NWZmOTMyN2QwZDQ4YzNmMDRjIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:56 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IlpaWTFXZHYyQmJoTWtpc3lQMGxUeFE9PSIsInZhbHVlIjoiR1FhNFArUVlWTVc1SWNZUkxDYmh2MFFhbXRBV3Q3RTNlSlBvakt6OXdLc0dFc2ZEYitOT1FBTXlhS1M3OE5MMFpRUnVwZmhEYmtoQnkxOWcvNEoyVVBYd2RneGYzTU5XMk44UE05c2EzeU1kOS9wR0lWMzRRV1hiTExFQ2ZSVGMiLCJtYWMiOiJmYzRkODIyNmVjNjhhODhhMmM4ZTIyMDlhMDQ2NzQ3ZWU0MzliZmU5M2EyMWIzYTY1MzdhNzc3ODIzMjlkM2Q0IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:39:56 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjRwN0FxL3N2MUtBZDFlM3ZtQ3p3QkE9PSIsInZhbHVlIjoiRnd1RkI2Wm5Jejk4YzlxK1AvVlVGb1R4SDY2RVdZNEViMkNPbGw2ZGhaU0N2K3lWVHVrTHNPOUVSZEJOMkV4WXJxNXUxOFBuMzA1eW5NZjZ6SzJsNzJYQ1Z2RFl3Y25lZFc5eG1RQ3BwcUJaR1E0NHVhTllDeEJQY0xCSkpGaFkiLCJtYWMiOiI5NzRhMWNhY2M3ZWE1ZGEwMWM5MzUxOWExNzAzOTczYmFhNDE4MWVjYzYxOTc3NWZmOTMyN2QwZDQ4YzNmMDRjIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IlpaWTFXZHYyQmJoTWtpc3lQMGxUeFE9PSIsInZhbHVlIjoiR1FhNFArUVlWTVc1SWNZUkxDYmh2MFFhbXRBV3Q3RTNlSlBvakt6OXdLc0dFc2ZEYitOT1FBTXlhS1M3OE5MMFpRUnVwZmhEYmtoQnkxOWcvNEoyVVBYd2RneGYzTU5XMk44UE05c2EzeU1kOS9wR0lWMzRRV1hiTExFQ2ZSVGMiLCJtYWMiOiJmYzRkODIyNmVjNjhhODhhMmM4ZTIyMDlhMDQ2NzQ3ZWU0MzliZmU5M2EyMWIzYTY1MzdhNzc3ODIzMjlkM2Q0IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:39:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011354529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1579200441 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1LVju8dIJOlGyQzAI8hf424dRvXYBM5RR2HB6Hef</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost/buzfi-new-backend/api/v3/banners-middle/hero</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579200441\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/banners-middle/hero", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners_tow"}, "badge": null}}