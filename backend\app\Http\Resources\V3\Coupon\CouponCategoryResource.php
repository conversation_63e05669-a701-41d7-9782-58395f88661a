<?php

namespace App\Http\Resources\V3\Coupon;

use Illuminate\Http\Resources\Json\JsonResource;

class CouponCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => (string) $this->slug,
            'name' => $this->name,
            'count' => $this->coupon_count,
            'image' => $this->banner ? asset($this->banner) : null
        ];
    }
}
