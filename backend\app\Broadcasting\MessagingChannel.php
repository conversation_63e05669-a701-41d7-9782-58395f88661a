<?php

namespace App\Broadcasting;

use App\Models\User;
use App\Models\Messaging\MessageThread;
use App\Models\Messaging\Conversation;
use Illuminate\Support\Facades\DB;

class MessagingChannel
{
    /**
     * Create a new channel instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the messaging channel.
     *
     * @param  \App\Models\User  $user
     * @return array|bool
     */
    public function join(User $user)
    {
        return true;
    }

    /**
     * Authenticate the user's access to a thread channel.
     *
     * @param  \App\Models\User  $user
     * @param  string  $threadId
     * @return array|bool
     */
    public function joinThread(User $user, $threadId)
    {
        // Check if user is a participant in the thread
        return DB::table('thread_participants')
            ->where('thread_id', $threadId)
            ->where('user_id', $user->id)
            ->exists();
    }

    /**
     * Authenticate the user's access to a conversation channel.
     *
     * @param  \App\Models\User  $user
     * @param  string  $conversationId
     * @return array|bool
     */
    public function joinConversation(User $user, $conversationId)
    {
        // Check if user is a participant in the conversation
        return DB::table('conversations')
            ->where('id', $conversationId)
            ->where(function($query) use ($user) {
                $query->where('user1_id', $user->id)
                      ->orWhere('user2_id', $user->id);
            })
            ->exists();
    }

    /**
     * Authenticate the user's access to their unread count channel.
     *
     * @param  \App\Models\User  $user
     * @param  string  $userId
     * @return array|bool
     */
    public function joinUnreadCount(User $user, $userId)
    {
        // Only allow access to user's own unread count channel
        return (string) $user->id === (string) $userId;
    }
} 