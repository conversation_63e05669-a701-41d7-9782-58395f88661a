<?php

namespace App\Http\Resources\V3\Coupon;

use Illuminate\Http\Resources\Json\JsonResource;

class ExpiringSoonCouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Parse details JSON
        $details = json_decode($this->details, true) ?? [];
        
        // Calculate days remaining
        $now = strtotime(date('d-m-Y'));
        $daysRemaining = floor(($this->end_date - $now) / (60 * 60 * 24));
        
        return [
            'id' => (string) $this->id,
            'code' => $this->code,
            'type' => $this->discount_type == 'percent' ? 'percentage' : 'fixed',
            'value' => (float) $this->discount,
            'minPurchase' => isset($details['min_buy']) ? (float) $details['min_buy'] : null,
            'description' => $this->getDescription(),
            'startDate' => date('Y-m-d\TH:i:s\Z', $this->start_date),
            'endDate' => date('Y-m-d\TH:i:s\Z', $this->end_date),
            'status' => $this->isActive() ? 'active' : 'inactive',
            'image' => $this->image ? asset($this->image) : null,
            'daysRemaining' => $daysRemaining
        ];
    }
}
