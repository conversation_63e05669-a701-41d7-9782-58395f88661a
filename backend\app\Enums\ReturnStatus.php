<?php

namespace App\Enums;

enum ReturnStatus: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case REJECTED = 'rejected';
    case CANCELLED = 'cancelled';
    case CANCELLED_BY_USER = 'cancelled_by_user';
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
