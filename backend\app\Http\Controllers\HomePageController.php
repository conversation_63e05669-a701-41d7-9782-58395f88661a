<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\FlashDeal;
use App\Models\FlashDealProduct;
use App\Models\PopularProductInfo;
use App\Models\Product;
use App\Models\HomePageBanner;
use App\Models\ProductsByCategorySectionInfo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;
use Jenssegers\Agent\Agent;

class HomePageController
{

    public function index()
    {
        $agent = new Agent();

        $data = array();
        if ($agent->isMobile()) {
            $data['flash_deal_grid_product_number'] = 2;
            $data['top_pick_grid_product_number'] = 2;
        } else if ($agent->isTablet()) {
            $data['flash_deal_grid_product_number'] = 12;
            $data['top_pick_grid_product_number'] = 4;
        } else {
            $data['flash_deal_grid_product_number'] = 12;
            $data['top_pick_grid_product_number'] = 5;
        }
        //$data['featured_categories'] = Category::all();
        //$data['popularProducts'] = PopularProductInfo::with(['popular_products', 'popular_products.product'])->where('id', 1)->where('status', 1)->first();



       $data['products_by_categories'] = Cache::remember('home_page_products_by_categories', now()->addMinutes(60), function () {
            return ProductsByCategorySectionInfo::with([
                'product_by_category_section_details',
                'product_by_category_section_details.category'
            ])->where('status', 1)->get();
        });
       $data['popularProducts'] = Cache::remember('home_page_popular_products', now()->addMinutes(60), function () {
            return PopularProductInfo::with(['popular_products', 'popular_products.product'])->where('id', 1)->where('status', 1)->first();
        });

        /*$data['products_by_categories'] = ProductsByCategorySectionInfo::with([
            'product_by_category_section_details',
            'product_by_category_section_details.category'
        ])->where('status', 1)->get();*/

        $data['flash_deals'] = FlashDeal::where('status', 1)->orderBy('created_at', 'desc')->get();
        $data['first_flash_deal'] = $data['flash_deals']->first();
        $data['home_page_banners'] = HomePageBanner::where('status', 1)->orderBy('position', 'asc')->get();
        return view('frontend.index-new', $data);
    }

    public function get_product_by_category(Request $request)
    {
        if ($request->ajax()) {
            $agent = new Agent();
            $top_pick_grid_product_number = 5;
            if ($agent->isMobile()) {
                $top_pick_grid_product_number = 2;
            } else if ($agent->isTablet()) {
                $top_pick_grid_product_number = 4;
            }
            $category = Category::where('slug', $request->category_slug)->first();
            $product_collection = Product::where('category_id', $category->id)->orderBy('created_at', 'desc');
            $data['products'] = filter_products($product_collection)->paginate($top_pick_grid_product_number);
            $data['pagination_class_name'] = 'pagination-link';
            $data['category_slug'] = $request->category_slug;
            $data['category_name'] = $category->getTranslation('name');
            return view('frontend.home_page_inc.top_pick_category_products', $data)->render();
        }
    }

    public function get_offer_product_by_offer_id(Request $request)
    {
        if ($request->ajax()) {
            $agent = new Agent();
            $flash_deal_grid_product_number = 12;
            if ($agent->isMobile()) {
                $flash_deal_grid_product_number = 2;
            } else if ($agent->isTablet()) {
                $flash_deal_grid_product_number = 12;
            }
            $flash_deal = FlashDeal::where('slug', $request->category_slug)->first();
            $data['flash_deal_products'] = FlashDealProduct::where('flash_deal_id', $flash_deal->id)
                ->orderBy('created_at', 'desc')
                ->paginate($flash_deal_grid_product_number);
            $data['pagination_class_name'] = 'pagination-link-offer';
            $data['category_slug'] = $request->category_slug;
            return view('frontend.home_page_inc.offer_products_div', $data)->render();
        }
    }
}
