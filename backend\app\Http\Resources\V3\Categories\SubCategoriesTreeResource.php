<?php

namespace App\Http\Resources\V3\Categories;

use Illuminate\Http\Resources\Json\JsonResource;

class SubCategoriesTreeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

            return [
                'id' => (int)$this->id,
                'name' => $this->name,
                'slug' => $this->slug,
                'level' => $this->level,
                'children' => $this->categories ? SubCategoriesTreeResource::collection($this->childrenCategories) : [],
            ];

    }
}
