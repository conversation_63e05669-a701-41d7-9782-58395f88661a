<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\SearchController;
use App\Http\Resources\V3\EarlyAccessProductCollection;
use App\Http\Resources\V3\PremiumProductCollection;
use App\Http\Resources\V3\ProductResource;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Review;
use App\Services\ApiProductService;
use App\Services\ApiTrendingProductService;
use App\Services\Product\BestSellersProductService;
use App\Services\Product\DealOfTheDayService;
use App\Services\Product\EarlyAccessProductsService;
use App\Services\Product\NewArrivalsProductService;
use App\Services\Product\PremiumProductsService;
use App\Services\ProductReviewRatingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ApiProductController extends ApiResponse
{
    protected ProductReviewRatingService $productReviewRatingService;
    public function __construct(ProductReviewRatingService $productReviewRatingService)
    {
        parent::__construct();
        $this->productReviewRatingService = $productReviewRatingService;
    }

    /**
     * List products with pagination
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // Set pagation parameters
            $per_page = min((int) $request->input('per_page', 20), 50);
            $page = max((int) $request->input('page', 1), 1);

            // Build base query with eager loading for performance
            $products = Product::with(['category', 'brand', 'stocks'])
                ->where('published', '1')
                ->where('auction_product', 0)
                ->where('approved', '1');

            // Category filters with error handling
            if ($category = $request->input('category')) {
                $category_id = Category::where('slug', $category)->value('id');
                if ($category_id) {
                    $products->where('category_id', $category_id);
                }
            }
            if ($subcategory = $request->input('subcategory')) {
                $subcategory_id = Category::where('slug', $subcategory)->value('id');
                if ($subcategory_id) {
                    $products->where('subcategory_id', $subcategory_id);
                }
            }
            if ($subsubcategory = $request->input('subsubcategory')) {
                $subsubcategory_id = Category::where('slug', $subsubcategory)->value('id');
                if ($subsubcategory_id) {
                    $products->where('subsubcategory_id', $subsubcategory_id);
                }
            }

            // Brand filter with error handling
            if ($brand = $request->input('brand')) {
                $brand_id = Brand::where('slug', $brand)->value('id');
                if ($brand_id) {
                    $products->where('brand_id', $brand_id);
                }
            }

            // Price range filters with validation
            if ($price_min = $request->input('price_min')) {
                $products->where('unit_price', '>=', (float)$price_min);
            }
            if ($price_max = $request->input('price_max')) {
                $products->where('unit_price', '<=', (float)$price_max);
            }

            // Color filter with sanitization
            if ($colors = $request->input('color')) {
                $colorArray = array_filter(explode(',', $colors));
                if (!empty($colorArray)) {
                    $products->where(function ($query) use ($colorArray) {
                        foreach ($colorArray as $color) {
                            $str = '"' . trim($color) . '"';
                            $query->orWhere('colors', 'like', '%' . $str . '%');
                        }
                    });
                }
            }

            // Rating filter with validation
            if ($rating = $request->input('rating')) {
                $rating = min(max((int)$rating, 1), 5);
                $products->where('rating', '>=', $rating);
            }

            // Special offers filters with validation
            if ($offers = $request->input('special_offers')) {
                $offersArray = array_filter(explode(',', $offers));
                if (!empty($offersArray)) {
                    $products->where(function ($query) use ($offersArray) {
                        if (in_array('on-sale', $offersArray)) {
                            $query->orWhere('discount', '>', 0);
                        }
                        if (in_array('free-delivery', $offersArray)) {
                            $query->orWhere('shipping_cost', 0);
                        }
                    });
                }
            }

            // Featured products filter
            if ($request->input('is_featured') === 'true') {
                $products->where('featured', 1);
            }

            // Search query with optimization
            if ($search = $request->input('search_query')) {
                $searchController = new SearchController();
                $searchController->store($request);

                $words = array_filter(array_map('trim', explode(' ', $search)));
                if (!empty($words)) {
                    $products->where(function ($q) use ($words) {
                        foreach ($words as $word) {
                            $searchTerm = '%' . $word . '%';
                            $q->where(function ($subQuery) use ($searchTerm) {
                                $subQuery->where('name', 'like', $searchTerm)
                                    ->orWhere('tags', 'like', $searchTerm)
                                    ->orWhere('description', 'like', $searchTerm);
                            });
                        }
                    });

                    // Prioritize exact matches
                    $case1 = addslashes($search) . '%';
                    $case2 = '%' . addslashes($search) . '%';
                    $products->orderByRaw("CASE
                        WHEN name LIKE ? THEN 1
                        WHEN name LIKE ? THEN 2
                        ELSE 3
                        END", [$case1, $case2]);
                }
            }

            // Sorting with validation
            $sort = $request->input('sort', 'popularity');

            // Handle multiple sort parameters
            $sortParams = explode(',', $sort);
            foreach ($sortParams as $sortParam) {
                switch (strtolower(trim($sortParam))) {
                    case 'newest':
                        $products->orderBy('created_at', 'desc');
                        break;
                    case 'oldest':
                        $products->orderBy('created_at', 'asc');
                        break;
                    case 'price_low':
                    case 'price_asc':
                        $products->orderBy('unit_price', 'asc');
                        break;
                    case 'price_high':
                    case 'price_desc':
                        $products->orderBy('unit_price', 'desc');
                        break;
                    case 'rating':
                    case 'rating_desc':
                        $products->orderBy('rating', 'desc')
                            ->orderBy('num_of_sale', 'desc'); // Secondary sort
                        break;
                    case 'rating_asc':
                        $products->orderBy('rating', 'asc')
                            ->orderBy('num_of_sale', 'desc'); // Secondary sort
                        break;
                    case 'name_asc':
                        $products->orderBy('name', 'asc');
                        break;
                    case 'name_desc':
                        $products->orderBy('name', 'desc');
                        break;
                    case 'sales_high':
                        $products->orderBy('num_of_sale', 'desc')
                            ->orderBy('rating', 'desc'); // Secondary sort
                        break;
                    case 'sales_low':
                        $products->orderBy('num_of_sale', 'asc')
                            ->orderBy('rating', 'desc'); // Secondary sort
                        break;
                    case 'popularity':
                    default:
                        if (!in_array('sales_high', $sortParams) && !in_array('sales_low', $sortParams)) {
                            $products->orderBy('num_of_sale', 'desc')
                                ->orderBy('rating', 'desc'); // Secondary sort
                        }
                        break;
                }
            }

            // Apply any additional filters from the filter_products helper
            $products = filter_products($products);

            // Get paginated results with error handling
            try {
                $paginatedProducts = $products->paginate($per_page);
            } catch (\Exception $e) {
                \Log::error('Pagination error: ' . $e->getMessage());
                $paginatedProducts = $products->simplePaginate($per_page);
            }

            // Format response
            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $paginatedProducts->lastPage(),
                    'totalItems' => $paginatedProducts->total(),
                    'itemsPerPage' => (int) $per_page,
                ],
                'filters' => [
                    'category' => $request->input('category'),
                    'subcategory' => $request->input('subcategory'),
                    'subsubcategory' => $request->input('subsubcategory'),
                    'brand' => $request->input('brand'),
                    'price_min' => $request->input('price_min'),
                    'price_max' => $request->input('price_max'),
                    'color' => $request->input('color'),
                    'rating' => $request->input('rating'),
                    'special_offers' => $request->input('special_offers'),
                    'is_featured' => $request->input('is_featured'),
                    'sort' => $sort
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            \Log::error('Error in product index: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product details
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    private function validateAndGetProduct(Request $request): Product|JsonResponse
    {
        $messages = array(
            'slug.required' => translate('Please enter a valid product slug'),
            'slug.exists' => translate('Invalid product slug.Product not found'),
        );
        $validator = Validator::make($request->all(), [
            'slug' => 'required|exists:products,slug',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $product = Product::where('slug', $request->slug)
            ->where('published', '1')
            ->where('auction_product', 0)
            ->where('approved', '1')
            ->first();

        if (!$product) {
            return $this->error(
                'product_not_found',
                'Product not found',
                null,
                404
            );
        }

        return $product;
    }

    public function get_product_details(Request $request): JsonResponse
    {
        $result = $this->validateAndGetProduct($request);

        if ($result instanceof JsonResponse) {
            return $result;
        }
        try {
            if (auth('sanctum')->user()) {
                $apiProductService = new ApiProductService();
                $r = $apiProductService->storeRecentlyViewedProduct(auth('sanctum')->user()->id, $result->id);
            }
            return $this->success(new ProductResource($result));
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch recently viewed products',
                $e->getMessage(),
                500
            );
        }

    }

    /**
     * Get product details (camelCase version to match route)
     *
     * @param string $idOrSlug
     * @param Request $request
     * @return JsonResponse
     */
    public function getProductDetails($idOrSlug, Request $request): JsonResponse
    {
        try {
            // Check if we're looking up by numeric ID or by string slug
            if (is_numeric($idOrSlug)) {
                $product = Product::where('id', $idOrSlug)
                    ->where('published', '1')
                    ->where('auction_product', 0)
                    ->where('approved', '1')
                    ->first();
            } else {
                $product = Product::where('slug', $idOrSlug)
                    ->where('published', '1')
                    ->where('auction_product', 0)
                    ->where('approved', '1')
                    ->first();
            }

            if (!$product) {
                return $this->error(
                    'product_not_found',
                    'Product not found',
                    null,
                    404
                );
            }

            // Record recently viewed product
            if (auth('sanctum')->user()) {
                $apiProductService = new ApiProductService();
                $apiProductService->storeRecentlyViewedProduct(auth('sanctum')->user()->id, $product->id);
            }

            return $this->success(new ProductResource($product));
        } catch (\Exception $e) {
            \Log::error('Error in getProductDetails: ' . $e->getMessage(), [
                'idOrSlug' => $idOrSlug,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product details',
                $e->getMessage(),
                500
            );
        }
    }

    public function get_product_description(Request $request): JsonResponse
    {
        $result = $this->validateAndGetProduct($request);
        if ($result instanceof JsonResponse) {
            return $result;
        }
        return $this->success(new ProductResource($result));
    }
    public function get_product_specifications(Request $request): JsonResponse
    {
        $result = $this->validateAndGetProduct($request);
        if ($result instanceof JsonResponse) {
            return $result;
        }
        return $this->success(new ProductResource($result));
    }

    public function get_product_ratings(Request $request): JsonResponse
    {
        $product = $this->validateAndGetProduct($request);
        if ($product instanceof JsonResponse) {
            return $product;
        }
        $ratings = Review::where('product_id', $product->id)->where('status', 1)
            ->selectRaw('rating, COUNT(*) as total')
            ->groupBy('rating')
            ->pluck('total', 'rating');

        $totalReviews = $ratings->sum();

        // Ensure all 5 star ratings are represented in percentage
        $percentageBreakdown = collect(range(1, 5))->mapWithKeys(function ($star) use ($ratings, $totalReviews) {
            $count = $ratings->get($star, 0);
            $percentage = $totalReviews > 0 ? round(($count / $totalReviews) * 100, 2) : 0;
            return [$star => $percentage];
        });

        $average = Review::where('product_id', $product->id)
            ->where('status', 1)
            ->avg('rating');
        $data['averageRating'] = round($average ?? 0, 2);//(int)$product->getApprovedRatingCount();
        $data['ratingPercentageBreakdown'] = $percentageBreakdown;

        return $this->success($data);
    }
    public function get_product_reviews(Request $request): JsonResponse
    {
        $product = $this->validateAndGetProduct($request);
        if ($product instanceof JsonResponse) {
            return $product;
        }
        $per_page = min((int) $request->input('per_page', 3), 50);
        $page = max((int) $request->input('page', 1), 1);

        return $this->productReviewRatingService->get_product_reviews($per_page, $page, 1, $product->id);

    }

    /**
     * Get personalized product recommendations
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);

            // Get product IDs from request if provided
            $productIds = null;
            if ($request->has('ids')) {
                $ids = explode(',', $request->input('ids'));
                $productIds = array_filter(array_map('intval', $ids));

                // Validate that the products exist
                $existingProductIds = Product::whereIn('id', $productIds)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->pluck('id')
                    ->toArray();

                $productIds = array_intersect($productIds, $existingProductIds);
            }

            // Get user ID if authenticated
            $userId = auth('sanctum')->check() ? auth('sanctum')->id() : null;


            // Get recommendations from service
            $apiProductService = new ApiProductService();
            $recommendations = $apiProductService->getPersonalizedRecommendations(
                $userId,
                $productIds,
                $limit * 2 // Get more than needed to allow for pagination
            );

            // Paginate the results manually
            $total = $recommendations->count();
            $totalPages = ceil($total / $perPage);

            $paginatedRecommendations = $recommendations->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedRecommendations),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product recommendations',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get related product recommendations (general related products without specific product ID)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRelatedRecommendations(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);
            $categoryId = $request->input('category_id');
            $tags = $request->input('tags');

            // Build base query for related products
            $query = Product::where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0);

            // If category ID is provided, get products from the same category
            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }

            // If tags are provided, get products with similar tags
            if ($tags) {
                $tagArray = array_filter(explode(',', $tags));
                if (!empty($tagArray)) {
                    $query->where(function ($q) use ($tagArray) {
                        foreach ($tagArray as $tag) {
                            if (trim($tag)) {
                                $q->orWhere('tags', 'like', '%' . trim($tag) . '%');
                            }
                        }
                    });
                }
            }

            // If no specific filters, get popular/trending products
            if (!$categoryId && !$tags) {
                $query->orderBy('num_of_sale', 'desc')
                    ->orderBy('rating', 'desc')
                    ->orderBy('created_at', 'desc');
            } else {
                // For filtered results, add some randomness to avoid showing same products
                $query->inRandomOrder();
            }

            // Get the products
            $relatedProducts = $query->take($limit * 2)->get();

            // If we don't have enough products, supplement with popular products
            if ($relatedProducts->count() < $limit) {
                $existingIds = $relatedProducts->pluck('id')->toArray();
                $additionalProducts = Product::where('published', 1)
                    ->where('approved', 1)
                    ->where('auction_product', 0)
                    ->whereNotIn('id', $existingIds)
                    ->orderBy('num_of_sale', 'desc')
                    ->orderBy('rating', 'desc')
                    ->take($limit - $relatedProducts->count())
                    ->get();

                $relatedProducts = $relatedProducts->concat($additionalProducts);
            }

            // Paginate the results manually
            $total = $relatedProducts->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $relatedProducts->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ],
                'filters' => [
                    'category_id' => $categoryId,
                    'tags' => $tags,
                    'applied_filters' => [
                        'has_category' => !empty($categoryId),
                        'has_tags' => !empty($tags),
                        'is_general' => empty($categoryId) && empty($tags)
                    ]
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch related products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get similar products to a specific product
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     */
    public function getSimilarProducts(string $id, Request $request): JsonResponse
    {
        try {
            $apiProductService = app(ApiProductService::class);
            $limit = min((int) $request->get('limit', 4), 20);

            // Validate product ID
            $product = Product::findOrFail($id);

            // Get similar products from service
            $similarProducts = $apiProductService->getSimilarProducts(
                $product->id,
                $limit
            );

            // Extract product IDs for the frontend
            $productIds = $similarProducts->pluck('id')->toArray();

            // Format response to match frontend expectation
            $result = [
                'ids' => $productIds,
                'count' => count($productIds)
            ];

            return $this->success( new ProductsResource($similarProducts)   );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch similar products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get trending products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTrendingProducts(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);

            // Get trending products from service
            $trendingProductService = new ApiTrendingProductService();
            $trendingProducts = $trendingProductService->getTrendingProducts($limit * 2);

            // Paginate the results manually
            $total = $trendingProducts->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $trendingProducts->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch trending products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get new arrival products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getNewArrivals(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);
            $days = min((int) $request->input('days', 30), 90); // Number of days to consider as "new arrivals"

            // Get new arrivals from service
            $newArrivalsService = new NewArrivalsProductService();
            $newArrivals = $newArrivalsService->getNewArrivals($limit * 2, $days);

            // Paginate the results manually
            $total = $newArrivals->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $newArrivals->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch new arrivals',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get best-selling products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBestSellers(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);
            $days = min((int) $request->input('days', 30), 365); // Number of days to consider for best sellers (0 for all time)

            // Get best sellers from service
            $bestSellersService = new BestSellersProductService();
            $bestSellers = $bestSellersService->getBestSellers($limit * 2, $days);

            // Paginate the results manually
            $total = $bestSellers->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $bestSellers->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch best sellers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get featured products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFeaturedProducts(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 20);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);

            // Query for featured products
            $featuredProducts = Product::where('published', 1)
                ->where('approved', 1)
                ->where('featured', 1)
                ->where('auction_product', 0)
                ->orderBy('created_at', 'desc')
                ->take($limit * 2)
                ->get();

            // Paginate the results manually
            $total = $featuredProducts->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $featuredProducts->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch featured products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get premium products
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPremiumOr_highMargin(Request $request)
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'limit' => 'nullable|integer|min:1|max:50',
                'search' => 'nullable|string|max:255',
                'category' => 'nullable|string|max:255',
                'brand' => 'nullable|string|max:255',
                'sort_by' => 'nullable|string|in:newest,price-low,price-high,rating,margin',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
                'min_rating' => 'nullable|integer|min:1|max:5',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Check if user has dropshipper role
            $user = auth()->user();
            /*if ($user->user_type != 'dropshipper') {
                return $this->error(
                    'UNAUTHORIZED',
                    'You do not have permission to access premium products',
                    '',
                    403
                );
            }*/
            $premiumProductsService=new PremiumProductsService();
            // Get premium products
            $products = $premiumProductsService->getPremiumOrHighMarginProducts($request->all());

            // Return response
            return $this->success(
                new PremiumProductCollection($products),
                'Premium products retrieved successfully'
            );
        } catch (\Exception $e) {

            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving premium products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get early access products
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function early_access_products(Request $request)
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'limit' => 'nullable|integer|min:1|max:50',
                'search' => 'nullable|string|max:255',
                'category' => 'nullable|string|max:255',
                'brand' => 'nullable|string|max:255',
                'sort_by' => 'nullable|string|in:newest,price-low,price-high,rating,expiry',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
                'min_rating' => 'nullable|integer|min:1|max:5',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Check if user has dropshipper role
            $user = auth()->user();
            /*if ($user->user_type != 'dropshipper') {
                return $this->error(
                    'UNAUTHORIZED',
                    'You do not have permission to access premium products',
                    '',
                    403
                );
            }*/
            $earlyAccessProductsService = new EarlyAccessProductsService();
            // Get early access products
            $products = $earlyAccessProductsService->getEarlyAccessProducts($request->all());

            // Return response
            return $this->success(
                new EarlyAccessProductCollection($products),
                'Early access products retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving early access products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get related products to a specific product
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     */
    public function getRelatedProducts(string $id, Request $request): JsonResponse
    {
        try {
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);

            // Get query parameters
            $limit = min((int) $request->input('limit', 6), 20);

            // Find the product
            $product = Product::findOrFail($id);

            // Get related products based on category
            $relatedProducts = Product::where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->where('published', 1)
                ->where('approved', 1)
                ->inRandomOrder()
                ->take($limit)
                ->get();

            // If we don't have enough related products by category, add some based on tags
            if ($relatedProducts->count() < $limit && $product->tags) {
                $productTags = explode(',', $product->tags);
                $additionalProducts = Product::where('id', '!=', $product->id)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->where(function ($query) use ($productTags) {
                        foreach ($productTags as $tag) {
                            if (trim($tag)) {
                                $query->orWhere('tags', 'like', '%' . trim($tag) . '%');
                            }
                        }
                    })
                    ->whereNotIn('id', $relatedProducts->pluck('id')->toArray())
                    ->inRandomOrder()
                    ->take($limit - $relatedProducts->count())
                    ->get();

                $relatedProducts = $relatedProducts->concat($additionalProducts);
            }

            $total = $relatedProducts->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $relatedProducts->forPage($page, $perPage);
            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch related products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get related products by ID
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     */
    public function getRelatedProductsId(string $id, Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 4), 20);

            // Find the product
            $product = Product::findOrFail($id);

            // Get related products based on category
            $relatedProducts = Product::where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->where('published', 1)
                ->where('approved', 1)
                ->inRandomOrder()
                ->take($limit)
                ->get();

            // If we don't have enough related products by category, add some based on tags
            if ($relatedProducts->count() < $limit && $product->tags) {
                $productTags = explode(',', $product->tags);
                $additionalProducts = Product::where('id', '!=', $product->id)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->where(function ($query) use ($productTags) {
                        foreach ($productTags as $tag) {
                            if (trim($tag)) {
                                $query->orWhere('tags', 'like', '%' . trim($tag) . '%');
                            }
                        }
                    })
                    ->whereNotIn('id', $relatedProducts->pluck('id')->toArray())
                    ->inRandomOrder()
                    ->take($limit - $relatedProducts->count())
                    ->get();

                $relatedProducts = $relatedProducts->concat($additionalProducts);
            }

            // Extract product IDs for the frontend
            $productIds = $relatedProducts->pluck('id')->toArray();

            // Format response to match frontend expectation
            $result = [
                'ids' => $productIds,
                'count' => count($productIds)
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch related products',
                $e->getMessage(),
                500
            );
        }
    }
    /**
     * Get on-sale products (products with discount)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getOnSaleProducts(Request $request): JsonResponse
    {
        try {
            // Get query parameters
            $limit = min((int) $request->input('limit', 10), 50);
            $page = max((int) $request->input('page', 1), 1);
            $perPage = min((int) $request->input('per_page', 10), 20);
            $minDiscountPercentage = max((int) $request->input('min_discount', 5), 1); // Minimum discount percentage

            // Get products with discount
            $onSaleProducts = Product::where('published', '1')
                ->where('auction_product', 0)
                ->where('approved', '1')
                ->where('discount', '>=', $minDiscountPercentage)
                ->orderBy('discount', 'desc')
                ->take($limit * 2)
                ->get();

            // Paginate the results manually
            $total = $onSaleProducts->count();
            $totalPages = ceil($total / $perPage);

            $paginatedProducts = $onSaleProducts->forPage($page, $perPage);

            $data = [
                'products' => new ProductsResource($paginatedProducts),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $totalPages,
                    'totalItems' => $total,
                    'itemsPerPage' => (int) $perPage,
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch on-sale products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get deal of the day product
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDealOfTheDay(Request $request): JsonResponse
    {
        try {
            $dealOfTheDayService = new DealOfTheDayService();
            $dealOfTheDay = $dealOfTheDayService->getDealOfTheDay();

            if (!$dealOfTheDay) {
                return $this->error(
                    'NOT_FOUND',
                    'No deal of the day available',
                    null,
                    404
                );
            }

            $data = [
                'product' => new ProductResource($dealOfTheDay),
                'validUntil' => $dealOfTheDayService->getDealExpiryTime()->format('Y-m-d H:i:s'),
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch deal of the day',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product suggestions for bulk order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBulkOrderSuggestions(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            // Parameters for filtering
            $limit = min((int) $request->input('limit', 10), 50);
            $category = $request->input('category');
            $brand = $request->input('brand');
            $price_min = $request->input('price_min');
            $price_max = $request->input('price_max');
            $sort = $request->input('sort', 'popular');

            // Start building the query
            $query = Product::where('published', 1)
                ->where('approved', 1)
                ->where('current_stock', '>', 0);

            // Apply filters
            if ($category) {
                $query->where('category_id', $category);
            }

            if ($brand) {
                $query->where('brand_id', $brand);
            }

            if ($price_min) {
                $query->where('unit_price', '>=', $price_min);
            }

            if ($price_max) {
                $query->where('unit_price', '<=', $price_max);
            }

            // Filter products suitable for bulk orders (e.g., has discount for bulk, etc.)
            $query->where(function($q) {
                $q->where('wholesale_price', '>', 0)
                    ->orWhere('min_qty', '>', 1)
                    ->orWhere('low_stock_quantity', '>', 10);
            });

            // Apply sorting
            switch ($sort) {
                case 'price_asc':
                    $query->orderBy('unit_price', 'asc');
                    break;
                case 'price_desc':
                    $query->orderBy('unit_price', 'desc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'popular':
                default:
                    $query->orderBy('num_of_sale', 'desc');
                    break;
            }

            // Get the products
            $products = $query->take($limit)->get();

            // Format response
            $suggestions = [];
            foreach ($products as $product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                $suggestions[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'thumbnail' => uploaded_asset($product->thumbnail_img),
                    'price' => $prices['displayPrice'],
                    'regularPrice' => $prices['regularPrice'],
                    'wholesalePrice' => (float) $product->wholesale_price,
                    'stockQuantity' => (int) $product->current_stock,
                    'minQuantity' => (int) $product->min_qty,
                    'sku' => $product->sku,
                    'rating' => $product->rating,
                    'sales' => (int) $product->num_of_sale,
                    'hasVariants' => $product->has_variant == 1,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2)
                ];
            }

            return $this->success(
                $suggestions,
                'Bulk order product suggestions retrieved successfully',
                200
            );

        } catch (\Exception $e) {
            Log::error('Error fetching bulk order suggestions: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to get product suggestions: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Get products for bulk ordering
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductsForBulkOrder(Request $request)
    {
        try {
            $perPage = $request->input('per_page', 12);
            $search = $request->input('search', '');
            $categoryId = $request->input('category', null);
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $priceMin = $request->input('price_min', null);
            $priceMax = $request->input('price_max', null);
            $inStock = $request->input('in_stock', null);

            // Base query for products
            $query = Product::query()
                ->where('published', 1)
                ->where('approved', 1);

            // Apply search filter
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('tags', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Apply category filter
            if (!empty($categoryId)) {
                $query->where('category_id', $categoryId);
            }

            // Apply price range filters
            if (!empty($priceMin)) {
                $query->where('unit_price', '>=', $priceMin);
            }
            if (!empty($priceMax)) {
                $query->where('unit_price', '<=', $priceMax);
            }

            // Apply stock filter
            if ($inStock !== null && $inStock !== '') {
                if ($inStock == '1') {
                    $query->where('current_stock', '>', 0);
                } else {
                    $query->where('current_stock', '<=', 0);
                }
            }

            // Apply sorting
            if ($sortBy === 'price') {
                $query->orderBy('unit_price', $sortOrder);
            } elseif ($sortBy === 'popularity') {
                $query->orderBy('num_of_sale', $sortOrder);
            } elseif ($sortBy === 'rating') {
                $query->orderBy('rating', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Get paginated products
            $products = $query->with(['category', 'brand'])->paginate($perPage);

            // Transform for API response
            $transformedProducts = $products->map(function($product) {
                $prices = ProductPriceHelper::getProductPrices($product);
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $prices['displayPrice'],
                    'regularPrice' => $prices['regularPrice'],
                    'discount' => $product->discount,
                    'discount_type' => $product->discount_type,
                    'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($regularPrice - $currentPrice) / $regularPrice * 100, 2),
                    'bulk_discount_percentage' => $product->bulk_discount_percentage ?? 0,
                    'min_bulk_quantity' => $product->min_bulk_quantity ?? 10,
                    'thumbnail' => uploaded_asset($product->thumbnail_img),
                    'current_stock' => $product->current_stock,
                    'rating' => $product->rating,
                    'rating_count' => $product->rating_count,
                    'category' => $product->category ? $product->category->name : '',
                    'brand' => $product->brand ? $product->brand->name : '',
                    'has_variations' => !empty($product->variations),
                    'is_digital' => $product->digital == 1,
                    'min_qty' => $product->min_qty ?? 1,
                    'tags' => $product->tags,
                    'description_short' => substr(strip_tags($product->description), 0, 200) . '...'
                ];
            });

            // Response structure
            $response = [
                'products' => $transformedProducts,
                'pagination' => [
                    'total' => $products->total(),
                    'count' => $products->count(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'total_pages' => $products->lastPage(),
                ],
                'filters' => [
                    'search' => $search,
                    'category' => $categoryId,
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                    'price_min' => $priceMin,
                    'price_max' => $priceMax,
                    'in_stock' => $inStock,
                ],
            ];

            return $this->success($response, 'Products for bulk order retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve products for bulk order: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to retrieve products for bulk order: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a specific product for bulk ordering
     *
     * @param Request $request
     * @param string $slug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductForBulkOrder(Request $request, $slug)
    {
        try {
            $product = Product::where('slug', $slug)
                ->where('published', 1)
                ->where('approved', 1)
                ->with(['category', 'brand', 'taxes'])
                ->first();

            if (!$product) {
                return $this->error(
                    'PRODUCT_NOT_FOUND',
                    'Product not found',
                    404
                );
            }
            $prices = ProductPriceHelper::getProductPrices($product);

            // Transform for API response
            $transformedProduct = [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $prices['displayPrice'],
                'regularPrice' => $prices['regularPrice'],
                'has_discount' => $product->discount > 0,
                'discount' => $product->discount,
                'discount_type' => $product->discount_type,
                'discount_percentage' => $product->discount_type === 'percent' ? $product->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
                'bulk_discount_percentage' => $product->bulk_discount_percentage ?? 0,
                'min_bulk_quantity' => $product->min_bulk_quantity ?? 10,
                'thumbnail' => uploaded_asset($product->thumbnail_img),
                'photos' => array_map(function($img) {
                    return uploaded_asset($img);
                }, explode(',', $product->photos)),
                'current_stock' => $product->current_stock,
                'rating' => $product->rating,
                'rating_count' => $product->rating_count,
                'category' => $product->category ? $product->category->name : '',
                'brand' => $product->brand ? $product->brand->name : '',
                'has_variations' => !empty($product->variations),
                'variations' => $product->variations ? json_decode($product->variations) : null,
                'is_digital' => $product->digital == 1,
                'min_qty' => $product->min_qty ?? 1,
                'tags' => $product->tags,
                'description' => $product->description,
                'shipping_days' => $product->est_shipping_days,
                'tax' => $product->taxes->map(function($tax) {
                    return [
                        'id' => $tax->id,
                        'name' => $tax->name,
                        'tax_rate' => $tax->tax_rate,
                    ];
                }),
                'bulk_pricing_tiers' => [
                    [
                        'min_qty' => 10,
                        'discount_percentage' => 5
                    ],
                    [
                        'min_qty' => 25,
                        'discount_percentage' => 10
                    ],
                    [
                        'min_qty' => 50,
                        'discount_percentage' => 15
                    ],
                    [
                        'min_qty' => 100,
                        'discount_percentage' => 20
                    ],
                ]
            ];

            return $this->success($transformedProduct, 'Product details for bulk order retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve product details for bulk order: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to retrieve product details for bulk order: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get enhanced product details including specifications, features, highlights, shipping info, etc.
     *
     * @param string $id Product ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEnhancedDetails(string $id): JsonResponse
    {
        try {
            $product = Product::where('id', $id)
                ->where('published', '1')
                ->where('auction_product', 0)
                ->where('approved', '1')
                ->first();

            if (!$product) {
                return $this->error(
                    'product_not_found',
                    'Product not found',
                    null,
                    404
                );
            }

            // Extract specifications from product attributes
            $specifications = [];
            if (!empty($product->choice_options)) {
                foreach ($product->choice_options as $option) {
                    if (isset($option['title']) && isset($option['options'])) {
                        $specifications[] = [
                            'label' => $option['title'],
                            'value' => is_array($option['options']) ? implode(', ', $option['options']) : $option['options']
                        ];
                    }
                }
            }

            // Add additional specifications from meta fields if available
            if (!empty($product->meta_specifications)) {
                $metaSpecs = json_decode($product->meta_specifications, true);
                if (is_array($metaSpecs)) {
                    foreach ($metaSpecs as $spec) {
                        if (isset($spec['key']) && isset($spec['value'])) {
                            $specifications[] = [
                                'label' => $spec['key'],
                                'value' => $spec['value']
                            ];
                        }
                    }
                }
            }

            // Extract features from product description or dedicated field
            $features = [];
            if (!empty($product->meta_features)) {
                $features = json_decode($product->meta_features, true) ?? [];
            } else if (!empty($product->features)) {
                // If features already exist as a field, use that
                $features = is_array($product->features) ? $product->features : [$product->features];
            }

            // Get highlights from dedicated field or extract from description
            $highlights = [];
            if (!empty($product->meta_highlights)) {
                $highlights = json_decode($product->meta_highlights, true) ?? [];
            }

            // Shipping information
            $shippingInfo = [
                'estimatedDelivery' => $product->meta_estimated_delivery ?? '3-5 days',
                'returnPolicy' => $product->meta_return_policy ?? '30 days',
                'freeShipping' => (bool) ($product->meta_free_shipping ?? false),
            ];

            // Warranty information
            $warranty = $product->meta_warranty ?? null;

            // Materials information
            $materials = [];
            if (!empty($product->meta_materials)) {
                $materials = json_decode($product->meta_materials, true) ?? [];
            }

            // Dimensions information
            $dimensions = null;
            if (!empty($product->meta_dimensions)) {
                $dimensionsData = json_decode($product->meta_dimensions, true);
                if (is_array($dimensionsData)) {
                    $dimensions = [
                        'width' => $dimensionsData['width'] ?? '',
                        'height' => $dimensionsData['height'] ?? '',
                        'depth' => $dimensionsData['depth'] ?? '',
                        'weight' => $dimensionsData['weight'] ?? '',
                    ];
                }
            }

            // Video URLs if available
            $videoUrls = [];
            if (!empty($product->meta_video_urls)) {
                $videoUrls = json_decode($product->meta_video_urls, true) ?? [];
            } else {
                // Check for product videos in the videos relation
                $productVideos = $product->product_videos;
                if ($productVideos && count($productVideos) > 0) {
                    foreach ($productVideos as $video) {
                        if (isset($video->url)) {
                            $videoUrls[] = $video->url;
                        }
                    }
                }
            }

            $data = [
                'specifications' => $specifications,
                'features' => $features,
                'highlights' => $highlights,
                'shippingInfo' => $shippingInfo,
                'warranty' => $warranty,
                'materials' => $materials,
                'dimensions' => $dimensions,
                'videoUrls' => $videoUrls
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch enhanced product details',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product FAQs
     *
     * @param string $id Product ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductFaqs(string $id): JsonResponse
    {
        try {
            $product = Product::where('id', $id)
                ->where('published', '1')
                ->where('auction_product', 0)
                ->where('approved', '1')
                ->first();

            if (!$product) {
                return $this->error(
                    'product_not_found',
                    'Product not found',
                    null,
                    404
                );
            }

            // Get FAQs from the related faqs table or meta field
            $faqs = [];

            // First check if product has meta_faqs field
            if (!empty($product->meta_faqs)) {
                $faqs = json_decode($product->meta_faqs, true) ?? [];
            }

            // If no FAQs found, get general FAQs from the FAQs table
            // that are related to the product's category
            if (empty($faqs) && $product->category_id) {
                // Get FAQs from Faq table that match this product's category
                $categoryFaqs = \App\Models\Faq::where('category_id', $product->category_id)
                    ->where('is_active', true)
                    ->orderBy('position')
                    ->limit(5)
                    ->get(['question', 'answer']);

                if ($categoryFaqs->count() > 0) {
                    foreach ($categoryFaqs as $faq) {
                        $faqs[] = [
                            'question' => $faq->question,
                            'answer' => $faq->answer
                        ];
                    }
                }
            }

            // If still no FAQs, add some generic product FAQs
            if (empty($faqs)) {
                $faqs = [
                    [
                        'question' => 'What is the warranty period for this product?',
                        'answer' => $product->meta_warranty ? $product->meta_warranty : 'Please contact customer support for warranty information.'
                    ],
                    [
                        'question' => 'How long does shipping take?',
                        'answer' => 'Standard shipping typically takes 3-5 business days, depending on your location.'
                    ],
                    [
                        'question' => 'Is this product eligible for return?',
                        'answer' => $product->hasReturnPolicy() ? 'Yes, this product can be returned within 30 days of purchase if unused and in original packaging.' : 'This product is not eligible for return due to hygiene reasons.'
                    ]
                ];
            }

            return $this->success(['faqs' => $faqs]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product FAQs',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product reviews with rating breakdown
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductReviews(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'slug' => 'required|exists:products,slug',
                'per_page' => 'integer|min:1|max:50',
                'page' => 'integer|min:1',
                'sort' => 'string|in:newest,oldest,highest_rating,lowest_rating,most_helpful',
            ]);

            if ($validator->fails()) {
                return $this->validation_error('Validation Error', 'Please provide valid parameters', $validator->errors()->messages(), 400);
            }

            $product = Product::where('slug', $request->slug)
                ->where('published', '1')
                ->where('auction_product', 0)
                ->where('approved', '1')
                ->first();

            if (!$product) {
                return $this->error(
                    'product_not_found',
                    'Product not found',
                    null,
                    404
                );
            }

            $perPage = min((int)($request->per_page ?? 10), 50);
            $page = max((int)($request->page ?? 1), 1);
            $sort = $request->sort ?? 'newest';

            // Get approved reviews for this product
            $reviewsQuery = Review::where('product_id', $product->id)
                ->where('status', 1);

            // Apply sorting
            switch ($sort) {
                case 'oldest':
                    $reviewsQuery->orderBy('created_at', 'asc');
                    break;
                case 'highest_rating':
                    $reviewsQuery->orderBy('rating', 'desc');
                    break;
                case 'lowest_rating':
                    $reviewsQuery->orderBy('rating', 'asc');
                    break;
                case 'most_helpful':
                    $reviewsQuery->orderBy('helpful_count', 'desc');
                    break;
                default:
                    $reviewsQuery->orderBy('created_at', 'desc');
                    break;
            }

            $reviews = $reviewsQuery->paginate($perPage, ['*'], 'page', $page);

            // Calculate rating breakdown
            $totalReviews = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->count();

            $ratingCounts = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->selectRaw('rating, count(*) as count')
                ->groupBy('rating')
                ->pluck('count', 'rating')
                ->toArray();

            $ratingBreakdown = [
                'five' => isset($ratingCounts[5]) ? ($ratingCounts[5] / $totalReviews) * 100 : 0,
                'four' => isset($ratingCounts[4]) ? ($ratingCounts[4] / $totalReviews) * 100 : 0,
                'three' => isset($ratingCounts[3]) ? ($ratingCounts[3] / $totalReviews) * 100 : 0,
                'two' => isset($ratingCounts[2]) ? ($ratingCounts[2] / $totalReviews) * 100 : 0,
                'one' => isset($ratingCounts[1]) ? ($ratingCounts[1] / $totalReviews) * 100 : 0,
            ];

            // Transform review data
            $reviewsData = [];
            foreach ($reviews as $review) {
                $user = $review->user;
                $images = [];

                // Get photos (already cast to array in Review model)
                if (!empty($review->photos)) {
                    $photoUrls = $review->photos;
                    if (is_array($photoUrls)) {
                        $images = $photoUrls;
                    }
                }

                $reviewsData[] = [
                    'id' => $review->id,
                    'userName' => $user ? $user->name : 'Anonymous',
                    'rating' => (float)$review->rating,
                    'date' => $review->created_at->format('Y-m-d'),
                    'content' => $review->comment,
                    'verified' => (bool)$review->verified_purchase,
                    'title' => $review->review_title,
                    'helpful' => (int)$review->helpful_count,
                    'avatar' => $user && $user->avatar ? upload_url($user->avatar) : null,
                    'images' => $images,
                ];
            }

            $data = [
                'reviews' => $reviewsData,
                'pagination' => [
                    'currentPage' => $reviews->currentPage(),
                    'totalPages' => $reviews->lastPage(),
                    'totalItems' => $reviews->total(),
                    'perPage' => $reviews->perPage(),
                ],
                'ratingBreakdown' => $ratingBreakdown
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product reviews',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product specifications by slug
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getProductSpecifications(Request $request): JsonResponse
    {
        try {
            // Validate request
            if (!$request->has('slug') || empty($request->slug)) {
                return $this->error(
                    'INVALID_REQUEST',
                    'Product slug is required',
                    null,
                    400
                );
            }

            // Get product by slug
            $product = Product::where('slug', $request->slug)
                ->where('published', 1)
                ->where('auction_product', 0)
                ->where('approved', 1)
                ->first();

            if (!$product) {
                return $this->error(
                    'PRODUCT_NOT_FOUND',
                    'Product not found',
                    null,
                    404
                );
            }

            // Extract specifications from product attributes
            $specifications = [];

            // Check choice options (for variants)
            if (!empty($product->choice_options)) {
                $choiceOptions = is_string($product->choice_options)
                    ? json_decode($product->choice_options, true)
                    : $product->choice_options;

                if (is_array($choiceOptions)) {
                    foreach ($choiceOptions as $option) {
                        if (isset($option['title']) && isset($option['options'])) {
                            $specifications[] = [
                                'label' => $option['title'],
                                'value' => is_array($option['options']) ? implode(', ', $option['options']) : $option['options']
                            ];
                        }
                    }
                }
            }

            // Add additional specifications from meta fields if available
            if (!empty($product->meta_specifications)) {
                $metaSpecs = json_decode($product->meta_specifications, true);
                if (is_array($metaSpecs)) {
                    foreach ($metaSpecs as $spec) {
                        if (isset($spec['key']) && isset($spec['value'])) {
                            $specifications[] = [
                                'label' => $spec['key'],
                                'value' => $spec['value']
                            ];
                        }
                    }
                }
            }

            // If no specifications found, add some basic product info as specifications
            if (empty($specifications)) {
                // Add brand if available
                if ($product->brand) {
                    $brand = $product->brand;
                    $specifications[] = [
                        'label' => 'Brand',
                        'value' => $brand->name ?? 'N/A'
                    ];
                }

                // Add category
                if ($product->category) {
                    $specifications[] = [
                        'label' => 'Category',
                        'value' => $product->category->name ?? 'N/A'
                    ];
                }

                // Add unit if available
                if ($product->unit) {
                    $specifications[] = [
                        'label' => 'Unit',
                        'value' => $product->unit
                    ];
                }

                // Add weight if available
                if ($product->weight) {
                    $specifications[] = [
                        'label' => 'Weight',
                        'value' => $product->weight . ' kg'
                    ];
                }

                // Add dimensions if available in meta_dimensions
                if (!empty($product->meta_dimensions)) {
                    $dimensions = json_decode($product->meta_dimensions, true);
                    if (is_array($dimensions)) {
                        foreach ($dimensions as $key => $value) {
                            $specifications[] = [
                                'label' => ucfirst($key),
                                'value' => $value
                            ];
                        }
                    }
                }
            }

            return $this->success([
                'specifications' => $specifications
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product specifications',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product stock status
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getStockStatus(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Calculate stock status
            $currentStock = $product->current_stock ?? 0;
            $lowStockThreshold = $product->low_stock_quantity ?? 5;

            $stockStatus = [
                'inStock' => $currentStock > 0,
                'quantity' => $currentStock,
                'lowStock' => $currentStock > 0 && $currentStock <= $lowStockThreshold,
                'backOrder' => false, // Set based on your business logic
                'stockLabel' => $this->getStockLabel($currentStock, $lowStockThreshold)
            ];

            return $this->success($stockStatus);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product stock status',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Helper method to get stock label text based on quantity
     */
    private function getStockLabel(int $quantity, int $lowStockThreshold): string
    {
        if ($quantity <= 0) {
            return 'Out of stock';
        } elseif ($quantity <= $lowStockThreshold) {
            return 'Low stock - only ' . $quantity . ' left';
        } else {
            return 'In stock';
        }
    }

    /**
     * Get product availability details
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getAvailability(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Get current stock from stocks table or product field
            $currentStock = $product->current_stock ?? 0;

            // Build availability data
            $availability = [
                'inStock' => $currentStock > 0,
                'quantity' => $currentStock,
                'maxPurchaseQuantity' => $product->max_purchase_qty ?? $currentStock,
                'restockDate' => $product->restock_date ?? null,
                'estimatedDeliveryDays' => [
                    'min' => $product->min_delivery_days ?? 2,
                    'max' => $product->max_delivery_days ?? 7
                ]
            ];

            // Get store availability if applicable
            if ($product->show_store_availability) {
                $availability['availableInStores'] = $this->getStoreAvailability($product->id);
            }

            return $this->success($availability);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product availability',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Helper method to get store availability
     */
    private function getStoreAvailability(string $productId): array
    {
        // This would typically query a store_inventory table
        // For now, returning placeholder data
        return [
            [
                'storeId' => '1',
                'storeName' => 'Main Store',
                'quantity' => 5
            ],
            [
                'storeId' => '2',
                'storeName' => 'Downtown Branch',
                'quantity' => 3
            ]
        ];
    }

    /**
     * Get product reviews summary
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getReviewsSummary(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Get reviews for this product
            $reviews = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->get();

            $totalReviews = $reviews->count();
            $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;

            // Calculate rating distribution
            $ratingDistribution = [
                '5' => 0, '4' => 0, '3' => 0, '2' => 0, '1' => 0
            ];

            foreach ($reviews as $review) {
                $rating = (string) $review->rating;
                if (isset($ratingDistribution[$rating])) {
                    $ratingDistribution[$rating]++;
                }
            }

            // Format as percentages
            $ratingPercentages = [];
            foreach ($ratingDistribution as $rating => $count) {
                $ratingPercentages[$rating] = $totalReviews > 0
                    ? round(($count / $totalReviews) * 100, 1)
                    : 0;
            }

            // Get recent reviews
            $recentReviews = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get()
                ->map(function ($review) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'comment' => $review->comment,
                        'date' => $review->created_at->format('Y-m-d'),
                        'reviewer' => $review->user ? $review->user->name : 'Anonymous'
                    ];
                });

            $summary = [
                'averageRating' => round($averageRating, 1),
                'totalReviews' => $totalReviews,
                'ratingDistribution' => $ratingDistribution,
                'ratingPercentages' => $ratingPercentages,
                'recentReviews' => $recentReviews
            ];

            return $this->success($summary);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product reviews summary',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get product reviews with pagination
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     */
    public function getReviews(string $id, Request $request): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Get query parameters
            $perPage = min((int) $request->input('per_page', 10), 50);
            $page = max((int) $request->input('page', 1), 1);
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $minRating = (int) $request->input('min_rating', 1);
            $maxRating = (int) $request->input('max_rating', 5);

            // Build query with user relationship
            $reviewsQuery = Review::with('user')
                ->where('product_id', $product->id)
                ->where('status', 1)
                ->whereBetween('rating', [$minRating, $maxRating]);

            // Apply sorting
            switch ($sortBy) {
                case 'rating':
                    $reviewsQuery->orderBy('rating', $sortOrder);
                    break;
                case 'helpful':
                    $reviewsQuery->orderBy('helpful_count', $sortOrder);
                    break;
                default:
                    $reviewsQuery->orderBy('created_at', $sortOrder);
            }

            $reviews = $reviewsQuery->paginate($perPage);

            // Transform reviews
            $formattedReviews = $reviews->map(function ($review) {
                $images = [];
                if (!empty($review->photos)) {
                    // photos field is already cast to array in Review model
                    $photos = $review->photos;
                    if (is_array($photos)) {
                        foreach ($photos as $photo) {
                            $images[] = uploaded_asset($photo);
                        }
                    }
                }

                // Get videos if available
                $videos = [];
                if (!empty($review->videos)) {
                    $reviewVideos = $review->videos;
                    if (is_array($reviewVideos)) {
                        foreach ($reviewVideos as $video) {
                            $videos[] = uploaded_asset($video);
                        }
                    }
                }

                // Get tags if available
                $tags = [];
                if (!empty($review->tags)) {
                    $reviewTags = $review->tags;
                    if (is_array($reviewTags)) {
                        $tags = $reviewTags;
                    } elseif (is_string($reviewTags)) {
                        $tags = json_decode($reviewTags, true) ?: [];
                    }
                }

                return [
                    'id' => $review->id,
                    'rating' => (int) $review->rating,
                    'title' => $review->title ?? '',
                    'review_title' => $review->title ?? $review->review_title ?? '',
                    'comment' => $review->comment,
                    'date' => $review->created_at->format('Y-m-d'),
                    'created_at' => $review->created_at->toISOString(),
                    'status' => (int) $review->status,
                    'reviewer' => [
                        'id' => $review->user_id,
                        'name' => $review->user ? $review->user->name : 'Anonymous',
                        'email' => $review->user ? $review->user->email : null,
                        'avatar' => $review->user && $review->user->avatar ? uploaded_asset($review->user->avatar) : null
                    ],
                    'user' => $review->user ? [
                        'id' => $review->user->id,
                        'name' => $review->user->name,
                        'email' => $review->user->email,
                        'avatar' => $review->user->avatar ? uploaded_asset($review->user->avatar) : null
                    ] : null,
                    'reviewer_email' => $review->user ? $review->user->email : null,
                    'verified' => (bool) $review->verified_purchase,
                    'helpful_count' => (int) $review->helpful_count,
                    'images' => $images,
                    'photos' => $images, // Alias for compatibility
                    'review_videos' => $videos,
                    'tags' => $tags
                ];
            });

            $response = [
                'reviews' => $formattedReviews,
                'pagination' => [
                    'total' => $reviews->total(),
                    'per_page' => $reviews->perPage(),
                    'current_page' => $reviews->currentPage(),
                    'last_page' => $reviews->lastPage(),
                    'from' => $reviews->firstItem(),
                    'to' => $reviews->lastItem()
                ]
            ];

            return $this->success($response);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product reviews',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get review count for a product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getReviewsCount(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            $totalReviews = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->count();

            return $this->success(['count' => $totalReviews]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product reviews count',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get average rating for a product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getAverageRating(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            $averageRating = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->avg('rating') ?? 0;

            return $this->success(['average' => round($averageRating, 1)]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product average rating',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get rating distribution for a product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getRatingDistribution(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Get rating distribution
            $distribution = Review::where('product_id', $product->id)
                ->where('status', 1)
                ->selectRaw('rating, COUNT(*) as count')
                ->groupBy('rating')
                ->pluck('count', 'rating')
                ->toArray();

            // Ensure all ratings are represented
            $ratingDistribution = [];
            for ($i = 1; $i <= 5; $i++) {
                $ratingDistribution[$i] = $distribution[$i] ?? 0;
            }

            return $this->success(['distribution' => $ratingDistribution]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product rating distribution',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get quick view data for a product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getQuickView(string $id): JsonResponse
    {
        try {
            $product = Product::findOrFail($id);

            // Check if product is available for quick view
            if (!$product->published || !$product->approved) {
                return $this->error(
                    'PRODUCT_NOT_AVAILABLE',
                    'Product is not available for quick view',
                    null,
                    404
                );
            }

            // Record view if user is authenticated
            if (auth('sanctum')->user()) {
                $apiProductService = new ApiProductService();
                $apiProductService->storeRecentlyViewedProduct(auth('sanctum')->user()->id, $product->id);
            }

            // Format the quick view data with only essential information
            $quickViewData = new ProductResource($product);

            return $this->success($quickViewData);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product quick view data',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Helper function to get product videos
     */
    private function getProductVideos($product): array
    {
        $videos = [];

        // Try to get videos from meta_video_urls field
        if (!empty($product->meta_video_urls)) {
            $videoUrls = json_decode($product->meta_video_urls, true);
            if (is_array($videoUrls)) {
                $videos = $videoUrls;
            }
        }

        // If no videos in meta field, check video_link field
        if (empty($videos) && !empty($product->video_link)) {
            $videos[] = $product->video_link;
        }

        // Also check product_videos relationship if available
        if (empty($videos) && method_exists($product, 'product_videos')) {
            $productVideos = $product->product_videos;
            if ($productVideos && count($productVideos) > 0) {
                foreach ($productVideos as $video) {
                    if (isset($video->url)) {
                        $videos[] = $video->url;
                    }
                }
            }
        }

        return $videos;
    }

    /**
     * Get product variants
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getProductVariants(string $id): JsonResponse
    {
        try {
            $product = Product::with(['stocks', 'choice_options'])
                ->where('id', $id)
                ->where('published', '1')
                ->where('approved', '1')
                ->first();

            if (!$product) {
                return $this->error(
                    'PRODUCT_NOT_FOUND',
                    'Product not found',
                    null,
                    404
                );
            }

            $variants = [];

            // Get choice options
            $choiceOptions = [];
            if (!empty($product->choice_options)) {
                $options = is_string($product->choice_options)
                    ? json_decode($product->choice_options, true)
                    : $product->choice_options;

                if (is_array($options)) {
                    foreach ($options as $option) {
                        if (isset($option['title']) && isset($option['options'])) {
                            $choiceOptions[$option['title']] = $option['options'];
                        }
                    }
                }
            }

            // Get variant combinations
            if (!empty($product->variations)) {
                $variations = is_string($product->variations)
                    ? json_decode($product->variations, true)
                    : $product->variations;

                if (is_array($variations)) {
                    foreach ($variations as $variation) {
                        $variantData = [
                            'id' => $variation['id'] ?? null,
                            'sku' => $variation['sku'] ?? null,
                            'price' => (float)($variation['price'] ?? $product->unit_price),
                            'stock_quantity' => (int)($variation['qty'] ?? 0),
                            'attributes' => $variation['attributes'] ?? [],
                            'image' => isset($variation['image']) ? uploaded_asset($variation['image']) : null
                        ];

                        // Add discount information if available
                        if (isset($variation['discount']) && $variation['discount'] > 0) {
                            $variantData['discount'] = [
                                'amount' => (float)$variation['discount'],
                                'type' => $variation['discount_type'] ?? 'amount',
                                'end_date' => $variation['discount_end_date'] ?? null
                            ];
                        }

                        $variants[] = $variantData;
                    }
                }
            }

            $data = [
                'has_variants' => !empty($variants),
                'choice_options' => $choiceOptions,
                'variants' => $variants,
                'default_variant' => [
                    'price' => (float)$product->unit_price,
                    'stock_quantity' => (int)$product->current_stock,
                    'sku' => $product->sku
                ]
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            \Log::error('Error getting product variants: ' . $e->getMessage(), [
                'product_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch product variants',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get detailed stock status for a product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getDetailedStockStatus(string $id): JsonResponse
    {
        try {
            $product = Product::with(['stocks'])
                ->where('id', $id)
                ->where('published', '1')
                ->where('approved', '1')
                ->first();

            if (!$product) {
                return $this->error(
                    'PRODUCT_NOT_FOUND',
                    'Product not found',
                    null,
                    404
                );
            }

            // Calculate total stock across all variants
            $totalStock = 0;
            $variantStock = [];

            // Check if product has variants
            if ($product->stocks && $product->stocks->count() > 0) {
                foreach ($product->stocks as $stock) {
                    $totalStock += $stock->qty;
                    $variantStock[] = [
                        'variant' => $stock->variant,
                        'quantity' => $stock->qty,
                        'sku' => $stock->sku,
                        'low_stock' => $stock->qty <= ($product->low_stock_quantity ?? 5)
                    ];
                }
            } else {
                $totalStock = $product->current_stock;
            }

            // Get stock thresholds
            $lowStockThreshold = $product->low_stock_quantity ?? 5;
            $criticalStockThreshold = $product->critical_stock_quantity ?? 2;

            // Determine stock status
            $stockStatus = $this->determineStockStatus($totalStock, $lowStockThreshold, $criticalStockThreshold);

            // Get restock information
            $restockInfo = null;
            if ($product->restock_date) {
                $restockInfo = [
                    'expected_date' => $product->restock_date,
                    'quantity' => $product->restock_quantity ?? 0
                ];
            }

            $data = [
                'stock_status' => $stockStatus,
                'total_stock' => $totalStock,
                'has_variants' => !empty($variantStock),
                'variant_stock' => $variantStock,
                'thresholds' => [
                    'low_stock' => $lowStockThreshold,
                    'critical_stock' => $criticalStockThreshold
                ],
                'restock_info' => $restockInfo,
                'max_purchase_quantity' => $product->max_purchase_qty ?? $totalStock,
                'min_purchase_quantity' => $product->min_purchase_qty ?? 1,
                'allow_backorders' => (bool)($product->allow_backorder ?? false),
                'inventory_tracking' => (bool)($product->track_inventory ?? true)
            ];

            return $this->success($data);

        } catch (\Exception $e) {
            \Log::error('Error getting stock status: ' . $e->getMessage(), [
                'product_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch stock status',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Helper method to determine stock status
     */
    private function determineStockStatus(int $quantity, int $lowThreshold, int $criticalThreshold): array
    {
        if ($quantity <= 0) {
            return [
                'status' => 'out_of_stock',
                'label' => 'Out of stock',
                'level' => 'critical'
            ];
        } elseif ($quantity <= $criticalThreshold) {
            return [
                'status' => 'critical_stock',
                'label' => 'Very low stock - only ' . $quantity . ' left',
                'level' => 'critical'
            ];
        } elseif ($quantity <= $lowThreshold) {
            return [
                'status' => 'low_stock',
                'label' => 'Low stock - only ' . $quantity . ' left',
                'level' => 'warning'
            ];
        } else {
            return [
                'status' => 'in_stock',
                'label' => 'In stock',
                'level' => 'normal'
            ];
        }
    }

    /**
     * Update product rating based on approved reviews
     *
     * @param int $productId
     * @return void
     */
    private function updateProductRating(int $productId): void
    {
        try {
            // Calculate average rating from approved reviews
            $averageRating = Review::where('product_id', $productId)
                ->where('status', 1) // Only approved reviews
                ->avg('rating');

            // Count total approved reviews
            $totalReviews = Review::where('product_id', $productId)
                ->where('status', 1)
                ->count();

            // Update product rating and review count
            Product::where('id', $productId)->update([
                'rating' => $averageRating ? round($averageRating, 2) : 0,
                'num_of_reviews' => $totalReviews
            ]);

            \Log::info('Product rating updated', [
                'product_id' => $productId,
                'average_rating' => $averageRating,
                'total_reviews' => $totalReviews
            ]);

        } catch (\Exception $e) {
            \Log::error('Error updating product rating: ' . $e->getMessage(), [
                'product_id' => $productId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Submit a product review
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     */
    public function submitProductReview(string $id, Request $request): JsonResponse
    {
        try {
            // Debug: Log incoming request
            \Log::info('=== Product Review Submission Debug ===', [
                'product_id' => $id,
                'request_method' => $request->method(),
                'content_type' => $request->header('Content-Type'),
                'all_data' => $request->all(),
                'files' => $request->allFiles(),
                'has_files' => $request->hasFile('photos') || $request->hasFile('review_videos'),
            ]);

            $product = Product::findOrFail($id);
            \Log::info('Product found:', ['product_id' => $product->id, 'product_name' => $product->name]);

            $user = auth('sanctum')->user();

            \Log::info('Authentication check:', [
                'user_authenticated' => (bool) $user,
                'user_id' => $user->id ?? null,
                'user_email' => $user->email ?? null,
                'auth_guard' => 'sanctum'
            ]);

            if (!$user) {
                \Log::warning('User not authenticated for review submission');
                return $this->error(
                    'UNAUTHORIZED',
                    'User not authenticated',
                    null,
                    401
                );
            }

            // Check if user already reviewed this product
            $existingReview = Review::where('product_id', $product->id)
                ->where('user_id', $user->id)
                ->first();

            if ($existingReview) {
                \Log::warning('Duplicate review attempt:', [
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'existing_review_id' => $existingReview->id
                ]);
                return $this->error(
                    'DUPLICATE_REVIEW',
                    'You have already reviewed this product',
                    null,
                    409
                );
            }

            // Validation rules
            $rules = [
                'rating' => 'required|integer|min:1|max:5',
                'review_title' => 'required|string|max:255',
                'comment' => 'required|string',
                'photos' => 'nullable|array',
                'photos.*' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg|max:2048',
                'review_videos' => 'nullable|array',
                'review_videos.*' => 'nullable|file|mimes:mp4,avi,mov,wmv|max:10240',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50',
                'productId' => 'sometimes|string|max:255', // Made optional since we have $id from route
            ];

            \Log::info('Validation rules:', $rules);

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                \Log::error('Validation failed:', [
                    'errors' => $validator->errors()->toArray(),
                    'input' => $request->all()
                ]);
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            \Log::info('Validation passed, processing files...');

            // Initialize AizUploadUtility for proper file handling
            $uploadUtility = new \App\Utility\ApiAizUploadUtility();

            // Handle photo uploads using AizUploadUtility
            $photosIds = '';
            if ($request->hasFile('photos')) {
                \Log::info('Processing photo uploads:', ['count' => count($request->file('photos'))]);
                $photoResult = $uploadUtility->multipleFileUpload($request->file('photos'), $user->id);
                if ($photoResult && isset($photoResult['files'])) {
                    $photosIds = $photoResult['files'];
                    \Log::info('Photos uploaded successfully:', ['upload_ids' => $photosIds]);
                }
            }

            // Handle video uploads using AizUploadUtility
            $videosIds = '';
            if ($request->hasFile('review_videos')) {
                \Log::info('Processing video uploads:', ['count' => count($request->file('review_videos'))]);
                $videoResult = $uploadUtility->multipleFileUpload($request->file('review_videos'), $user->id);
                if ($videoResult && isset($videoResult['files'])) {
                    $videosIds = $videoResult['files'];
                    \Log::info('Videos uploaded successfully:', ['upload_ids' => $videosIds]);
                }
            }

            $reviewData = [
                'product_id' => $product->id,
                'user_id' => $user->id,
                'rating' => $request->input('rating'),
                'review_title' => $request->input('review_title'),
                'comment' => $request->input('comment'),
                'photos' => $photosIds, // Store upload IDs, not paths
                'review_videos' => $videosIds, // Store upload IDs, not paths
                'tags' => !empty($request->input('tags')) ? json_encode($request->input('tags')) : null,
                'status' => 1, // Auto-approve for now (can be changed based on business logic)
                'viewed' => false,
                'verified' => false,
                'helpful_count' => 0
            ];

            \Log::info('Creating review with data:', $reviewData);

            $review = Review::create($reviewData);

            \Log::info('Review created successfully:', ['review_id' => $review->id]);

            // Update product rating after creating review
            $this->updateProductRating($product->id);

            // Load relationships for response
            $review->load(['user:id,name,email', 'product:id,name,rating,num_of_reviews']);

            // Get uploaded photo URLs for response
            $photoUrls = [];
            if (!empty($photosIds)) {
                $photoIds = explode(',', $photosIds);
                $uploads = \App\Models\Upload::whereIn('id', $photoIds)->get();
                foreach ($uploads as $upload) {
                    $photoUrls[] = uploaded_asset($upload->file_name);
                }
            }

            // Get uploaded video URLs for response
            $videoUrls = [];
            if (!empty($videosIds)) {
                $videoIds = explode(',', $videosIds);
                $uploads = \App\Models\Upload::whereIn('id', $videoIds)->get();
                foreach ($uploads as $upload) {
                    $videoUrls[] = uploaded_asset($upload->file_name);
                }
            }

            $response = [
                'id' => $review->id,
                'status' => 'success',
                'message' => 'Review submitted successfully',
                'review' => [
                    'id' => $review->id,
                    'rating' => $review->rating,
                    'review_title' => $review->review_title,
                    'comment' => $review->comment,
                    'photos' => $photoUrls,
                    'review_videos' => $videoUrls,
                    'tags' => !empty($review->tags) ? json_decode($review->tags, true) : [],
                    'status' => $review->status,
                    'created_at' => $review->created_at->toISOString(),
                    'user' => [
                        'id' => $review->user->id,
                        'name' => $review->user->name,
                        'email' => $review->user->email,
                    ]
                ],
                'product_updated_rating' => [
                    'average_rating' => $review->product->rating,
                    'total_reviews' => $review->product->num_of_reviews
                ]
            ];

            \Log::info('Review submission successful:', ['response' => $response]);

            return $this->success($response);

        } catch (\Exception $e) {
            \Log::error('Error submitting product review: ' . $e->getMessage(), [
                'product_id' => $id,
                'user_id' => $user->id ?? null,
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to submit product review: ' . $e->getMessage(),
                config('app.debug') ? $e->getTrace() : null,
                500
            );
        }
    }

    /**
     * Recalculate ratings for all products (admin function)
     * This can be used to fix any inconsistent product ratings
     *
     * @return JsonResponse
     */
    public function recalculateAllProductRatings(): JsonResponse
    {
        try {
            $user = auth('sanctum')->user();

            // Check if user has admin privileges (adjust according to your role system)
            if (!$user || !in_array($user->user_type, ['admin', 'staff'])) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You do not have permission to perform this action',
                    null,
                    403
                );
            }

            $updatedCount = 0;
            $totalProducts = 0;

            // Process all products in chunks to avoid memory issues
            Product::where('published', 1)
                ->where('approved', 1)
                ->chunk(100, function ($products) use (&$updatedCount, &$totalProducts) {
                    foreach ($products as $product) {
                        $totalProducts++;

                        // Calculate average rating from approved reviews
                        $averageRating = Review::where('product_id', $product->id)
                            ->where('status', 1)
                            ->avg('rating');

                        // Count total approved reviews
                        $totalReviews = Review::where('product_id', $product->id)
                            ->where('status', 1)
                            ->count();

                        // Update if there's a change
                        $newRating = $averageRating ? round($averageRating, 2) : 0;

                        if ($product->rating != $newRating || $product->num_of_reviews != $totalReviews) {
                            Product::where('id', $product->id)->update([
                                'rating' => $newRating,
                                'num_of_reviews' => $totalReviews
                            ]);
                            $updatedCount++;
                        }
                    }
                });

            return $this->success([
                'message' => 'Product ratings recalculated successfully',
                'total_products_processed' => $totalProducts,
                'products_updated' => $updatedCount
            ]);

        } catch (\Exception $e) {
            \Log::error('Error recalculating product ratings: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                'SERVER_ERROR',
                'Failed to recalculate product ratings',
                $e->getMessage(),
                500
            );
        }
    }
}
