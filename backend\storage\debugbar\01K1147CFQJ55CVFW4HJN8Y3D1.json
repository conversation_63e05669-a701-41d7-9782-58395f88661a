{"__meta": {"id": "01K1147CFQJ55CVFW4HJN8Y3D1", "datetime": "2025-07-25 08:46:29", "utime": **********.495845, "method": "GET", "uri": "/buzfi-new-backend/api/v3/products/featured", "ip": "::1"}, "messages": {"count": 22, "messages": [{"message": "[08:46:28] LOG.info: OptionalAuth middleware - Start {\n    \"has_token\": false,\n    \"token_preview\": null,\n    \"token_source\": \"none\",\n    \"path\": \"api\\/v3\\/products\\/featured\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.807291, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:28] LOG.info: OptionalAuth middleware - Final state {\n    \"auth_check\": false,\n    \"auth_id\": null,\n    \"guard_check\": false,\n    \"guard_id\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.837117, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:28] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.969713, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:28] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31938", "message_html": null, "is_string": false, "label": "info", "time": **********.969808, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.155438, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31937", "message_html": null, "is_string": false, "label": "info", "time": **********.155515, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31936", "message_html": null, "is_string": false, "label": "info", "time": **********.202373, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31936", "message_html": null, "is_string": false, "label": "info", "time": **********.202469, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31935", "message_html": null, "is_string": false, "label": "info", "time": **********.246018, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31935", "message_html": null, "is_string": false, "label": "info", "time": **********.246091, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31934", "message_html": null, "is_string": false, "label": "info", "time": **********.286253, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31934", "message_html": null, "is_string": false, "label": "info", "time": **********.286324, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31933", "message_html": null, "is_string": false, "label": "info", "time": **********.322819, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31933", "message_html": null, "is_string": false, "label": "info", "time": **********.322896, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31932", "message_html": null, "is_string": false, "label": "info", "time": **********.358849, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31932", "message_html": null, "is_string": false, "label": "info", "time": **********.358937, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31931", "message_html": null, "is_string": false, "label": "info", "time": **********.392013, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31931", "message_html": null, "is_string": false, "label": "info", "time": **********.392119, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31930", "message_html": null, "is_string": false, "label": "info", "time": **********.425677, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31930", "message_html": null, "is_string": false, "label": "info", "time": **********.425776, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getWishlistStatus: No authenticated user for product 31929", "message_html": null, "is_string": false, "label": "info", "time": **********.459427, "xdebug_link": null, "collector": "log"}, {"message": "[08:46:29] LOG.info: ProductResource.getCompareStatus: No authenticated user for product 31929", "message_html": null, "is_string": false, "label": "info", "time": **********.459518, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 5, "start": **********.582072, "end": **********.495873, "duration": 0.9138009548187256, "duration_str": "914ms", "measures": [{"label": "Booting", "start": **********.582072, "relative_start": 0, "end": **********.731918, "relative_end": **********.731918, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.731924, "relative_start": 0.*****************, "end": **********.495874, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "764ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.739433, "relative_start": 0.*****************, "end": **********.742227, "relative_end": **********.742227, "duration": 0.002794027328491211, "duration_str": "2.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.492688, "relative_start": 0.****************, "end": **********.492929, "relative_end": **********.492929, "duration": 0.0002410411834716797, "duration_str": "241μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.494712, "relative_start": 0.****************, "end": **********.494747, "relative_end": **********.494747, "duration": 3.4809112548828125e-05, "duration_str": "35μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 176, "nb_statements": 176, "nb_visible_statements": 176, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.35394000000000025, "accumulated_duration_str": "354ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 76 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `products` where `published` = 1 and `approved` = 1 and `featured` = 1 and `auction_product` = 0 order by `created_at` desc limit 20", "type": "query", "params": [], "bindings": [1, 1, 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.839483, "duration": 0.033170000000000005, "duration_str": "33.17ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:820", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=820", "ajax": false, "filename": "ApiProductController.php", "line": "820"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 9.372}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (31890, 31920, 31921, 31922, 31923, 31924, 31925, 31926, 31927, 31928, 31929, 31930, 31931, 31932, 31933, 31934, 31935, 31936, 31937, 31938)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.875052, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:820", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=820", "ajax": false, "filename": "ApiProductController.php", "line": "820"}, "connection": "buzfi", "explain": null, "start_percent": 9.372, "width_percent": 0.667}, {"sql": "select * from `product_taxes` where `product_taxes`.`product_id` in (31890, 31920, 31921, 31922, 31923, 31924, 31925, 31926, 31927, 31928, 31929, 31930, 31931, 31932, 31933, 31934, 31935, 31936, 31937, 31938)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.878628, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:820", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=820", "ajax": false, "filename": "ApiProductController.php", "line": "820"}, "connection": "buzfi", "explain": null, "start_percent": 10.038, "width_percent": 0.622}, {"sql": "select * from `uploads` where `uploads`.`id` in (********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********, ********) and `uploads`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.882311, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:820", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=820", "ajax": false, "filename": "ApiProductController.php", "line": "820"}, "connection": "buzfi", "explain": null, "start_percent": 10.66, "width_percent": 0.178}, {"sql": "select * from `suppliers` where `suppliers`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.883789, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ApiProductController.php:820", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiProductController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiProductController.php", "line": 820}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=820", "ajax": false, "filename": "ApiProductController.php", "line": "820"}, "connection": "buzfi", "explain": null, "start_percent": 10.838, "width_percent": 0.082}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.913003, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 10.92, "width_percent": 0.141}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028356' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028356"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.9143429, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 11.061, "width_percent": 0.062}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31938 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31938, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.997495, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 11.123, "width_percent": 0.164}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0089722, "duration": 0.00782, "duration_str": "7.82ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 11.287, "width_percent": 2.209}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.027742, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 13.497, "width_percent": 0.158}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.043445, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 13.655, "width_percent": 0.277}, {"sql": "select * from `uploads` where `uploads`.`id` = '70' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["70"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0453732, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 13.932, "width_percent": 0.076}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.0463939, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 14.008, "width_percent": 0.054}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028393' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028393"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.047398, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 14.062, "width_percent": 0.054}, {"sql": "select * from `uploads` where `uploads`.`id` = '10028396' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10028396"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.048194, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 14.115, "width_percent": 0.048}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31938 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31938], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.1074252, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 14.163, "width_percent": 0.277}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.109432, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 14.44, "width_percent": 1.608}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.11571, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 16.048, "width_percent": 1.602}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.122088, "duration": 0.00588, "duration_str": "5.88ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 17.65, "width_percent": 1.661}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31938 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31938, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.128803, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 19.311, "width_percent": 0.093}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31938 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31938], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1297278, "duration": 0.00583, "duration_str": "5.83ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 19.404, "width_percent": 1.647}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1510048, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 21.052, "width_percent": 0.316}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.152827, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 21.368, "width_percent": 0.059}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.153526, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 21.427, "width_percent": 0.048}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.154356, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 21.475, "width_percent": 0.062}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31937 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31937, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1557448, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 21.538, "width_percent": 0.073}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.156482, "duration": 0.00613, "duration_str": "6.13ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 21.611, "width_percent": 1.732}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.163295, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 23.343, "width_percent": 0.113}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.164346, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 23.456, "width_percent": 0.192}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.166044, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 23.648, "width_percent": 0.065}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025871' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025871"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.167206, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 23.713, "width_percent": 0.057}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025872' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025872"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.168082, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 23.77, "width_percent": 0.054}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025873' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025873"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1688669, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 23.823, "width_percent": 0.059}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31937 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31937], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.1697328, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 23.883, "width_percent": 0.071}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.170606, "duration": 0.00824, "duration_str": "8.24ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 23.953, "width_percent": 2.328}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.179648, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 26.281, "width_percent": 1.65}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.1861398, "duration": 0.00637, "duration_str": "6.37ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 27.931, "width_percent": 1.8}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31937 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31937, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.193169, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 29.731, "width_percent": 0.076}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31937 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31937], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.194053, "duration": 0.00614, "duration_str": "6.14ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 29.807, "width_percent": 1.735}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.201081, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 31.542, "width_percent": 0.079}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31936 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31936, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.20276, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 31.621, "width_percent": 0.071}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31936 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31936], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2035599, "duration": 0.0065, "duration_str": "6.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 31.692, "width_percent": 1.836}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.210649, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 33.528, "width_percent": 0.096}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.211669, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 33.624, "width_percent": 0.17}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2131119, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 33.794, "width_percent": 0.071}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025866' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025866"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2141309, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 33.864, "width_percent": 0.057}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025867' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025867"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.214889, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 33.921, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025868' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025868"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.215608, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 33.969, "width_percent": 0.057}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025869' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025869"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.216351, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 34.026, "width_percent": 0.048}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31936 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31936], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.21703, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 34.074, "width_percent": 0.062}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31936 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31936], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2177489, "duration": 0.005679999999999999, "duration_str": "5.68ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 34.136, "width_percent": 1.605}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31936 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31936], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.22384, "duration": 0.00562, "duration_str": "5.62ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 35.741, "width_percent": 1.588}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31936 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31936], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.229991, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 37.328, "width_percent": 1.562}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31936 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31936, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.235931, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 38.891, "width_percent": 0.059}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31936 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31936], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.236562, "duration": 0.00628, "duration_str": "6.28ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 38.95, "width_percent": 1.774}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2433329, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 40.724, "width_percent": 0.054}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.243922, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 40.778, "width_percent": 0.042}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.244452, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 40.82, "width_percent": 0.048}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.24514, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 40.869, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31935 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31935, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.246306, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 40.919, "width_percent": 0.068}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31935 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31935], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.246927, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 40.987, "width_percent": 1.548}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.252794, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 42.535, "width_percent": 0.048}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.2533681, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 42.583, "width_percent": 0.127}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.254421, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 42.711, "width_percent": 0.051}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025861' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025861"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.255333, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 42.761, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025862' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025862"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.256046, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 42.81, "width_percent": 0.045}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025863' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025863"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.256813, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 42.855, "width_percent": 0.082}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025864' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025864"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.257772, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 42.937, "width_percent": 0.065}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31935 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31935], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.258533, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 43.002, "width_percent": 0.079}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31935 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31935], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.259307, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 43.081, "width_percent": 1.551}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31935 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31935], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.265167, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 44.632, "width_percent": 1.562}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31935 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31935], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.271082, "duration": 0.0056500000000000005, "duration_str": "5.65ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 46.194, "width_percent": 1.596}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31935 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31935, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.277134, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 47.791, "width_percent": 0.051}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31935 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31935], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2777178, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 47.841, "width_percent": 1.551}, {"sql": "select * from `taxes` where `taxes`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.283667, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 49.393, "width_percent": 0.054}, {"sql": "select * from `taxes` where `taxes`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.284279, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 49.446, "width_percent": 0.042}, {"sql": "select * from `taxes` where `taxes`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 27, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 18}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2847881, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "ProductVatTaxesResource.php:22", "source": {"index": 20, "namespace": null, "name": "app/Http/Resources/V3/Product/ProductVatTaxesResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\Product\\ProductVatTaxesResource.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FProduct%2FProductVatTaxesResource.php&line=22", "ajax": false, "filename": "ProductVatTaxesResource.php", "line": "22"}, "connection": "buzfi", "explain": null, "start_percent": 49.489, "width_percent": 0.042}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.285424, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 49.531, "width_percent": 0.051}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31934 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31934, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.286509, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 49.582, "width_percent": 0.057}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31934 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31934], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.287097, "duration": 0.0055, "duration_str": "5.5ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 49.638, "width_percent": 1.554}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.292986, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 51.192, "width_percent": 0.051}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.293585, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 51.243, "width_percent": 0.15}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.294751, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 51.393, "width_percent": 0.054}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025858' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025858"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.295558, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 51.447, "width_percent": 0.045}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025859' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025859"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.2962291, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 51.492, "width_percent": 0.045}, {"sql": "select * from `videos` where `videos`.`subject_type` = 'App\\\\Models\\\\Product' and `videos`.`subject_id` = 31934 and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Product", 31934], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 68}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 26, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.296921, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 51.537, "width_percent": 0.059}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31934 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31934], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 76}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.297605, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 51.596, "width_percent": 1.551}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31934 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31934], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 77}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.303635, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 53.147, "width_percent": 1.54}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31934 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31934], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 78}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.309483, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 54.687, "width_percent": 1.534}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31934 and `reviews`.`product_id` is not null and `status` = 1 and `rating` is not null", "type": "query", "params": [], "bindings": [31934, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.31533, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:73", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=73", "ajax": false, "filename": "Product.php", "line": "73"}, "connection": "buzfi", "explain": null, "start_percent": 56.221, "width_percent": 0.048}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31934 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31934], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.315908, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 56.269, "width_percent": 1.531}, {"sql": "select * from `categories` where `categories`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.321945, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 57.801, "width_percent": 0.054}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 31933 and `reviews`.`product_id` is not null and `status` = 1 and `comment` is not null", "type": "query", "params": [], "bindings": [31933, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 60}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.323098, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:69", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=69", "ajax": false, "filename": "Product.php", "line": "69"}, "connection": "buzfi", "explain": null, "start_percent": 57.854, "width_percent": 0.068}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = 31933 and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [31933], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 22, "namespace": null, "name": "app/Http/Resources/V3/ProductResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\ProductResource.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.323734, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "Product.php:87", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Product.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=87", "ajax": false, "filename": "Product.php", "line": "87"}, "connection": "buzfi", "explain": null, "start_percent": 57.922, "width_percent": 1.574}, {"sql": "select * from `brands` where `brands`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, {"index": 22, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 71}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 28, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.3298411, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DelegatesToResource.php:139", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FHttp%2FResources%2FDelegatesToResource.php&line=139", "ajax": false, "filename": "DelegatesToResource.php", "line": "139"}, "connection": "buzfi", "explain": null, "start_percent": 59.496, "width_percent": 0.085}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = 11 and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, {"index": 21, "namespace": null, "name": "app/Http/ProductHelpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\ProductHelpers.php", "line": 74}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}, {"index": 27, "namespace": null, "name": "vendor/symfony/http-foundation/JsonResponse.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\symfony\\http-foundation\\JsonResponse.php", "line": 49}], "start": **********.3305602, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Brand.php:15", "source": {"index": 20, "namespace": null, "name": "app/Models/Brand.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Models\\Brand.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=15", "ajax": false, "filename": "Brand.php", "line": "15"}, "connection": "buzfi", "explain": null, "start_percent": 59.581, "width_percent": 0.13}, {"sql": "select * from `uploads` where `uploads`.`id` = '********' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["********"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.331646, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 59.711, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025852' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025852"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3324301, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 59.759, "width_percent": 0.042}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025853' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025853"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.33309, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 59.801, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = '10025854' and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10025854"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 24, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1261}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3337831, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Helpers.php:1262", "source": {"index": 20, "namespace": null, "name": "app/Http/Helpers.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Helpers.php", "line": 1262}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FHelpers.php&line=1262", "ajax": false, "filename": "Helpers.php", "line": "1262"}, "connection": "buzfi", "explain": null, "start_percent": 59.849, "width_percent": 0.042}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.334423, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 59.892, "width_percent": 0.042}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.334801, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 59.934, "width_percent": 0.042}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.33515, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 59.976, "width_percent": 0.059}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.335543, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 60.036, "width_percent": 1.531}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.341072, "duration": 0.00557, "duration_str": "5.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 61.567, "width_percent": 1.574}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.346746, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 63.141, "width_percent": 1.534}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.352287, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 64.675, "width_percent": 0.048}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.352576, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 64.723, "width_percent": 1.534}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.358272, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 66.257, "width_percent": 0.057}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3591409, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 66.313, "width_percent": 0.057}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.359441, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 66.37, "width_percent": 1.54}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.365018, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 67.91, "width_percent": 0.059}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.365422, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 67.969, "width_percent": 0.13}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.366217, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 68.099, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.366728, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 68.147, "width_percent": 0.042}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367094, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 68.19, "width_percent": 0.059}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.367494, "duration": 0.00537, "duration_str": "5.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 68.249, "width_percent": 1.517}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.372962, "duration": 0.00545, "duration_str": "5.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 69.766, "width_percent": 1.54}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3785188, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 71.306, "width_percent": 1.557}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.384171, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.863, "width_percent": 0.054}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3845131, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 72.916, "width_percent": 1.52}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390076, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.436, "width_percent": 0.059}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390492, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.496, "width_percent": 0.088}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.390956, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.583, "width_percent": 0.054}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.391397, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.637, "width_percent": 0.048}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392344, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.685, "width_percent": 0.068}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.392695, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 74.753, "width_percent": 1.543}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3982859, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.295, "width_percent": 0.054}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3986402, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.349, "width_percent": 0.127}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.39942, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.476, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.399965, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.524, "width_percent": 0.045}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400362, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.569, "width_percent": 0.045}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.400745, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.615, "width_percent": 0.059}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.401139, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 76.674, "width_percent": 1.543}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.406704, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 78.217, "width_percent": 1.548}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.412292, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 79.765, "width_percent": 1.545}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.417876, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 81.31, "width_percent": 0.054}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4181828, "duration": 0.00553, "duration_str": "5.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 81.364, "width_percent": 1.562}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.423902, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.926, "width_percent": 0.054}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.424233, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 82.98, "width_percent": 0.059}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4245532, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.039, "width_percent": 0.04}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4249508, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.079, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4260018, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.144, "width_percent": 0.071}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.426362, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 83.215, "width_percent": 1.531}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.431916, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 84.746, "width_percent": 0.079}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432379, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 84.825, "width_percent": 0.141}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433204, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 84.966, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.433726, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.014, "width_percent": 0.045}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434125, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.06, "width_percent": 0.048}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4345062, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.108, "width_percent": 0.079}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.434985, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 85.187, "width_percent": 1.551}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4405959, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 86.738, "width_percent": 1.548}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.44622, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 88.286, "width_percent": 1.534}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.451794, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 89.82, "width_percent": 0.054}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4521399, "duration": 0.00551, "duration_str": "5.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 89.874, "width_percent": 1.557}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.457825, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.431, "width_percent": 0.048}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458117, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.479, "width_percent": 0.045}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458396, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.524, "width_percent": 0.051}, {"sql": "select * from `categories` where `categories`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.458829, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.575, "width_percent": 0.065}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `comment` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.459726, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.64, "width_percent": 0.051}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.46001, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 91.691, "width_percent": 1.543}, {"sql": "select * from `brands` where `brands`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.46559, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.233, "width_percent": 0.045}, {"sql": "select * from `brand_translations` where `brand_translations`.`brand_id` = ? and `brand_translations`.`brand_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4658792, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.279, "width_percent": 0.127}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.466653, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.406, "width_percent": 0.045}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467166, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.451, "width_percent": 0.048}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.467588, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.499, "width_percent": 0.059}, {"sql": "select * from `uploads` where `uploads`.`id` = ? and `uploads`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.468074, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.558, "width_percent": 0.048}, {"sql": "select * from `videos` where `videos`.`subject_type` = ? and `videos`.`subject_id` = ? and `videos`.`subject_id` is not null and `videos`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4685209, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.606, "width_percent": 0.062}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.468936, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 93.668, "width_percent": 1.529}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.474456, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 95.197, "width_percent": 1.529}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.480017, "duration": 0.00552, "duration_str": "5.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 96.725, "width_percent": 1.56}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = ? and `reviews`.`product_id` is not null and `status` = ? and `rating` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.485666, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.285, "width_percent": 0.054}, {"sql": "select sum(`qty`) as aggregate from `product_stocks` where `product_stocks`.`product_id` = ? and `product_stocks`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4859781, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 98.339, "width_percent": 1.529}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.49158, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.867, "width_percent": 0.054}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4919062, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.921, "width_percent": 0.04}, {"sql": "select * from `taxes` where `taxes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.492157, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "buzfi", "explain": null, "start_percent": 99.96, "width_percent": 0.04}]}, "models": {"data": {"App\\Models\\Upload": {"retrieved": 60, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FUpload.php&line=1", "ajax": false, "filename": "Upload.php", "line": "?"}}, "App\\Models\\ProductTax": {"retrieved": 51, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTax.php&line=1", "ajax": false, "filename": "ProductTax.php", "line": "?"}}, "App\\Models\\Tax": {"retrieved": 21, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FTax.php&line=1", "ajax": false, "filename": "Tax.php", "line": "?"}}, "App\\Models\\Product": {"retrieved": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\ProductTranslation": {"retrieved": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FProductTranslation.php&line=1", "ajax": false, "filename": "ProductTranslation.php", "line": "?"}}, "App\\Models\\Category": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\BrandTranslation": {"retrieved": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FBrandTranslation.php&line=1", "ajax": false, "filename": "BrandTranslation.php", "line": "?"}}, "App\\Models\\Supplier": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FSupplier.php&line=1", "ajax": false, "filename": "Supplier.php", "line": "?"}}}, "count": 200, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 200}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/products/featured", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getFeaturedProducts", "uri": "GET api/v3/products/featured", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getFeaturedProducts<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=805\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3/products", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiProductController.php&line=805\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiProductController.php:805-848</a>", "middleware": "api, app_language, app_language, optional.auth", "duration": "918ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-508001686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-508001686\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1865511744 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1865511744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-764287848 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">public, max-age=10</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6ImZOMUlsMDB5amh1ZjdCVWhiMEorcWc9PSIsInZhbHVlIjoiWW1KV2RtNk1MRGlIQ3Y4b2w5d3ZCbWVKcERYRWU1YnRKS3B1emxFL20xQ2pFQUtGYUlxSzFiTFRXMkVJdGlnWjVPZTFJeDlPYzUvcXlNZVQxYzArSlVqMW5meWlHMnJ4VUVyTC93bzNMUitwNU5wTnF3cHZNa3IvZFloZUhabnciLCJtYWMiOiI0NTI3Nzg0ODQ2NTRiNjNmMjljNmEyZmEyZWJiYTIyNzZhMmQ4NWQwZTBkYWVhNGY5NTc3ZjJmMTY1MjUwODMwIiwidGFnIjoiIn0%3D; buzficom_session=YwNUdc8ruNUstSyVvI36y72tGCLgDgPu7koof68k</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764287848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1774657155 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774657155\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-cache</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">MISS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">596</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ilg2Y3REK2MyaUkvMUFuTXJ0RFl0Vnc9PSIsInZhbHVlIjoidnk5cjZRcThRcy84OGdNSUZRcGdwbHNrOTBvZGowOWdWM0FDTlhvajNTN2FGcmUxTXpyM3I1eEtjdWxaYTlrTWdYdGM3MVNyZ01UM0UxYm9kSWV4cllyaE5abjZvQ3pCS0IxazQwYnJRTmN4cGI0Q3VDaFA1UjBYdndaeXNmZWgiLCJtYWMiOiI2NzE1MzcwZmFhMTM0ZDBlMWZmZmEyN2RiZmExYjY4OTE4OTRiMTE0YTBiYzY2OTk3ODkwMjQ3NjRmNDVlMmJjIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:29 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6IjY4TUJkWDdzdndLOEJXZ01nNUxvZHc9PSIsInZhbHVlIjoiZ080NEZtYmFWd1QwQTQwUFY0bnFZNENmaG1weStlUWZNeWR1bXl2cWVYK3Mva3lhMnY0WGpDWkxJbys1bU5aeVZJZzFHRlBJclYvRHRJS2g2UmFrSzAxbWY3c21UR1lNbFQwMHJFZUc1TjNiMFBJS2lzWmo1bUlXdFpoZDR0U0EiLCJtYWMiOiI3ODc4NTI4NTFlMGRjZWMzOWU3ZDI2Y2EyYWM0MDUzYjQ2YjE3MzY2YzY1MGUxNTRkN2MyYmI0NTRjMGJjMThlIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:29 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ilg2Y3REK2MyaUkvMUFuTXJ0RFl0Vnc9PSIsInZhbHVlIjoidnk5cjZRcThRcy84OGdNSUZRcGdwbHNrOTBvZGowOWdWM0FDTlhvajNTN2FGcmUxTXpyM3I1eEtjdWxaYTlrTWdYdGM3MVNyZ01UM0UxYm9kSWV4cllyaE5abjZvQ3pCS0IxazQwYnJRTmN4cGI0Q3VDaFA1UjBYdndaeXNmZWgiLCJtYWMiOiI2NzE1MzcwZmFhMTM0ZDBlMWZmZmEyN2RiZmExYjY4OTE4OTRiMTE0YTBiYzY2OTk3ODkwMjQ3NjRmNDVlMmJjIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6IjY4TUJkWDdzdndLOEJXZ01nNUxvZHc9PSIsInZhbHVlIjoiZ080NEZtYmFWd1QwQTQwUFY0bnFZNENmaG1weStlUWZNeWR1bXl2cWVYK3Mva3lhMnY0WGpDWkxJbys1bU5aeVZJZzFHRlBJclYvRHRJS2g2UmFrSzAxbWY3c21UR1lNbFQwMHJFZUc1TjNiMFBJS2lzWmo1bUlXdFpoZDR0U0EiLCJtYWMiOiI3ODc4NTI4NTFlMGRjZWMzOWU3ZDI2Y2EyYWM0MDUzYjQ2YjE3MzY2YzY1MGUxNTRkN2MyYmI0NTRjMGJjMThlIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://localhost/buzfi-new-backend/api/v3/products/featured</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/products/featured", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiProductController@getFeaturedProducts"}, "badge": null}}