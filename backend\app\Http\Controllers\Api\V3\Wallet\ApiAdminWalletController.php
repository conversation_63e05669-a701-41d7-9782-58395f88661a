<?php

namespace App\Http\Controllers\Api\V3\Wallet;

use App\Http\Controllers\Controller;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\WalletExchangeRate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Activity;
use Spatie\Activitylog\Facades\LogActivity;

class ApiAdminWalletController extends Controller
{
    public function getWalletDetails(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = User::findOrFail($request->user_id);
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        // Get transaction statistics
        $stats = [
            'total_transactions' => WalletTransaction::where('user_id', $user->id)->count(),
            'total_credits' => WalletTransaction::where('user_id', $user->id)
                ->where('type', 'credit')
                ->sum('amount'),
            'total_debits' => WalletTransaction::where('user_id', $user->id)
                ->where('type', 'debit')
                ->sum('amount'),
            'pending_transactions' => WalletTransaction::where('user_id', $user->id)
                ->where('approval_status', 'pending')
                ->count(),
            'latest_transaction' => WalletTransaction::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->first()
        ];

        // Calculate balance breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'refund')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'admin_credits' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'admin')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'rewards' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'reward')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'promotions' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'promotion')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount')
        ];

        // Get recent transactions
        $recentTransactions = WalletTransaction::where('user_id', $user->id)
            ->with('addedBy:id,name,email')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function($transaction) {
                return [
                    'id' => (string)$transaction->id,
                    'amount' => (float)$transaction->amount,
                    'type' => $transaction->type,
                    'balance_type' => $transaction->balance_type,
                    'description' => $transaction->description,
                    'source' => $transaction->reference_type,
                    'added_by' => $transaction->addedBy ? $transaction->addedBy->name : null,
                    'timestamp' => $transaction->created_at->toIso8601String()
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? null,
                    'registered_at' => $user->created_at->toIso8601String()
                ],
                'wallet' => [
                    'id' => $wallet->id,
                    'balance' => (float)$wallet->balance,
                    'reward_points' => (int)$wallet->reward_points,
                    'promotional_credits' => (float)$wallet->promotional_credits,
                    'status' => $wallet->status,
                    'last_updated_at' => $wallet->last_updated_at ? $wallet->last_updated_at->toIso8601String() : null,
                    'created_at' => $wallet->created_at->toIso8601String()
                ],
                'statistics' => $stats,
                'balance_breakdown' => $balanceBreakdown,
                'recent_transactions' => $recentTransactions
            ]
        ]);
    }

    public function getTransactions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'type' => 'nullable|in:credit,debit',
            'source' => 'nullable|in:order,refund,admin,reward,promotion,purchase,promotional',
            'balance_type' => 'nullable|in:balance,reward_points,promotional_credits',
            'approval_status' => 'nullable|in:pending,approved,rejected',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $wallet = Wallet::where('user_id', $request->user_id)->first();
        
        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        $perPage = $request->per_page ?? 15;
        $page = $request->page ?? 1;

        $query = WalletTransaction::where('user_id', $request->user_id);

        if ($request->type) {
            $query->where('type', $request->type);
        }

        if ($request->source) {
            $query->where('reference_type', $request->source);
        }

        if ($request->balance_type) {
            $query->where('balance_type', $request->balance_type);
        }

        if ($request->approval_status) {
            $query->where('approval_status', $request->approval_status);
        }

        $transactions = $query->with(['user:id,name,email', 'addedBy:id,name,email'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Format transactions for admin frontend
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'amount' => (float)$transaction->amount,
                'type' => $transaction->type,
                'balance_type' => $transaction->balance_type,
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'reference_id' => $transaction->reference_id,
                'approval_status' => $transaction->approval_status,
                'user' => $transaction->user ? [
                    'id' => $transaction->user->id,
                    'name' => $transaction->user->name,
                    'email' => $transaction->user->email
                ] : null,
                'added_by' => $transaction->addedBy ? [
                    'id' => $transaction->addedBy->id,
                    'name' => $transaction->addedBy->name,
                    'email' => $transaction->addedBy->email
                ] : null,
                'metadata' => $transaction->metadata,
                'timestamp' => $transaction->created_at->toIso8601String()
            ];
        });

        return response()->json([
            'success' => true,
            'transactions' => $formattedTransactions,
            'pagination' => [
                'total' => $transactions->total(),
                'per_page' => $transactions->perPage(),
                'current_page' => $transactions->currentPage(),
                'total_pages' => $transactions->lastPage()
            ]
        ]);
    }

    public function addFunds(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string|max:255',
            'reference_type' => 'nullable|in:order,refund,admin,reward,promotion',
            'reference_id' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $admin = $request->user();
            $wallet = Wallet::where('user_id', $request->user_id)->first();

            if (!$wallet) {
                $wallet = Wallet::create([
                    'user_id' => $request->user_id,
                    'balance' => 0,
                    'reward_points' => 0,
                    'promotional_credits' => 0,
                    'status' => 'active'
                ]);
            }

            // Based on the type, update the appropriate wallet balance
            switch ($request->type) {
                case 'balance':
                    $wallet->balance += $request->amount;
                    break;
                case 'reward_points':
                    $wallet->reward_points += (int)$request->amount;
                    break;
                case 'promotional_credits':
                    $wallet->promotional_credits += $request->amount;
                    break;
            }
            
            $wallet->last_updated_at = now();
            $wallet->save();

            // Create transaction record
            $transaction = WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $request->user_id,
                'type' => 'credit',
                'amount' => $request->amount,
                'balance_type' => $request->type,
                'description' => $request->description,
                'reference_type' => $request->reference_type ?? 'admin',
                'reference_id' => $request->reference_id,
                'added_by' => $admin->id,
                'approval_status' => 'approved',
                'metadata' => [
                    'type' => $request->type,
                    'admin_id' => $admin->id,
                    'admin_name' => $admin->name,
                    'admin_email' => $admin->email
                ]
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Funds added successfully',
                'data' => [
                    'transaction' => [
                        'id' => (string)$transaction->id,
                        'amount' => (float)$transaction->amount,
                        'type' => $transaction->type,
                        'description' => $transaction->description,
                        'timestamp' => $transaction->created_at->toIso8601String()
                    ],
                    'wallet' => [
                        'balance' => $wallet->balance,
                        'reward_points' => $wallet->reward_points,
                        'promotional_credits' => $wallet->promotional_credits
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add funds: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deductFunds(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string|max:255',
            'reference_type' => 'nullable|in:order,refund,admin,reward,promotion',
            'reference_id' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $admin = $request->user();
            $wallet = Wallet::where('user_id', $request->user_id)->first();

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'Wallet not found'
                ], 404);
            }

            // Check if sufficient funds are available
            $currentBalance = match($request->type) {
                'balance' => $wallet->balance,
                'reward_points' => $wallet->reward_points,
                'promotional_credits' => $wallet->promotional_credits,
                default => 0
            };

            if ($currentBalance < $request->amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient funds'
                ], 400);
            }

            // Based on the type, update the appropriate wallet balance
            switch ($request->type) {
                case 'balance':
                    $wallet->balance -= $request->amount;
                    break;
                case 'reward_points':
                    $wallet->reward_points -= (int)$request->amount;
                    break;
                case 'promotional_credits':
                    $wallet->promotional_credits -= $request->amount;
                    break;
            }
            
            $wallet->last_updated_at = now();
            $wallet->save();

            // Create transaction record
            $transaction = WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $request->user_id,
                'type' => 'debit',
                'amount' => $request->amount,
                'balance_type' => $request->type,
                'description' => $request->description,
                'reference_type' => $request->reference_type ?? 'admin',
                'reference_id' => $request->reference_id,
                'added_by' => $admin->id,
                'approval_status' => 'approved',
                'metadata' => [
                    'type' => $request->type,
                    'admin_id' => $admin->id,
                    'admin_name' => $admin->name,
                    'admin_email' => $admin->email,
                    'reason' => $request->reason ?? 'Administrative action'
                ]
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Funds deducted successfully',
                'data' => [
                    'transaction' => [
                        'id' => (string)$transaction->id,
                        'amount' => (float)$transaction->amount,
                        'type' => $transaction->type,
                        'description' => $transaction->description,
                        'timestamp' => $transaction->created_at->toIso8601String()
                    ],
                    'wallet' => [
                        'balance' => $wallet->balance,
                        'reward_points' => $wallet->reward_points,
                        'promotional_credits' => $wallet->promotional_credits
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to deduct funds: ' . $e->getMessage()
            ], 500);
        }
    }

    public function manageExchangeRates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:create,update,delete',
            'id' => 'required_if:action,update,delete|exists:wallet_exchange_rates,id',
            'points' => 'required_if:action,create,update|integer|min:1',
            'currency_amount' => 'required_if:action,create,update|numeric|min:0.01',
            'status' => 'nullable|in:active,inactive,expired',
            'valid_from' => 'nullable|date',
            'valid_until' => 'nullable|date|after:valid_from'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();
            $admin = $request->user();

            switch ($request->action) {
                case 'create':
                    $rate = WalletExchangeRate::create([
                        'points' => $request->points,
                        'currency_amount' => $request->currency_amount,
                        'status' => $request->status ?? 'active',
                        'valid_from' => $request->valid_from,
                        'valid_until' => $request->valid_until
                    ]);

                    // Log the creation action
                    activity()
                        ->causedBy($admin)
                        ->performedOn($rate)
                        ->withProperties([
                            'action' => 'created',
                            'admin_id' => $admin->id,
                            'admin_name' => $admin->name,
                            'details' => [
                                'points' => $request->points,
                                'currency_amount' => $request->currency_amount,
                                'ratio' => round($request->currency_amount / $request->points, 4)
                            ]
                        ])
                        ->log('Created new points exchange rate');
                    break;

                case 'update':
                    $rate = WalletExchangeRate::findOrFail($request->id);
                    $oldData = [
                        'points' => $rate->points,
                        'currency_amount' => $rate->currency_amount,
                        'status' => $rate->status
                    ];
                    
                    $rate->update([
                        'points' => $request->points,
                        'currency_amount' => $request->currency_amount,
                        'status' => $request->status,
                        'valid_from' => $request->valid_from,
                        'valid_until' => $request->valid_until
                    ]);
                    
                    // Log the update action
                    activity()
                        ->causedBy($admin)
                        ->performedOn($rate)
                        ->withProperties([
                            'action' => 'updated',
                            'admin_id' => $admin->id,
                            'admin_name' => $admin->name,
                            'old_data' => $oldData,
                            'new_data' => [
                                'points' => $request->points,
                                'currency_amount' => $request->currency_amount,
                                'status' => $request->status
                            ]
                        ])
                        ->log('Updated points exchange rate');
                    break;

                case 'delete':
                    $rate = WalletExchangeRate::findOrFail($request->id);
                    $rateData = [
                        'id' => $rate->id,
                        'points' => $rate->points,
                        'currency_amount' => $rate->currency_amount
                    ];
                    
                    $rate->delete();
                    
                    // Log the deletion action
                    activity()
                        ->causedBy($admin)
                        ->withProperties([
                            'action' => 'deleted',
                            'admin_id' => $admin->id,
                            'admin_name' => $admin->name,
                            'deleted_rate' => $rateData
                        ])
                        ->log('Deleted points exchange rate');
                    break;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Exchange rate ' . $request->action . 'd successfully',
                'data' => isset($rate) ? [
                    'id' => $rate->id,
                    'points' => $rate->points,
                    'currency_amount' => $rate->currency_amount,
                    'ratio' => round($rate->currency_amount / $rate->points, 4),
                    'status' => $rate->status,
                    'valid_from' => $rate->valid_from ? $rate->valid_from->toIso8601String() : null,
                    'valid_until' => $rate->valid_until ? $rate->valid_until->toIso8601String() : null
                ] : null
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to ' . $request->action . ' exchange rate: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getWalletStats()
    {
        // Overall wallet statistics
        $totalWallets = Wallet::count();
        $activeWallets = Wallet::where('status', 'active')->count();
        $totalBalance = Wallet::sum('balance');
        $totalRewardPoints = Wallet::sum('reward_points');
        $totalPromotionalCredits = Wallet::sum('promotional_credits');

        // Transaction statistics
        $transactionStats = [
            'total_transactions' => WalletTransaction::count(),
            'total_credits' => WalletTransaction::where('type', 'credit')->sum('amount'),
            'total_debits' => WalletTransaction::where('type', 'debit')->sum('amount'),
            'transaction_count_by_type' => [
                'credit' => WalletTransaction::where('type', 'credit')->count(),
                'debit' => WalletTransaction::where('type', 'debit')->count()
            ],
            'transaction_count_by_reference' => [
                'order' => WalletTransaction::where('reference_type', 'order')->count(),
                'refund' => WalletTransaction::where('reference_type', 'refund')->count(),
                'admin' => WalletTransaction::where('reference_type', 'admin')->count(),
                'reward' => WalletTransaction::where('reference_type', 'reward')->count(),
                'promotion' => WalletTransaction::where('reference_type', 'promotion')->count()
            ],
            'transaction_count_by_balance_type' => [
                'balance' => WalletTransaction::where('balance_type', 'balance')->count(),
                'reward_points' => WalletTransaction::where('balance_type', 'reward_points')->count(),
                'promotional_credits' => WalletTransaction::where('balance_type', 'promotional_credits')->count()
            ]
        ];

        // Get top users by wallet balance
        $topUsersByBalance = Wallet::with('user:id,name,email')
            ->orderBy('balance', 'desc')
            ->limit(10)
            ->get()
            ->map(function($wallet) {
                return [
                    'user_id' => $wallet->user_id,
                    'name' => $wallet->user ? $wallet->user->name : 'Unknown',
                    'email' => $wallet->user ? $wallet->user->email : 'Unknown',
                    'balance' => (float)$wallet->balance,
                    'reward_points' => (int)$wallet->reward_points,
                    'promotional_credits' => (float)$wallet->promotional_credits
                ];
            });

        // Get recent transactions
        $recentTransactions = WalletTransaction::with(['wallet.user:id,name,email', 'addedBy:id,name,email'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($transaction) {
                return [
                    'id' => (string)$transaction->id,
                    'amount' => (float)$transaction->amount,
                    'type' => $transaction->type,
                    'balance_type' => $transaction->balance_type,
                    'description' => $transaction->description,
                    'source' => $transaction->reference_type,
                    'user' => $transaction->wallet && $transaction->wallet->user ? [
                        'id' => $transaction->wallet->user->id,
                        'name' => $transaction->wallet->user->name,
                        'email' => $transaction->wallet->user->email
                    ] : null,
                    'added_by' => $transaction->addedBy ? [
                        'id' => $transaction->addedBy->id,
                        'name' => $transaction->addedBy->name,
                        'email' => $transaction->addedBy->email
                    ] : null,
                    'timestamp' => $transaction->created_at->toIso8601String()
                ];
            });

        // Get active exchange rates
        $activeExchangeRates = WalletExchangeRate::where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('valid_until')
                    ->orWhere('valid_until', '>', now());
            })
            ->get()
            ->map(function($rate) {
                return [
                    'id' => $rate->id,
                    'points' => $rate->points,
                    'currency_amount' => (float)$rate->currency_amount,
                    'ratio' => round($rate->currency_amount / $rate->points, 4),
                    'status' => $rate->status,
                    'valid_from' => $rate->valid_from ? $rate->valid_from->toIso8601String() : null,
                    'valid_until' => $rate->valid_until ? $rate->valid_until->toIso8601String() : null
                ];
            });

        // Daily statistics for the past 7 days for charting
        $lastWeekStats = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $nextDate = now()->subDays($i-1)->format('Y-m-d');
            
            $dailyStats = [
                'date' => $date,
                'credits' => WalletTransaction::where('type', 'credit')
                    ->whereDate('created_at', $date)
                    ->sum('amount'),
                'debits' => WalletTransaction::where('type', 'debit')
                    ->whereDate('created_at', $date)
                    ->sum('amount'),
                'transaction_count' => WalletTransaction::whereDate('created_at', $date)->count(),
                'new_wallets' => Wallet::whereDate('created_at', $date)->count()
            ];
            
            $lastWeekStats[] = $dailyStats;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'wallet_overview' => [
                    'total_wallets' => $totalWallets,
                    'active_wallets' => $activeWallets,
                    'inactive_wallets' => $totalWallets - $activeWallets,
                    'total_balance' => (float)$totalBalance,
                    'average_balance' => $totalWallets > 0 ? (float)($totalBalance / $totalWallets) : 0,
                    'total_reward_points' => (int)$totalRewardPoints,
                    'total_promotional_credits' => (float)$totalPromotionalCredits
                ],
                'transaction_stats' => $transactionStats,
                'top_users' => $topUsersByBalance,
                'recent_transactions' => $recentTransactions,
                'active_exchange_rates' => $activeExchangeRates,
                'daily_stats' => $lastWeekStats
            ]
        ]);
    }

    /**
     * Get all wallets with pagination for admin
     */
    public function getAllWallets(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'search' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
            'sort_by' => 'nullable|in:balance,reward_points,promotional_credits,created_at',
            'sort_order' => 'nullable|in:asc,desc'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $perPage = $request->per_page ?? 15;
        $page = $request->page ?? 1;
        $sortBy = $request->sort_by ?? 'created_at';
        $sortOrder = $request->sort_order ?? 'desc';

        $query = Wallet::with('user:id,name,email');

        // Apply search if provided
        if ($request->search) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        $wallets = $query->paginate($perPage, ['*'], 'page', $page);

        // Format wallet data for response
        $formattedWallets = $wallets->map(function($wallet) {
            $recentTransaction = WalletTransaction::where('wallet_id', $wallet->id)
                ->orderBy('created_at', 'desc')
                ->first();
                
            return [
                'id' => $wallet->id,
                'user' => $wallet->user ? [
                    'id' => $wallet->user->id,
                    'name' => $wallet->user->name,
                    'email' => $wallet->user->email
                ] : null,
                'balance' => (float)$wallet->balance,
                'reward_points' => (int)$wallet->reward_points,
                'promotional_credits' => (float)$wallet->promotional_credits,
                'status' => $wallet->status,
                'last_transaction' => $recentTransaction ? [
                    'id' => $recentTransaction->id,
                    'type' => $recentTransaction->type,
                    'amount' => (float)$recentTransaction->amount,
                    'description' => $recentTransaction->description,
                    'date' => $recentTransaction->created_at->toIso8601String()
                ] : null,
                'created_at' => $wallet->created_at->toIso8601String(),
                'updated_at' => $wallet->updated_at->toIso8601String()
            ];
        });

        return response()->json([
            'success' => true,
            'wallets' => $formattedWallets,
            'pagination' => [
                'total' => $wallets->total(),
                'per_page' => $wallets->perPage(),
                'current_page' => $wallets->currentPage(),
                'total_pages' => $wallets->lastPage()
            ]
        ]);
    }

    /**
     * Approve or reject pending wallet transactions
     */
    public function manageTransactionApproval(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transaction_id' => 'required|exists:wallet_transactions,id',
            'action' => 'required|in:approve,reject',
            'reason' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $admin = $request->user();
            $transaction = WalletTransaction::findOrFail($request->transaction_id);

            // Check if the transaction is pending
            if ($transaction->approval_status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending transactions can be approved or rejected'
                ], 400);
            }

            // Get the wallet
            $wallet = Wallet::findOrFail($transaction->wallet_id);

            if ($request->action === 'approve') {
                // Update transaction status
                $transaction->approval_status = 'approved';
                
                // Update wallet balance based on transaction type
                if ($transaction->type === 'credit') {
                    switch ($transaction->balance_type) {
                        case 'balance':
                            $wallet->balance += $transaction->amount;
                            break;
                        case 'reward_points':
                            $wallet->reward_points += (int)$transaction->amount;
                            break;
                        case 'promotional_credits':
                            $wallet->promotional_credits += $transaction->amount;
                            break;
                    }
                } else if ($transaction->type === 'debit') {
                    switch ($transaction->balance_type) {
                        case 'balance':
                            // Check if sufficient balance exists
                            if ($wallet->balance < $transaction->amount) {
                                DB::rollBack();
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Insufficient wallet balance'
                                ], 400);
                            }
                            $wallet->balance -= $transaction->amount;
                            break;
                        case 'reward_points':
                            // Check if sufficient points exist
                            if ($wallet->reward_points < $transaction->amount) {
                                DB::rollBack();
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Insufficient reward points'
                                ], 400);
                            }
                            $wallet->reward_points -= (int)$transaction->amount;
                            break;
                        case 'promotional_credits':
                            // Check if sufficient credits exist
                            if ($wallet->promotional_credits < $transaction->amount) {
                                DB::rollBack();
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Insufficient promotional credits'
                                ], 400);
                            }
                            $wallet->promotional_credits -= $transaction->amount;
                            break;
                    }
                }
                
                $wallet->last_updated_at = now();
                $wallet->save();
                
                // Update metadata
                $metadata = $transaction->metadata ?? [];
                $metadata['approved_by'] = [
                    'id' => $admin->id,
                    'name' => $admin->name,
                    'email' => $admin->email
                ];
                $metadata['approved_at'] = now()->toIso8601String();
                $transaction->metadata = $metadata;
                
            } else { // Reject
                $transaction->approval_status = 'rejected';
                
                // Update metadata
                $metadata = $transaction->metadata ?? [];
                $metadata['rejected_by'] = [
                    'id' => $admin->id,
                    'name' => $admin->name,
                    'email' => $admin->email
                ];
                $metadata['rejected_at'] = now()->toIso8601String();
                $metadata['rejection_reason'] = $request->reason ?? 'Administrative action';
                $transaction->metadata = $metadata;
            }
            
            $transaction->save();
            
            // Log the action
            activity()
                ->causedBy($admin)
                ->performedOn($transaction)
                ->withProperties([
                    'action' => $request->action,
                    'admin_id' => $admin->id,
                    'admin_name' => $admin->name,
                    'transaction_id' => $transaction->id,
                    'wallet_id' => $wallet->id,
                    'user_id' => $wallet->user_id,
                    'reason' => $request->reason ?? ''
                ])
                ->log("Transaction {$request->action}d by admin");
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Transaction ' . ($request->action === 'approve' ? 'approved' : 'rejected') . ' successfully',
                'data' => [
                    'transaction' => [
                        'id' => (string)$transaction->id,
                        'amount' => (float)$transaction->amount,
                        'type' => $transaction->type,
                        'balance_type' => $transaction->balance_type,
                        'approval_status' => $transaction->approval_status,
                        'description' => $transaction->description,
                        'timestamp' => $transaction->created_at->toIso8601String()
                    ],
                    'wallet' => [
                        'id' => $wallet->id,
                        'balance' => (float)$wallet->balance,
                        'reward_points' => (int)$wallet->reward_points,
                        'promotional_credits' => (float)$wallet->promotional_credits
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to process transaction: ' . $e->getMessage()
            ], 500);
        }
    }
} 