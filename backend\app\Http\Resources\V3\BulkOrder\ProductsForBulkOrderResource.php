<?php

namespace App\Http\Resources\V3\BulkOrder;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductsForBulkOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $stock = $this->stocks->first();
        $bulkPricingTiers = [];

        $bulkPricingTiers[] = [
            'minimumQuantity' => $this->min_qty,
            'price' => $this->wholesale_price
        ];
        $prices = ProductPriceHelper::getProductPrices($this);
        return [
            'id' => $this->slug,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $prices['displayPrice'],
            'regularPrice' => $prices['regularPrice'],
            'has_discount' => $this->discount > 0,
            'discount' => $this->discount,
            'discount_type' => $this->discount_type,
            'discount_percentage' => $this->discount_type === 'percent' ? $this->discount : round(($prices['regularPrice'] - $prices['displayPrice']) / $prices['regularPrice'] * 100, 2),
            'thumbnail' => uploaded_asset($this->thumbnail_img),
            'featuredImage' => uploaded_asset($this->featured_img),
            'images' => array_map(function($img) {
                return uploaded_asset($img);
            }, explode(',', $this->photos)),
            'brand' => $this->brand ? $this->brand->name : null,
            'category' => $this->category ? $this->category->name : null,
            'stock' => $stock ? $stock->qty : 0,
            'inStock' => ($stock && $stock->qty > 0),
            'rating' =>  (float) $this->rating,
            'reviewCount' => (int)$this->getApprovedReviewCount(),
            'minimumOrderQuantity' => $this->min_qty,
            'stockQuantity' => $this->getTotalStockQuantity(),
            'bulkPricingTiers' => $bulkPricingTiers
        ];
    }
}
