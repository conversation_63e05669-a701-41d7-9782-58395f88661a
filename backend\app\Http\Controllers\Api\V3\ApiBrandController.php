<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\BrandResource;
use App\Http\Resources\V3\BrandsResource;
use App\Http\Resources\V3\Categories\CategoriesResource;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Brand;
use App\Models\Category;
use App\Models\OrderDetail;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class ApiBrandController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index(Request $request)
    {
        $per_page = min((int) $request->input('per_page', 40), 100);
        $page = max((int) $request->input('page', 1), 1);
        $sort = in_array(
            $request->input('sort'),
            [
                'name_asc',
                'name_desc',
                /*'popular',*/
                'product_count',
            ]
        ) ? $request->input('sort') : 'name_asc';
        $category = $request->input('category', null);
        $featured = $request->input('featured', false);
        $search = $request->input('search', null);
        $brands = Brand::query();

        if ($category != null) {
            $brand_ids = Product::where('category_id', $category)->whereNotNull('brand_id')->pluck('brand_id')->unique()->toArray();
            $brands = $brands->whereIn('id', $brand_ids);
        }
        if ($featured) {
            $brands = $brands->where('top', 1);
        }
        if ($search) {
            $brands = $brands->where('name', 'like', "%$search%");
        }
        switch ($sort) {
            case 'name_asc':
                $brands = $brands->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $brands = $brands->orderBy('name', 'desc');
                break;
            case 'product_count':
                $brands = $brands->orderBy('products_count', 'desc');
                break;
        }
        $paginatedBrands = $brands->paginate($per_page);
        $total_items = $paginatedBrands->total();
        $total_pages = $paginatedBrands->lastPage();

        $data = [
            'brands' => new BrandsResource($paginatedBrands),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function get_featured_brands(Request $request)
    {
        $request->merge(
            [
                'featured' => 1,
                'category' => $request->input('category', null),
                'per_page' => $request->input('limit', 10),
            ]
        );
        return $this->index($request);
    }
    public function details(Request $request, $brandSlug)
    {
        // Check if we have a valid brand slug
        $brand = Brand::where('slug', $brandSlug)->first();
        
        if (!$brand) {
            return $this->error(
                'BRAND_NOT_FOUND',
                'Brand not found',
                null,
                404
            );
        }
        
        $include_categories = $request->input('include_categories', 1);
        $include_sample_products = $request->input('include_sample_products', 1);
        $product_limit = max($request->input('product_limit', 4), 12);

        $categories = [];
        if ($include_categories) {
            $categories_id = Product::where('brand_id', $brand->id)->whereNotNull('category_id')->pluck('category_id')->unique()->toArray();
            $categories = Category::whereIn('id', $categories_id)->get();
        }
        if ($include_sample_products) {
            $products = Product::where('brand_id', $brand->id)
                ->limit($product_limit)
                ->get();
            $sample_products = $products;
        } else {
            $sample_products = [];
        }
        $responce = [
            'brand' => new BrandResource($brand),
            'categories' => new CategoriesResource($categories),
            'products' => new ProductsResource($sample_products),
        ];
        return $this->success(
            $responce,
            translate('Brand details fetched successfully')
        );
    }
    /**
     * Get categories for a specific brand
     *
     * @param Request $request
     * @param string $brandIdentifier
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_brand_categories(Request $request)
    {
        // Get parameters
        $includeProductCounts = $request->input('include_product_counts', true);

        // Get categories that have products from any brand
        $query = Category::query();

        // Join with products to get only categories with products 
        $query->whereHas('products', function ($q) {
            $q->whereNotNull('brand_id');
        });

        // Add product count if requested
        if ($includeProductCounts) {
            $query->withCount(['products' => function ($q) {
                $q->whereNotNull('brand_id');
            }]);
        }

        // Order by name
        $query->orderBy('name', 'asc');

        // Get results
        $categories = $query->get();

        // Transform categories to include product counts if requested
        $transformedCategories = $categories->map(function ($category) use ($includeProductCounts) {
            $data = [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'icon' => uploaded_asset($category->icon),
                'banner' => uploaded_asset($category->banner)
            ];

            if ($includeProductCounts) {
                $data['product_count'] = $category->products_count;
            }

            return $data;
        });

        // Return response
        return $this->success([
            'categories' => $transformedCategories
        ], translate('All brand categories fetched successfully'));
    }

    /**
     * Get brands that have products in a specific category
     *
     * @param Request $request
     * @param string $categoryIdentifier
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBrandsByCategory(Request $request, $categoryIdentifier)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort' => 'nullable|string|in:name_asc,name_desc,popular,product_count'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        $sort = $request->input('sort', 'name_asc');

        // Find the category
        $category = null;
        if (is_numeric($categoryIdentifier)) {
            $category = Category::find($categoryIdentifier);
        } else {
            $category = Category::where('slug', $categoryIdentifier)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Get brands that have products in this category
        $query = Brand::query();

        // Join with products to get only brands with products in this category
        $query->whereHas('products', function ($q) use ($category) {
            $q->where('category_id', $category->id);
        });

        // Add product count
        $query->withCount([
            'products' => function ($q) use ($category) {
                $q->where('category_id', $category->id);
            }
        ]);

        // Apply sorting
        switch ($sort) {
            case 'name_asc':
                $query->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $query->orderBy('name', 'desc');
                break;
            case 'product_count':
                $query->orderBy('products_count', 'desc');
                break;
            case 'popular':
                // For popular, we'll use a combination of product count and sales
                $query->addSelect([
                    'brands.*',
                    DB::raw('(SELECT COUNT(*) FROM order_details
                              JOIN products ON order_details.product_id = products.id
                              WHERE products.brand_id = brands.id
                              AND products.category_id = ' . $category->id . ') as sales_count')
                ]);
                $query->orderByRaw('(products_count * 0.6) + (sales_count * 0.4) DESC');
                break;
        }

        // Paginate results
        $brands = $query->paginate($perPage, ['*'], 'page', $page);

        // Return response
        return $this->success([
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug
            ],
            'brands' => new BrandsResource($brands),
            'pagination' => [
                'currentPage' => $brands->currentPage(),
                'totalPages' => $brands->lastPage(),
                'totalItems' => $brands->total(),
                'itemsPerPage' => $brands->perPage(),
            ]
        ]);
    }

    /**
     * Get a list of popular brands based on product count and sales
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPopularBrands(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50',
            'category' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $limit = $request->input('limit', 10);
        $categoryFilter = $request->input('category');

        // Start query
        $query = Brand::query();

        // Join with products to get product count
        $query->withCount('products');

        // Join with order details to get sales data
        $query->addSelect([
            'brands.*',
            DB::raw('(SELECT COUNT(*) FROM order_details
                      JOIN products ON order_details.product_id = products.id
                      WHERE products.brand_id = brands.id) as sales_count'),
            DB::raw('(SELECT COALESCE(SUM(order_details.price * order_details.quantity), 0) FROM order_details
                      JOIN products ON order_details.product_id = products.id
                      WHERE products.brand_id = brands.id) as sales_amount')
        ]);

        // Apply category filter if provided
        if ($categoryFilter) {
            // Check if it's a slug or ID
            if (is_numeric($categoryFilter)) {
                $categoryId = $categoryFilter;
            } else {
                $category = Category::where('slug', $categoryFilter)->first();
                $categoryId = $category ? $category->id : 0;
            }

            // Filter brands by category
            $query->whereHas('products', function ($q) use ($categoryId) {
                $q->where('category_id', $categoryId);
            });
        }

        // Order by a combination of product count and sales
        $query->orderByRaw('(products_count * 0.5) + (sales_count * 0.3) + (sales_amount * 0.2) DESC');

        // Paginate results
        $brands = $query->paginate($limit);

        // Return response
        return $this->success([
            'brands' => new BrandsResource($brands),
            'pagination' => [
                'currentPage' => $brands->currentPage(),
                'totalPages' => $brands->lastPage(),
                'totalItems' => $brands->total(),
                'itemsPerPage' => $brands->perPage(),
            ]
        ]);
    }

    public function get_category_by_brands(Request $request, $categorySlug)
    {
        return $this->getBrandsByCategory($request, $categorySlug);
    }
}
