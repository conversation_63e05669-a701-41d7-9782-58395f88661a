<?php

namespace App\Http\Resources\V3\User;

use Illuminate\Http\Resources\Json\JsonResource;

class DropshipperProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $status = translate('Pending');
        if($this->verification_status == 1) {
            $status = translate('Verified');
        }else if($this->verification_status == 2) {
            $status = translate('Rejected');
        }
        return [
            'id' => $this->slug,
            'company_name' => (string)$this->name,
            'business_address' => (object)json_decode($this->business_address),
            'shipping_address' => (object)json_decode($this->shipping_address),
            'tax_id' => (string)$this->tax_id,
            'business_type' => (string)$this->business_type,
            'website' => (string)$this->website,
            'status' => (string)$status,
        ];
    }
}
