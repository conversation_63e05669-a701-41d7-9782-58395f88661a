<?php

namespace App\Http\Controllers;

use App\Models\ChangeRequest;
use App\Models\Order;
use App\Notifications\order\return_exchange\OrderReturnExchangeRequestNotification;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Models\OrderDetail;
use Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeRequestController extends Controller
{
    public function __construct() {
       //  Staff Permission Check
        $this->middleware(['permission:view_refund_requests'])->only('admin_index');
        $this->middleware(['permission:view_approved_refund_requests'])->only('paid_index');
        $this->middleware(['permission:view_rejected_refund_requests'])->only('rejected_index');
        $this->middleware(['permission:refund_request_configuration'])->only('refund_config');
    }

    /**
     * @param Request $request
     * @param $id
     * @return RedirectResponse
     */
    public function change_store(Request $request, $id)
    {
        try {
            DB::beginTransaction();
                $order_detail = OrderDetail::where('id', $id)->first();
                $changeRequest = new ChangeRequest;
                $changeRequest->user_id = Auth::user()->id;
                $changeRequest->order_id = $order_detail->order_id;
                $changeRequest->order_detail_id = $order_detail->id;
                $changeRequest->seller_id = $order_detail->seller_id;
                $changeRequest->seller_approval = 0;
                $changeRequest->reason = $request->reason;
                $changeRequest->admin_approval = 0;
                $changeRequest->admin_seen = 0;
                $changeRequest->change_amount = $order_detail->price + $order_detail->tax;
                $changeRequest->change_status = 0;
                OrderDetail::where('id', $id)->update(['change_status' => 3]);
                $changeRequest->save();
            DB::commit();
            $array = array();
            $order = Order::where('id', $order_detail->order_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Exchange';
            $array['subject'] = translate('We’ve Received Your Exchange Request – ') .$order->code . " ." ;
            $array['product_name'] = $order_detail?->product?->name;

            try {
                $order->user->notify(new OrderReturnExchangeRequestNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return request email in ReturnRequestController : ' . $e->getMessage());
            }
                flash(translate("Change Request has been sent successfully"))->success();
                return redirect()->route('purchase_history.index');
        }  catch (\Exception $e) {
            DB::rollBack();
            flash(translate("something wrong"))->error();
            return redirect()->route('purchase_history.index');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function changeRequestStatusChange(Request $request) : RedirectResponse
    {

        try {
            DB::beginTransaction();
                // Update ReturnRequest
                ChangeRequest::where('id', $request->change_id)->update(['change_status' => 1]);
                // Update OrderDetail
                $changeRequest = ChangeRequest::findOrFail($request->change_id);
                OrderDetail::where('id', $changeRequest->order_detail_id)->update(['change_status' => 1]);
            if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
                ChangeRequest::where('id', $request->change_id)->update(['seller_approval' => 1, 'admin_approval' => 1 ]);
            }
            DB::commit();
            flash(translate("Change has been sent successfully"))->success();
            return back();
        }catch (\Exception $e) {
            DB::rollBack();
                flash(translate("something wrong"))->error();
            return back();
        }
    }

    public function reject_change_request(Request $request){
        try {
            DB::beginTransaction();
                $change = ChangeRequest::findOrFail($request->change_id);
                if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
                    $change->admin_approval = 2;
                    $change->change_status  = 2;
                    $change->reject_reason  = $request->reject_reason;
                    $change->save();
                }
                else{
                    $change->seller_approval = 2;
                    $change->reject_reason  = $request->reject_reason;
                    $change->save();
                }
                OrderDetail::where('id', $change->order_detail_id)->update(['change_status' => 2]);
            DB::commit();
            return back();
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving change request: ' . $e->getMessage());
            flash(translate("something wrong"))->error();
            return back();
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function admin_index()
    {
        $changes = ChangeRequest::where('change_status', 0)->latest()->paginate(15);
        return view('change_request.index', compact('changes'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function approvedChange()
    {
        $changes = ChangeRequest::where('change_status', 1)->latest()->paginate(15);
        return view('change_request.approve_return', compact('changes'));
    }

    public function rejected_index()
    {
        $changes = ChangeRequest::where('change_status', 2)->latest()->paginate(15);
        return view('change_request.rejected_change', compact('changes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function change_request_send_page($id)
    {
        $order_detail = OrderDetail::findOrFail($id);
        if ($order_detail->product != null && $order_detail->product->refundable == 1) {
            return view('change_request.frontend.change_request.create', compact('order_detail'));
        }
        else {
            return back();
        }
    }

    /**
     * Show the form for view the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function reason_view($id)
    {
        $change = ChangeRequest::findOrFail($id);
        if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            if ($change->orderDetail != null) {
                $change->admin_seen = 1;
                $change->save();
                return view('change_request.reason', compact('change'));
            }
        }
        else {
            return view('change_request.frontend.change_request.reason', compact('change'));
        }
    }

    public function reject_reason_view($id)
    {
        $refund = ChangeRequest::findOrFail($id);
        return $refund->reject_reason;
    }
}
