<?php

namespace App\Events\Messaging;

use App\Models\Messaging\Conversation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ConversationUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The conversation instance.
     *
     * @var \App\Models\Messaging\Conversation
     */
    public $conversation;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\Messaging\Conversation  $conversation
     * @return void
     */
    public function __construct(Conversation $conversation)
    {
        $this->conversation = $conversation;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to both users' private channels
        return [
            new PrivateChannel('user.' . $this->conversation->user1_id . '.conversations'),
            new PrivateChannel('user.' . $this->conversation->user2_id . '.conversations'),
            new PrivateChannel('conversation.' . $this->conversation->id),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'conversation.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        // Load relationships to include in the broadcast
        $this->conversation->load(['lastMessage']);
        
        return [
            'conversation' => [
                'id' => $this->conversation->id,
                'updated_at' => $this->conversation->updated_at,
                'last_message' => $this->conversation->lastMessage ? [
                    'id' => $this->conversation->lastMessage->id,
                    'content' => $this->conversation->lastMessage->content,
                    'sender_id' => $this->conversation->lastMessage->sender_id,
                    'created_at' => $this->conversation->lastMessage->created_at,
                ] : null,
            ],
        ];
    }
} 