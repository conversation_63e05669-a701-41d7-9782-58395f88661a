<?php

namespace App\Http\Controllers\Api\V3\Wallet;

use App\Http\Controllers\Controller;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\WalletExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Services\WalletValidationService;
use App\Services\WalletTransactionService;

class ApiWalletController extends Controller
{
    private $validationService;
    private $transactionService;

    public function __construct(WalletValidationService $validationService, WalletTransactionService $transactionService)
    {
        $this->validationService = $validationService;
        $this->transactionService = $transactionService;
    }

    public function getWalletDetails(Request $request)
    {
        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            $wallet = Wallet::create([
                'user_id' => $user->id,
                'balance' => 0,
                'reward_points' => 0,
                'promotional_credits' => 0,
                'status' => 'active'
            ]);
        }

        // Get wallet transactions for calculating balance breakdown
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'balance')
            ->where('approval_status', 'approved')
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        // Calculate balance breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'refund')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'bonuses' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'reward')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'promotionalCredits' => $wallet->promotional_credits,
            'other' => $wallet->balance - 
                WalletTransaction::where('user_id', $user->id)
                    ->where('balance_type', 'balance')
                    ->whereIn('reference_type', ['refund', 'reward'])
                    ->where('type', 'credit')
                    ->where('approval_status', 'approved')
                    ->sum('amount') - 
                $wallet->promotional_credits
        ];

        // Get wallet balance with pending amounts
        $walletBalance = [
            'totalBalance' => $wallet->balance,
            'availableBalance' => $wallet->balance,
            'pendingCredits' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'pendingDebits' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'debit')
                ->sum('amount'),
            'currency' => config('app.currency', 'USD'),
            'breakdown' => $balanceBreakdown
        ];

        // Get reward points summary
        $rewardSummary = [
            'total' => $wallet->reward_points,
            'available' => $wallet->reward_points,
            'pending' => 0,
            'used' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('type', 'debit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'expiringPoints' => 0,
            'nextExpiry' => null
        ];

        // Get exchange rates for points
        $exchangeRates = WalletExchangeRate::where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('valid_until')
                    ->orWhere('valid_until', '>', now());
            })
            ->orderBy('points', 'asc')
            ->get();

        // Format exchange rates for frontend
        $exchangeRatesObject = [
            'USD' => 1,
            'EUR' => 0.92,
            'GBP' => 0.78,
            'CAD' => 1.36,
            'AUD' => 1.52
        ];

        // Format transactions for frontend
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'amount' => (float)$transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'balance' => $walletBalance,
                'transactions' => $formattedTransactions,
                'rewards' => $rewardSummary,
                'exchangeRates' => $exchangeRatesObject
            ]
        ]);
    }

    public function getTransactions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'nullable|in:credit,debit',
            'source' => 'nullable|in:order,refund,admin,reward,promotion,purchase,promotional',
            'balance_type' => 'nullable|in:balance,reward_points,promotional_credits',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();
        
        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        $perPage = $request->per_page ?? 10;
        $page = $request->page ?? 1;

        $query = WalletTransaction::where('user_id', $user->id);

        if ($request->type) {
            $query->where('type', $request->type);
        }

        if ($request->source) {
            $query->where('reference_type', $request->source);
        }

        if ($request->balance_type) {
            $query->where('balance_type', $request->balance_type);
        }

        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Format transactions for frontend
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'amount' => (float)$transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });

        return response()->json([
            'success' => true,
            'transactions' => $formattedTransactions,
            'totalPages' => $transactions->lastPage(),
            'currentPage' => $transactions->currentPage(),
            'total' => $transactions->total()
        ]);
    }

    public function getExchangeRates()
    {
        // Provide currency exchange rates for the frontend in simplified format
        $currencyRates = [
            'USD' => 1.00,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'CAD' => 1.35,
            'AUD' => 1.45,
            'JPY' => 146.50
        ];

        return response()->json([
            'success' => true,
            'data' => $currencyRates
        ]);
    }

    public function convertPointsToCurrency(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'points' => 'required|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();
        
        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }
        
        if ($wallet->reward_points < $request->points) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient reward points'
            ], 400);
        }

        $rate = WalletExchangeRate::where('status', 'active')
            ->where('points', '<=', $request->points)
            ->where(function ($query) {
                $query->whereNull('valid_until')
                    ->orWhere('valid_until', '>', now());
            })
            ->orderBy('points', 'desc')
            ->first();

        if (!$rate) {
            return response()->json([
                'success' => false,
                'message' => 'No valid exchange rate found for the given points'
            ], 404);
        }

        $currencyAmount = ($request->points / $rate->points) * $rate->currency_amount;
        $currencyAmount = round($currencyAmount, 2);

        try {
            DB::beginTransaction();
            
            // Deduct points from wallet
            $wallet->reward_points -= $request->points;
            $wallet->balance += $currencyAmount;
            $wallet->last_updated_at = now();
            $wallet->save();
            
            // Create transaction record for points deduction
            WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $request->points,
                'balance_type' => 'reward_points',
                'description' => 'Converted reward points to wallet balance',
                'reference_type' => 'reward',
                'reference_id' => null,
                'approval_status' => 'approved',
                'metadata' => [
                    'exchange_rate_id' => $rate->id,
                    'points' => $request->points,
                    'currency_amount' => $currencyAmount
                ]
            ]);
            
            // Create transaction record for balance addition
            WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $user->id,
                'type' => 'credit',
                'amount' => $currencyAmount,
                'balance_type' => 'balance',
                'description' => 'Credit from converted reward points',
                'reference_type' => 'reward',
                'reference_id' => null,
                'approval_status' => 'approved',
                'metadata' => [
                    'exchange_rate_id' => $rate->id,
                    'points' => $request->points,
                    'currency_amount' => $currencyAmount
                ]
            ]);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Points successfully converted to wallet balance',
                'data' => [
                    'points_used' => $request->points,
                    'amount_added' => $currencyAmount,
                    'current_balance' => $wallet->balance,
                    'current_points' => $wallet->reward_points,
                    'exchange_rate' => [
                        'id' => $rate->id,
                        'points' => $rate->points,
                        'currency_amount' => $rate->currency_amount,
                        'ratio' => $rate->currency_amount / $rate->points
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to convert points: ' . $e->getMessage()
            ], 500);
        }
    }

    public function addFunds(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string|max:255',
            'reference_type' => 'nullable|in:order,refund,admin,reward,promotion',
            'reference_id' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = $request->user();
            $wallet = Wallet::where('user_id', $user->id)->first();

            if (!$wallet) {
                $wallet = Wallet::create([
                    'user_id' => $user->id,
                    'balance' => 0,
                    'reward_points' => 0,
                    'promotional_credits' => 0,
                    'status' => 'active'
                ]);
            }

            // Based on the type, update the appropriate wallet balance
            switch ($request->type) {
                case 'balance':
                    $wallet->balance += $request->amount;
                    break;
                case 'reward_points':
                    $wallet->reward_points += (int)$request->amount;
                    break;
                case 'promotional_credits':
                    $wallet->promotional_credits += $request->amount;
                    break;
            }
            
            $wallet->last_updated_at = now();
            $wallet->save();

            // Create transaction record
            $transaction = WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $user->id,
                'type' => 'credit',
                'amount' => $request->amount,
                'balance_type' => $request->type,
                'description' => $request->description,
                'reference_type' => $request->reference_type ?? 'admin',
                'reference_id' => $request->reference_id,
                'approval_status' => 'approved',
                'metadata' => [
                    'type' => $request->type,
                    'added_by' => 'user',
                    'ip_address' => $request->ip()
                ]
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Funds added successfully',
                'data' => [
                    'transaction' => [
                        'id' => (string)$transaction->id,
                        'amount' => (float)$transaction->amount,
                        'type' => $transaction->type,
                        'description' => $transaction->description,
                        'timestamp' => $transaction->created_at->toIso8601String()
                    ],
                    'wallet' => [
                        'balance' => $wallet->balance,
                        'reward_points' => $wallet->reward_points,
                        'promotional_credits' => $wallet->promotional_credits
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add funds: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deductFunds(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'type' => 'required|in:balance,reward_points,promotional_credits',
            'description' => 'required|string|max:255',
            'reference_type' => 'nullable|in:order,refund,admin,reward,promotion',
            'reference_id' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = $request->user();
            $wallet = Wallet::where('user_id', $user->id)->first();

            if (!$wallet) {
                return response()->json([
                    'success' => false,
                    'message' => 'Wallet not found'
                ], 404);
            }

            // Check if sufficient funds are available
            $currentBalance = match($request->type) {
                'balance' => $wallet->balance,
                'reward_points' => $wallet->reward_points,
                'promotional_credits' => $wallet->promotional_credits,
                default => 0
            };

            if ($currentBalance < $request->amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient funds'
                ], 400);
            }

            // Based on the type, update the appropriate wallet balance
            switch ($request->type) {
                case 'balance':
                    $wallet->balance -= $request->amount;
                    break;
                case 'reward_points':
                    $wallet->reward_points -= (int)$request->amount;
                    break;
                case 'promotional_credits':
                    $wallet->promotional_credits -= $request->amount;
                    break;
            }
            
            $wallet->last_updated_at = now();
            $wallet->save();

            // Create transaction record
            $transaction = WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $request->amount,
                'balance_type' => $request->type,
                'description' => $request->description,
                'reference_type' => $request->reference_type ?? 'admin',
                'reference_id' => $request->reference_id,
                'approval_status' => 'approved',
                'metadata' => [
                    'type' => $request->type,
                    'requested_by' => 'user',
                    'ip_address' => $request->ip()
                ]
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Funds deducted successfully',
                'data' => [
                    'transaction' => [
                        'id' => (string)$transaction->id,
                        'amount' => (float)$transaction->amount,
                        'type' => $transaction->type,
                        'description' => $transaction->description,
                        'timestamp' => $transaction->created_at->toIso8601String()
                    ],
                    'wallet' => [
                        'balance' => $wallet->balance,
                        'reward_points' => $wallet->reward_points,
                        'promotional_credits' => $wallet->promotional_credits
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to deduct funds: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reward points history with pagination
     */
    public function getRewardPointsHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();
        
        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        $perPage = $request->per_page ?? 10;
        $page = $request->page ?? 1;

        $rewardPointsHistory = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Format transactions for frontend
        $formattedRewardHistory = $rewardPointsHistory->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'amount' => (int)$transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });

        // Get reward points summary
        $rewardSummary = [
            'total' => $wallet->reward_points,
            'available' => $wallet->reward_points,
            'pending' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'used' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('type', 'debit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'expiringPoints' => 0,
            'nextExpiry' => null,
            'conversionRate' => 100 // Points per currency unit
        ];

        return response()->json([
            'success' => true,
            'rewardHistory' => $formattedRewardHistory,
            'totalPages' => $rewardPointsHistory->lastPage(),
            'currentPage' => $rewardPointsHistory->currentPage(),
            'total' => $rewardPointsHistory->total(),
            'rewards' => $rewardSummary
        ]);
    }

    public function getWalletActivities(Request $request)
    {
        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            $wallet = Wallet::create([
                'user_id' => $user->id,
                'balance' => 0,
                'reward_points' => 0,
                'promotional_credits' => 0,
                'status' => 'active'
            ]);
        }

        // Get wallet transactions
        $transactions = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'balance')
            ->where('approval_status', 'approved')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Format transactions
        $formattedTransactions = $transactions->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'amount' => (float)$transaction->amount,
                'type' => $transaction->type,
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });

        // Get reward points history
        $rewardHistory = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Format reward history
        $formattedRewardHistory = $rewardHistory->map(function($transaction) {
            return [
                'id' => (string)$transaction->id,
                'points' => (int)$transaction->amount,
                'type' => $transaction->type === 'credit' ? 'earned' : 
                         ($transaction->type === 'debit' ? 'redeemed' : 'expired'),
                'description' => $transaction->description,
                'source' => $transaction->reference_type,
                'timestamp' => $transaction->created_at->toIso8601String(),
                'expiryDate' => $transaction->metadata && isset($transaction->metadata['expiry_date']) ? 
                                $transaction->metadata['expiry_date'] : null,
                'reference' => $transaction->reference_type ? $transaction->reference_type . ':' . $transaction->reference_id : null
            ];
        });

        // Get wallet balance with breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'refund')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'bonuses' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'reward')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'promotionalCredits' => $wallet->promotional_credits,
            'other' => $wallet->balance - 
                WalletTransaction::where('user_id', $user->id)
                    ->where('balance_type', 'balance')
                    ->whereIn('reference_type', ['refund', 'reward'])
                    ->where('type', 'credit')
                    ->where('approval_status', 'approved')
                    ->sum('amount') - 
                $wallet->promotional_credits
        ];

        $walletBalance = [
            'totalBalance' => (float)$wallet->balance,
            'availableBalance' => (float)$wallet->balance,
            'pendingCredits' => (float)WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'pendingDebits' => (float)WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'debit')
                ->sum('amount'),
            'currency' => config('app.currency', 'USD'),
            'breakdown' => $balanceBreakdown
        ];

        // Get reward points summary
        $rewardPointsLifetimeEarned = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('type', 'credit')
            ->where('approval_status', 'approved')
            ->sum('amount');

        $rewardPointsLifetimeRedeemed = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('type', 'debit')
            ->where('approval_status', 'approved')
            ->sum('amount');

        $rewardPointsLifetimeExpired = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('reference_type', 'expiry')
            ->where('approval_status', 'approved')
            ->sum('amount');

        // Get expiring points
        $expiringPoints = DB::table('wallet_transactions')
            ->where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('type', 'credit')
            ->where('approval_status', 'approved')
            ->whereRaw("JSON_EXTRACT(metadata, '$.expiry_date') IS NOT NULL")
            ->whereRaw("JSON_EXTRACT(metadata, '$.expiry_date') > ?", [now()->toDateTimeString()])
            ->whereRaw("JSON_EXTRACT(metadata, '$.expiry_date') < ?", [now()->addMonths(3)->toDateTimeString()])
            ->sum('amount');

        $nextExpiryDate = DB::table('wallet_transactions')
            ->where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('type', 'credit')
            ->where('approval_status', 'approved')
            ->whereRaw("JSON_EXTRACT(metadata, '$.expiry_date') IS NOT NULL")
            ->whereRaw("JSON_EXTRACT(metadata, '$.expiry_date') > ?", [now()->toDateTimeString()])
            ->orderByRaw("JSON_EXTRACT(metadata, '$.expiry_date') ASC")
            ->value(DB::raw("JSON_EXTRACT(metadata, '$.expiry_date')"));

        if ($nextExpiryDate) {
            $nextExpiryDate = str_replace('"', '', $nextExpiryDate);
        }

        // Calculate level based on lifetime points
        $levelThresholds = [
            'Bronze' => 0,
            'Silver' => 1000,
            'Gold' => 5000,
            'Platinum' => 15000,
            'Diamond' => 30000
        ];

        $currentLevel = 'Bronze';
        $nextLevel = 'Silver';
        $currentLevelPoints = 0;
        $nextLevelPoints = 1000;

        foreach ($levelThresholds as $level => $threshold) {
            if ($rewardPointsLifetimeEarned >= $threshold) {
                $currentLevel = $level;
                $currentLevelPoints = $threshold;
            } else {
                $nextLevel = $level;
                $nextLevelPoints = $threshold;
                break;
            }
        }

        // Get lowest exchange rate for conversion rate
        $exchangeRate = WalletExchangeRate::where('status', 'active')
            ->orderBy('points', 'asc')
            ->first();

        $conversionRate = $exchangeRate ? $exchangeRate->currency_amount / $exchangeRate->points : 0.01;

        $rewardSummary = [
            'totalPoints' => (int)$wallet->reward_points,
            'availablePoints' => (int)$wallet->reward_points,
            'pendingPoints' => (int)WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'reward_points')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'expiringPoints' => (int)$expiringPoints,
            'expiringDate' => $nextExpiryDate,
            'conversionRate' => (float)$conversionRate,
            'pointsEarnedLifetime' => (int)$rewardPointsLifetimeEarned,
            'pointsRedeemedLifetime' => (int)$rewardPointsLifetimeRedeemed,
            'pointsExpiredLifetime' => (int)$rewardPointsLifetimeExpired,
            'currentLevel' => [
                'name' => $currentLevel,
                'threshold' => (int)$currentLevelPoints,
                'points' => (int)$rewardPointsLifetimeEarned,
                'progress' => $nextLevelPoints > $currentLevelPoints ? 
                            round(($rewardPointsLifetimeEarned - $currentLevelPoints) / ($nextLevelPoints - $currentLevelPoints) * 100) : 100
            ],
            'nextLevel' => [
                'name' => $nextLevel,
                'threshold' => (int)$nextLevelPoints,
                'points' => (int)$rewardPointsLifetimeEarned,
                'progress' => $nextLevelPoints > $currentLevelPoints ? 
                            round(($rewardPointsLifetimeEarned - $currentLevelPoints) / ($nextLevelPoints - $currentLevelPoints) * 100) : 100
            ]
        ];

        // Get promotional credits
        $promotionalCredits = DB::table('promotional_credits')
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expiry_date')
                    ->orWhere('expiry_date', '>', now());
            })
            ->get();

        $formattedPromotionalCredits = $promotionalCredits->map(function($credit) {
            return [
                'id' => (string)$credit->id,
                'amount' => (float)$credit->amount,
                'description' => $credit->description,
                'startDate' => $credit->created_at,
                'expiryDate' => $credit->expiry_date,
                'isActive' => (bool)$credit->is_active,
                'usageLimit' => $credit->usage_limit,
                'usageCount' => $credit->usage_count,
                'minPurchaseAmount' => $credit->min_purchase_amount,
                'applicableCategories' => $credit->applicable_categories ? json_decode($credit->applicable_categories) : null
            ];
        });

        // Get exchange rates
        $exchangeRates = [
            'USD' => 1,
            'EUR' => 0.92,
            'GBP' => 0.78,
            'CAD' => 1.36,
            'AUD' => 1.52
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'transactions' => $formattedTransactions,
                'balance' => $walletBalance,
                'rewards' => $rewardSummary,
                'rewardHistory' => $formattedRewardHistory,
                'promotionalCredits' => $formattedPromotionalCredits,
                'exchangeRates' => $exchangeRates
            ]
        ]);
    }

    public function getRewardLevelProgress(Request $request)
    {
        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        // Calculate lifetime earned points
        $rewardPointsLifetimeEarned = WalletTransaction::where('user_id', $user->id)
            ->where('balance_type', 'reward_points')
            ->where('type', 'credit')
            ->where('approval_status', 'approved')
            ->sum('amount');

        // Level thresholds
        $levelThresholds = [
            'Bronze' => 0,
            'Silver' => 1000,
            'Gold' => 5000,
            'Platinum' => 15000,
            'Diamond' => 30000
        ];

        $currentLevel = 'Bronze';
        $nextLevel = 'Silver';
        $currentLevelPoints = 0;
        $nextLevelPoints = 1000;

        foreach ($levelThresholds as $level => $threshold) {
            if ($rewardPointsLifetimeEarned >= $threshold) {
                $currentLevel = $level;
                $currentLevelPoints = $threshold;
            } else {
                $nextLevel = $level;
                $nextLevelPoints = $threshold;
                break;
            }
        }

        $pointsToNextLevel = max(0, $nextLevelPoints - $rewardPointsLifetimeEarned);
        $progressPercentage = $nextLevelPoints > $currentLevelPoints ? 
                            round(($rewardPointsLifetimeEarned - $currentLevelPoints) / ($nextLevelPoints - $currentLevelPoints) * 100) : 100;

        return response()->json([
            'success' => true,
            'data' => [
                'currentLevel' => [
                    'name' => $currentLevel,
                    'threshold' => (int)$currentLevelPoints
                ],
                'nextLevel' => [
                    'name' => $nextLevel,
                    'threshold' => (int)$nextLevelPoints
                ],
                'pointsToNextLevel' => (int)$pointsToNextLevel,
                'progressPercentage' => (int)$progressPercentage
            ]
        ]);
    }

    public function getRewardRedemptionOptions(Request $request)
    {
        // Fetch active redemption options
        $options = DB::table('reward_redemption_options')
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->get();

        $formattedOptions = $options->map(function($option) {
            return [
                'id' => (string)$option->id,
                'title' => $option->title,
                'description' => $option->description,
                'pointsCost' => (int)$option->points_cost,
                'category' => $option->category,
                'value' => (float)$option->value,
                'imageUrl' => $option->image_url,
                'expiresAt' => $option->expires_at,
                'isPopular' => (bool)$option->is_popular,
                'isLimited' => (bool)$option->is_limited,
                'remainingQuantity' => $option->is_limited ? (int)$option->remaining_quantity : null
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedOptions
        ]);
    }

    public function getRedemptionHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'perPage' => 'nullable|integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $perPage = $request->perPage ?? 10;
        $page = $request->page ?? 1;

        $redemptions = DB::table('reward_redemptions')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $formattedRedemptions = $redemptions->map(function($redemption) {
            return [
                'id' => (string)$redemption->id,
                'optionId' => (string)$redemption->option_id,
                'optionTitle' => $redemption->option_title,
                'pointsUsed' => (int)$redemption->points_used,
                'valueRedeemed' => (float)$redemption->value_redeemed,
                'redemptionDate' => $redemption->created_at,
                'status' => $redemption->status,
                'code' => $redemption->redemption_code,
                'expiresAt' => $redemption->expires_at
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedRedemptions,
            'meta' => [
                'currentPage' => $redemptions->currentPage(),
                'totalPages' => $redemptions->lastPage(),
                'totalItems' => $redemptions->total(),
                'perPage' => $redemptions->perPage()
            ]
        ]);
    }

    public function redeemRewardPoints(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'optionId' => 'required|string',
            'pointsToUse' => 'required|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        if ($wallet->reward_points < $request->pointsToUse) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient reward points',
                'error' => 'insufficient_points'
            ], 400);
        }

        // Find the redemption option
        $option = DB::table('reward_redemption_options')
            ->where('id', $request->optionId)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->first();

        if (!$option) {
            return response()->json([
                'success' => false,
                'message' => 'Redemption option not found or expired',
                'error' => 'invalid_option'
            ], 404);
        }

        if ($option->points_cost > $request->pointsToUse) {
            return response()->json([
                'success' => false,
                'message' => 'Not enough points for this redemption option',
                'error' => 'insufficient_points_for_option'
            ], 400);
        }

        // Check for limited availability
        if ($option->is_limited && $option->remaining_quantity <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'This redemption option is no longer available',
                'error' => 'out_of_stock'
            ], 400);
        }

        DB::beginTransaction();
        
        try {
            // Deduct points from wallet
            $wallet->reward_points -= $request->pointsToUse;
            $wallet->save();

            // Record transaction
            WalletTransaction::create([
                'wallet_id' => $wallet->id,
                'user_id' => $user->id,
                'type' => 'debit',
                'amount' => $request->pointsToUse,
                'balance_type' => 'reward_points',
                'description' => 'Redeemed points for ' . $option->title,
                'reference_type' => 'redemption',
                'reference_id' => $option->id,
                'approval_status' => 'approved',
                'metadata' => [
                    'option_title' => $option->title,
                    'option_category' => $option->category,
                    'value_redeemed' => $option->value
                ]
            ]);

            // Generate redemption code if necessary
            $redemptionCode = null;
            if (in_array($option->category, ['voucher', 'gift-card'])) {
                $redemptionCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 10));
            }

            // Create redemption record
            $redemptionId = DB::table('reward_redemptions')->insertGetId([
                'user_id' => $user->id,
                'option_id' => $option->id,
                'option_title' => $option->title,
                'points_used' => $request->pointsToUse,
                'value_redeemed' => $option->value,
                'status' => 'completed',
                'redemption_code' => $redemptionCode,
                'expires_at' => $option->category === 'voucher' ? now()->addDays(30) : null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Update option quantity if limited
            if ($option->is_limited) {
                DB::table('reward_redemption_options')
                    ->where('id', $option->id)
                    ->decrement('remaining_quantity');
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Points redeemed successfully',
                'data' => [
                    'pointsUsed' => $request->pointsToUse,
                    'valueRedeemed' => $option->value,
                    'code' => $redemptionCode,
                    'message' => 'You have successfully redeemed ' . $request->pointsToUse . ' points for ' . $option->title
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to redeem points',
                'error' => 'redemption_failed',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function applyRewardPointsToCheckout(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cartId' => 'required|string',
            'pointsToUse' => 'required|integer|min:1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        if ($wallet->reward_points < $request->pointsToUse) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient reward points',
                'error' => 'insufficient_points'
            ], 400);
        }

        // Find cart
        $cartInfo = DB::table('cart_info')
            ->where('id', $request->cartId)
            ->where('user_id', $user->id)
            ->first();

        if (!$cartInfo) {
            return response()->json([
                'success' => false,
                'message' => 'Cart not found',
                'error' => 'cart_not_found'
            ], 404);
        }

        // Get exchange rate for points
        $exchangeRate = WalletExchangeRate::where('status', 'active')
            ->orderBy('points', 'asc')
            ->first();

        if (!$exchangeRate) {
            return response()->json([
                'success' => false,
                'message' => 'Exchange rate not found',
                'error' => 'rate_not_found'
            ], 404);
        }

        // Calculate value of points
        $pointValue = ($request->pointsToUse * $exchangeRate->currency_amount) / $exchangeRate->points;
        
        // Make sure the discount doesn't exceed the cart total
        $discountAmount = min($pointValue, $cartInfo->grand_total);
        $newCartTotal = max(0, $cartInfo->grand_total - $discountAmount);

        // Round to 2 decimal places
        $discountAmount = round($discountAmount, 2);
        $newCartTotal = round($newCartTotal, 2);
        
        // Update cart with point redemption
        DB::table('cart_info')
            ->where('id', $request->cartId)
            ->update([
                'reward_points_used' => $request->pointsToUse,
                'reward_points_discount' => $discountAmount,
                'grand_total' => $newCartTotal,
                'updated_at' => now()
            ]);

        return response()->json([
            'success' => true,
            'data' => [
                'pointsUsed' => $request->pointsToUse,
                'amountApplied' => $discountAmount,
                'newCartTotal' => $newCartTotal,
            ]
        ]);
    }

    public function applyWalletToCheckout(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cartId' => 'required|string',
            'amount' => 'required|numeric|min:0.01'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            return response()->json([
                'success' => false,
                'message' => 'Wallet not found'
            ], 404);
        }

        if ($wallet->balance < $request->amount) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient wallet balance',
                'error' => 'insufficient_balance'
            ], 400);
        }

        // Find cart
        $cartInfo = DB::table('cart_info')
            ->where('id', $request->cartId)
            ->where('user_id', $user->id)
            ->first();

        if (!$cartInfo) {
            return response()->json([
                'success' => false,
                'message' => 'Cart not found',
                'error' => 'cart_not_found'
            ], 404);
        }
        
        // Calculate amount to apply
        $applyAmount = min($request->amount, $cartInfo->grand_total);
        $newCartTotal = max(0, $cartInfo->grand_total - $applyAmount);
        
        // Round to 2 decimal places
        $applyAmount = round($applyAmount, 2);
        $newCartTotal = round($newCartTotal, 2);
        
        // Update cart with wallet payment
        DB::table('cart_info')
            ->where('id', $request->cartId)
            ->update([
                'wallet_amount_used' => $applyAmount,
                'grand_total' => $newCartTotal,
                'updated_at' => now()
            ]);

        return response()->json([
            'success' => true,
            'data' => [
                'amountApplied' => $applyAmount,
                'newCartTotal' => $newCartTotal
            ]
        ]);
    }

    /**
     * Get wallet balance for user
     */
    public function getWalletBalance(Request $request)
    {
        $user = $request->user();
        $wallet = Wallet::where('user_id', $user->id)->first();

        if (!$wallet) {
            $wallet = Wallet::create([
                'user_id' => $user->id,
                'balance' => 0,
                'reward_points' => 0,
                'promotional_credits' => 0,
                'status' => 'active'
            ]);
        }

        // Calculate balance breakdown
        $balanceBreakdown = [
            'refunds' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'refund')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'bonuses' => WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('reference_type', 'reward')
                ->where('type', 'credit')
                ->where('approval_status', 'approved')
                ->sum('amount'),
            'promotionalCredits' => $wallet->promotional_credits,
            'other' => max(0, $wallet->balance - 
                WalletTransaction::where('user_id', $user->id)
                    ->where('balance_type', 'balance')
                    ->whereIn('reference_type', ['refund', 'reward'])
                    ->where('type', 'credit')
                    ->where('approval_status', 'approved')
                    ->sum('amount') - 
                $wallet->promotional_credits)
        ];

        $walletBalance = [
            'totalBalance' => (float)$wallet->balance,
            'availableBalance' => (float)$wallet->balance,
            'pendingCredits' => (float)WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'credit')
                ->sum('amount'),
            'pendingDebits' => (float)WalletTransaction::where('user_id', $user->id)
                ->where('balance_type', 'balance')
                ->where('approval_status', 'pending')
                ->where('type', 'debit')
                ->sum('amount'),
            'currency' => config('app.currency', 'USD'),
            'breakdown' => $balanceBreakdown
        ];

        return response()->json([
            'success' => true,
            'data' => $walletBalance
        ]);
    }
} 