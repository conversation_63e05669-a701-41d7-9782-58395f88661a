<?php

namespace App\Http\Resources\V3\Coupon;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class CouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'title' => $this->title,
            'description' => $this->description,
            'discountType' => $this->discount_type,
            'discountValue' => (float) $this->discount_value,
            'minimumPurchase' => isset($this->minimum_purchase) ? (float) $this->minimum_purchase : null,
            'maximumDiscount' => isset($this->maximum_discount) ? (float) $this->maximum_discount : null,
            'startDate' => is_int($this->start_date) ? Carbon::createFromTimestamp($this->start_date)->toIso8601String() : $this->start_date->toIso8601String(),
            'endDate' => isset($this->end_date) ? (is_int($this->end_date) ? Carbon::createFromTimestamp($this->end_date)->toIso8601String() : $this->end_date->toIso8601String()) : null,
            'usageLimit' => $this->usage_limit,
            'usageCount' => (int) $this->usage_count,
            'isActive' => (bool) $this->is_active,
            'isAppliedToCart' => $this->when(isset($this->is_applied_to_cart), (bool) $this->is_applied_to_cart, false),
            'appliedDiscount' => $this->when(isset($this->applied_discount), (float) $this->applied_discount, null),
            'restrictions' => [
                'customerGroups' => $this->customer_groups,
                'excludedProductIds' => $this->excluded_product_ids,
                'includedProductIds' => $this->product_ids,
                'excludedCategoryIds' => $this->excluded_category_ids,
                'includedCategoryIds' => $this->category_ids,
                'usableWith' => $this->usable_with,
            ],
            'isFirstTimeOnly' => (bool) $this->is_first_time_only,
            'isSingleUse' => (bool) $this->is_single_use,
            'isFeatured' => (bool) $this->is_featured,
            'isPersonalized' => (bool) $this->is_personalized,
            'image' => $this->image,
            'terms' => $this->terms,
            'categoryId' => $this->category_id,
            'categoryName' => $this->when($this->category_id, function () {
                return optional($this->category)->name;
            }),
            'created_at' => $this->created_at instanceof Carbon ? $this->created_at->toIso8601String() : Carbon::createFromTimestamp($this->created_at)->toIso8601String(),
            'updated_at' => $this->updated_at instanceof Carbon ? $this->updated_at->toIso8601String() : Carbon::createFromTimestamp($this->updated_at)->toIso8601String(),
        ];
    }
}
