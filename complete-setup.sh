#!/bin/bash

# Complete Docker Setup Script for Laravel Backend
# This script sets up everything you need for Laravel development with Docker

echo "🚀 Complete Docker Setup for Laravel Backend"
echo "=============================================="
echo ""

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Error: Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Docker is running"

# Check if we're in the right directory
if [ ! -d "laradock" ] || [ ! -d "backend" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "Expected structure:"
    echo "  - laradock/ (Docker configuration)"
    echo "  - backend/ (Laravel application)"
    exit 1
fi

echo "✅ Project structure verified"

# Step 1: Configure <PERSON><PERSON> for Docker
echo ""
echo "📝 Step 1: Configuring <PERSON><PERSON> for Docker..."
if [ -f "setup-laravel.sh" ]; then
    ./setup-laravel.sh
else
    echo "⚠️  Laravel setup script not found, skipping..."
fi

# Step 2: Start Docker containers
echo ""
echo "🐳 Step 2: Starting Docker containers..."
cd laradock

echo "Building and starting containers (this may take a few minutes on first run)..."
docker-compose up -d nginx mysql phpmyadmin workspace

echo "⏳ Waiting for services to initialize..."
sleep 15

# Step 3: Install Laravel dependencies
echo ""
echo "📦 Step 3: Installing Laravel dependencies..."
echo "Installing Composer dependencies..."
docker-compose exec -T workspace bash -c "cd /var/www/backend && composer install --no-interaction"

# Step 4: Generate application key
echo ""
echo "🔑 Step 4: Generating Laravel application key..."
docker-compose exec -T workspace bash -c "cd /var/www/backend && php artisan key:generate --no-interaction"

# Step 5: Run database migrations
echo ""
echo "🗄️  Step 5: Setting up database..."
echo "Waiting for MySQL to be ready..."
sleep 10

echo "Running database migrations..."
docker-compose exec -T workspace bash -c "cd /var/www/backend && php artisan migrate --no-interaction" || echo "⚠️  Migrations failed - you may need to run them manually"

# Step 6: Set proper permissions
echo ""
echo "🔧 Step 6: Setting proper permissions..."
docker-compose exec -T workspace bash -c "cd /var/www/backend && chmod -R 775 storage bootstrap/cache"

# Step 7: Check container status
echo ""
echo "🔍 Step 7: Checking container status..."
docker-compose ps

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "🌐 Your Laravel application is now running:"
echo "   - Laravel App: http://localhost"
echo "   - phpMyAdmin: http://localhost:8081"
echo ""
echo "🔧 Database Configuration:"
echo "   - Host: mysql (from containers) / localhost (from host)"
echo "   - Database: default"
echo "   - Username: default"
echo "   - Password: secret"
echo "   - Root Password: root"
echo "   - Port: 3306"
echo ""
echo "💻 Useful Commands:"
echo "   - Access workspace: docker-compose exec workspace bash"
echo "   - View logs: docker-compose logs -f nginx"
echo "   - Stop containers: docker-compose down"
echo "   - Restart containers: docker-compose restart"
echo ""
echo "📚 For more information, see DOCKER_README.md"

# Return to project root
cd ..

echo ""
echo "✅ All done! Happy coding! 🚀"
