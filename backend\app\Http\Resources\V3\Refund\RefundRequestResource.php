<?php

namespace App\Http\Resources\V3\Refund;

use App\Models\OrderDetail;
use App\Models\ReturnMethod;
use App\Models\Upload;
use Illuminate\Http\Resources\Json\JsonResource;

class RefundRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $refundMethod ='';
        if($this->refundMethod !== null || $this->refundMethod !=""){
            $refundMethod = optional(ReturnMethod::where('id', $this->refundMethod)->first())->name;
        }
        $commonFields = [
            'id' => $this->refund_code,
            'orderCode' => $this->order->code,
            'orderDate' => date('d-m-Y h:i A', $this->order->date),
            'requestDate' => date('d-m-Y h:i A', $this->request_date),
            'status' => ucfirst($this->refund_status->value),
            'refundMethod' => $refundMethod,
            'reason_for_refund' => $this->reason_for_refund,
            'description' => $this->user_note,
            'refundAmount' => (float) $this->amount,
            'products' => $this->refund_request_products->map(function ($item) {
                $orderDetail = OrderDetail::where('id', $item->order_detail_id)->first(); $detail = OrderDetail::where('id', $item->order_detail_id)->first();
                return [
                    'id' => $item->product->slug,
                    'name' => $item->product->name,
                    'thumbnail' => $item->product->thumbnail_img ? uploaded_asset($item->product->thumbnail_img) : "",
                    'quantity' => (float) $item->quantity,
                    'price' =>(float) $item->unit_price,
                    'reason' => '',
                    'condition' => '',
                    'sku' => (string) product_sku_by_product_id($item->product,$orderDetail->variation) ?? Null,
                ];
            }),

        ];
        if($request->route()->getActionMethod()==='index'){
            return $commonFields;
        }else{
            return array_merge($commonFields,[
                'attachments'=> $this->attachments ? array_map(function ($image) {
                    $upload = Upload::where('id', $image)->first();
                    return [
                        'type' => $upload->type,
                        'url' => uploaded_asset($image),
                    ];
                }, explode(',', $this->attachments)) : [] ,
                'timeline' =>$this->getReturnRequestTimeline($this->activityLogs)
            ]);
        }

    }
    private function getReturnRequestTimeline($activityLogs)
    {
        return $activityLogs->map(function ($log) {
            return [
                'status' => ucfirst($log->new_status),
                'timestamp' => $log->created_at->toIso8601String(),
                'description' => $log->description,

            ];
        });
    }
}
