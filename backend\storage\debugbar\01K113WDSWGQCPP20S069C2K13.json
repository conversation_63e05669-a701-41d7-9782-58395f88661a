{"__meta": {"id": "01K113WDSWGQCPP20S069C2K13", "datetime": "2025-07-25 08:40:30", "utime": **********.396362, "method": "GET", "uri": "/buzfi-new-backend/api/v3/features", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.184356, "end": **********.396371, "duration": 0.21201491355895996, "duration_str": "212ms", "measures": [{"label": "Booting", "start": **********.184356, "relative_start": 0, "end": **********.330668, "relative_end": **********.330668, "duration": 0.*****************, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.330673, "relative_start": 0.****************, "end": **********.396372, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "65.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.338127, "relative_start": 0.*****************, "end": **********.340589, "relative_end": **********.340589, "duration": 0.002462148666381836, "duration_str": "2.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.394309, "relative_start": 0.*****************, "end": **********.394489, "relative_end": **********.394489, "duration": 0.0001800060272216797, "duration_str": "180μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.395308, "relative_start": 0.****************, "end": **********.395339, "relative_end": **********.395339, "duration": 3.0994415283203125e-05, "duration_str": "31μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01433, "accumulated_duration_str": "14.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `wizard_infos` where `slug` = 'home-page-promotional-features' and `wizard_info_status` = 1 order by `wizard_info_position` asc limit 1", "type": "query", "params": [], "bindings": ["home-page-promotional-features", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 173}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.372771, "duration": 0.01308, "duration_str": "13.08ms", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:173", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=173", "ajax": false, "filename": "ApiHomePageController.php", "line": "173"}, "connection": "buzfi", "explain": null, "start_percent": 0, "width_percent": 91.277}, {"sql": "select * from `wizard_details` where `wizard_details`.`wizard_info_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 173}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.387868, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ApiHomePageController.php:173", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/V3/ApiHomePageController.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Controllers\\Api\\V3\\ApiHomePageController.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=173", "ajax": false, "filename": "ApiHomePageController.php", "line": "173"}, "connection": "buzfi", "explain": null, "start_percent": 91.277, "width_percent": 2.233}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.391073, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BannerResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FBannerResource.php&line=18", "ajax": false, "filename": "BannerResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 93.51, "width_percent": 1.745}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.3922112, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BannerResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FBannerResource.php&line=18", "ajax": false, "filename": "BannerResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 95.255, "width_percent": 1.884}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.39295, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BannerResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FBannerResource.php&line=18", "ajax": false, "filename": "BannerResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 97.139, "width_percent": 1.465}, {"sql": "select * from `wizard_infos` where `wizard_infos`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 155}, {"index": 28, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 17}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 108}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 255}], "start": **********.393606, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BannerResource.php:18", "source": {"index": 21, "namespace": null, "name": "app/Http/Resources/V3/BannerResource.php", "file": "D:\\Development\\laragon\\www\\buzfi-new-backend\\app\\Http\\Resources\\V3\\BannerResource.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FResources%2FV3%2FBannerResource.php&line=18", "ajax": false, "filename": "BannerResource.php", "line": "18"}, "connection": "buzfi", "explain": null, "start_percent": 98.604, "width_percent": 1.396}]}, "models": {"data": {"App\\Models\\WizardInfo": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardInfo.php&line=1", "ajax": false, "filename": "WizardInfo.php", "line": "?"}}, "App\\Models\\WizardDetail": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FModels%2FWizardDetail.php&line=1", "ajax": false, "filename": "WizardDetail.php", "line": "?"}}}, "count": 9, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 9}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/features", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@promotional_features", "uri": "GET api/v3/features", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@promotional_features<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=167\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=167\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:167-176</a>", "middleware": "api", "duration": "212ms", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-291657661 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-291657661\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1963981672 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1963981672\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-378932721 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c03caeb96aca979edafe679b36e9fda5676f10e64d67c27e; XSRF-TOKEN=eyJpdiI6Ik94WDlyU0tRaGdnUDBKUEtibEJtNkE9PSIsInZhbHVlIjoiNytSa1diZXZETUxxRkx6UXdiK2loYUJadi9XSUdXOG9sTHZ0Y0RNcnR4VnlNb21wOUVic3dQZHlvVU0yZ3h1NTVIRWRVb1FOS1hlL0dOdUJ1TGtIRDRBVFJZdnBmS2ZUMDBLaXBUbHlVZG5maXovZzVhM0dvNWVsVy8vSWQxZUEiLCJtYWMiOiIzZGMzZmEyZDNmNGU4MTIwZTExYjZjOTgzZTkwYWFlYzBiYzg1ZDA0ODc1MGU0NDhiZjkyNTA5ODA0YTA0ZDI0IiwidGFnIjoiIn0%3D; buzficom_session=BmgAhSSukcFjvrNkGTdxUHGCE1DHxtQElGkxseuG</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378932721\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1566704405 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e8zfbw8IWBryoCeFcbmkaqfCxIBGuuCTa4qmC48A</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566704405\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1529877115 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">max-age=3600, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:40:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">567</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9uRjVmdi80M01iNEY0TmJ5Yjl6ZFE9PSIsInZhbHVlIjoiZ3RaRkhJM1BNZExuUXhZM1RsUVZUZll2V0thV3ByYkVSdGlVSk1iaVdRTEp6R005R0loZlJnTENqWWRvYnh6RWJvVzg3aFJuS1paNks0RHhZeWQ0NEtvdFRJMWQxV0hEcUFJcjM1MndOcXRYV0kza3FoNVhmSkxtZDdqUVpKNlMiLCJtYWMiOiI0M2VjOTNmOTMxNjVlMjMxZjNlNGRmNzU5ZjBiYzBlNjgzOGVlYTBiNDlhMDcyNDg2YjRiYWYxNjI0MDU0MWQyIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6InpUbEFDaXNpcTdub2dFWmJ4YVV5Y0E9PSIsInZhbHVlIjoiSndBU3EwTCtQWTVIMUdoblRBeUVHa1NFMVNDbVBYVHJUMllFVXBUdllyb21TeExoZWtMcVFkd010M2lPMkpHZFJOc1RVNnlYaWlGSFNoOVQvUldBVkppU1lJRjk3TmFSbHpkSitVamNMZ1MzTzUrdkhicGZ3bG91ZXlZZGkrYXgiLCJtYWMiOiIzNDJjNjhhOTk2OTZiNGM1ZWNhMTM0NjYxM2FhYzVmZGI5NGE0NTZiYjkxOTNmZjg1ZmZkODcyZDM0MzNkMDkzIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:40:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9uRjVmdi80M01iNEY0TmJ5Yjl6ZFE9PSIsInZhbHVlIjoiZ3RaRkhJM1BNZExuUXhZM1RsUVZUZll2V0thV3ByYkVSdGlVSk1iaVdRTEp6R005R0loZlJnTENqWWRvYnh6RWJvVzg3aFJuS1paNks0RHhZeWQ0NEtvdFRJMWQxV0hEcUFJcjM1MndOcXRYV0kza3FoNVhmSkxtZDdqUVpKNlMiLCJtYWMiOiI0M2VjOTNmOTMxNjVlMjMxZjNlNGRmNzU5ZjBiYzBlNjgzOGVlYTBiNDlhMDcyNDg2YjRiYWYxNjI0MDU0MWQyIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6InpUbEFDaXNpcTdub2dFWmJ4YVV5Y0E9PSIsInZhbHVlIjoiSndBU3EwTCtQWTVIMUdoblRBeUVHa1NFMVNDbVBYVHJUMllFVXBUdllyb21TeExoZWtMcVFkd010M2lPMkpHZFJOc1RVNnlYaWlGSFNoOVQvUldBVkppU1lJRjk3TmFSbHpkSitVamNMZ1MzTzUrdkhicGZ3bG91ZXlZZGkrYXgiLCJtYWMiOiIzNDJjNjhhOTk2OTZiNGM1ZWNhMTM0NjYxM2FhYzVmZGI5NGE0NTZiYjkxOTNmZjg1ZmZkODcyZDM0MzNkMDkzIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:40:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529877115\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1204016861 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">e8zfbw8IWBryoCeFcbmkaqfCxIBGuuCTa4qmC48A</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"50 characters\">http://localhost/buzfi-new-backend/api/v3/features</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204016861\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/features", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@promotional_features"}, "badge": null}}