<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Utility\SmsUtility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SmsController extends Controller
{
    /**
     * Show SMS sending page
     */
    public function index()
    {
        $users = User::where('user_type', 'customer')->get();
        return view('backend.sms.index', compact('users'));
    }

    /**
     * Send SMS to users
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'users' => 'required|array',
            'users.*' => 'exists:users,id',
            'message' => 'required|string|max:160'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $users = User::whereIn('id', $request->users)->get();
        $message = $request->message;
        $sent_count = 0;
        $failed_count = 0;

        foreach ($users as $user) {
            if (!empty($user->phone)) {
                try {
                    $result = SmsUtility::send_sms($user->phone, $message);
                    if ($result) {
                        $sent_count++;
                    } else {
                        $failed_count++;
                    }
                } catch (\Exception $e) {
                    $failed_count++;
                    \Log::error('SMS Send Error for user ' . $user->id . ': ' . $e->getMessage());
                }
            } else {
                $failed_count++;
            }
        }

        if ($sent_count > 0) {
            flash(translate('SMS sent successfully to ') . $sent_count . translate(' users'))->success();
        }
        
        if ($failed_count > 0) {
            flash(translate('Failed to send SMS to ') . $failed_count . translate(' users'))->warning();
        }

        return redirect()->back();
    }

    /**
     * Send bulk SMS to all customers
     */
    public function sendBulk(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:160',
            'user_type' => 'nullable|in:customer,seller,all'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user_type = $request->user_type ?? 'customer';
        
        if ($user_type == 'all') {
            $users = User::whereNotNull('phone')->get();
        } else {
            $users = User::where('user_type', $user_type)->whereNotNull('phone')->get();
        }

        $message = $request->message;
        $sent_count = 0;
        $failed_count = 0;

        foreach ($users as $user) {
            try {
                $result = SmsUtility::send_sms($user->phone, $message);
                if ($result) {
                    $sent_count++;
                } else {
                    $failed_count++;
                }
            } catch (\Exception $e) {
                $failed_count++;
                \Log::error('Bulk SMS Send Error for user ' . $user->id . ': ' . $e->getMessage());
            }
        }

        if ($sent_count > 0) {
            flash(translate('Bulk SMS sent successfully to ') . $sent_count . translate(' users'))->success();
        }
        
        if ($failed_count > 0) {
            flash(translate('Failed to send SMS to ') . $failed_count . translate(' users'))->warning();
        }

        return redirect()->back();
    }

    /**
     * Send SMS notification for specific events
     */
    public function sendNotification($phone, $message)
    {
        try {
            return SmsUtility::send_sms($phone, $message);
        } catch (\Exception $e) {
            \Log::error('SMS Notification Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get SMS sending statistics
     */
    public function getStats()
    {
        // This can be implemented later to track SMS sending statistics
        return [
            'total_sent' => 0,
            'total_failed' => 0,
            'last_sent' => null
        ];
    }
} 