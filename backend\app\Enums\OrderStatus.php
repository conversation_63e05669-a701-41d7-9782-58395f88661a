<?php

namespace App\Enums;

class OrderStatus
{
    const PENDING = 'pending';
    const ORDER_PLACED = 'order_placed';
    const PROCESSING = 'processing';
    const IN_PROGRESS = 'in_progress';
    const CONFIRMED = 'confirmed';
    const PICKED_UP = 'picked_up';
    const SHIPPED = 'shipped';
    const ON_THE_WAY = 'on_the_way';
    const DELIVERED = 'delivered';
    const COMPLETED = 'completed';
    const CANCELLED = 'cancelled';
    const ON_HOLD = 'on_hold';
    const FAILED = 'failed';
    const RETURN = 'return';
    const RETURNED_DELIVERY = 'returned_delivery';
    const REFUNDED = 'refunded';
    const REFUND = 'refund';
    const RESHIPPED = 'reshipped';

    public static function getValues(): array
    {
        return [
            self::PENDING,
            self::ORDER_PLACED,
            self::PROCESSING,
            self::IN_PROGRESS,
            self::CONFIRMED,
            self::PICKED_UP,
            self::SHIPPED,
            self::ON_THE_WAY,
            self::DELIVERED,
            self::COMPLETED,
            self::CANCELLED,
            self::ON_HOLD,
            self::FAILED,
            self::RETURN,
            self::RETURNED_DELIVERY,
            self::REFUNDED,
            self::REFUND,
            self::RESHIPPED
        ];
    }

    public static function getLabels(): array
    {
        return [
            self::PENDING => 'Pending',
            self::ORDER_PLACED => 'Order Placed',
            self::PROCESSING => 'Processing',
            self::IN_PROGRESS => 'In Progress',
            self::CONFIRMED => 'Confirmed',
            self::PICKED_UP => 'Picked Up',
            self::SHIPPED => 'Shipped',
            self::ON_THE_WAY => 'On The Way',
            self::DELIVERED => 'Delivered',
            self::COMPLETED => 'Completed',
            self::CANCELLED => 'Cancelled',
            self::ON_HOLD => 'On Hold',
            self::FAILED => 'Failed',
            self::RETURN => 'Returned',
            self::RETURNED_DELIVERY => 'Returned Delivery',
            self::REFUNDED => 'Refunded',
            self::REFUND => 'Refund',
            self::RESHIPPED => 'Reshipped',
        ];
    }

    public static function getLabel(string $status): string
    {
        return self::getLabels()[$status] ?? 'Unknown';
    }
}
