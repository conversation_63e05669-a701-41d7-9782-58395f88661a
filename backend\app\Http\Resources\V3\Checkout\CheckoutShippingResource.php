<?php

namespace App\Http\Resources\V3\Checkout;

use Illuminate\Http\Resources\Json\JsonResource;

class CheckoutShippingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Calculate estimated delivery dates
        $minDate = now()->addDays($this->shippingMethod->min_days)->format('Y-m-d');
        $maxDate = now()->addDays($this->shippingMethod->max_days)->format('Y-m-d');

        return [
            'message' => 'Shipping method selected',
            'shippingMethod' => [
                'id' => $this->shippingMethod->id,
                'name' => $this->shippingMethod->name,
                'description' => $this->shippingMethod->description,
                'price' => (float) $this->shippingMethod->price,
                'estimatedDelivery' => [
                    'minDate' => $minDate,
                    'maxDate' => $maxDate
                ]
            ],
            'updatedTotals' => [
                'subtotal' => (float) $this->totals['subtotal'],
                'shipping' => (float) $this->totals['shipping'],
                'tax' => (float) $this->totals['tax'],
                'discount' => (float) $this->totals['discount'],
                'total' => (float) $this->totals['total']
            ]
        ];
    }
}
