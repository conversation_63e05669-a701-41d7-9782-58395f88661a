<?php

namespace App\Http\Controllers\Admin;

use App\Enums\NotificationPriority;
use App\Enums\NotificationType;
use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Models\BusinessSetting;
use App\Models\Order;
use App\Models\RefundRequest;
use App\Models\ReturnRequestInfo;
use App\Notifications\order\return_exchange\ReturnApprovedNotification;
use App\Services\ActivityLogService;
use App\Services\NotificationService;
use App\Services\UserNotificationServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ReturnRefundController extends Controller
{
    /*
     * Cash
     * Cache::forget('return_faq')
     *
     * */
    protected $notificationService;
    protected ActivityLogService $activityLogService;
    protected UserNotificationServices $userNotificationServices;
    public function __construct(
        ActivityLogService $activityLogService,
        UserNotificationServices $userNotificationServices,
        NotificationService $notificationService
    )
    {
        $this->activityLogService = $activityLogService;
        $this->userNotificationServices = $userNotificationServices;
        $this->notificationService = $notificationService;
    }

    public function index(Request $request)
    {
        $returns = ReturnRequestInfo::query()->latest()->paginate(15);
        return view('backend.return_refund.return_request_list', compact('returns'));
    }
    public function return_request_details(Request $request, $return_code)
    {
        $return_request_info = ReturnRequestInfo::with('return_request_products', 'order', 'return_request_products.product', 'activityLogs')->where('return_code', $return_code)->first();
        return view('backend.return_refund.return_request_details', compact('return_request_info'));
    }
    public function change_return_status(Request $request)
    {
        $request->validate([
            'return_id' => 'required',
            'return_status' => 'required',
            'admin_note' => 'required',
        ]);

        $return_request_info = ReturnRequestInfo::find($request->return_id);
        $old_status = $return_request_info->return_status;
        $return_request_info->admin_note = $request->admin_note;
        $return_request_info->return_status = $request->return_status;
        $return_request_info->save();


        if ($request->return_status != $old_status) {
            $this->activityLogService->log(
                'return_request',
                'Admin updated return request status',
                $return_request_info->id,
                ReturnRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                $old_status,
                $request->return_status,
                $request->admin_note,
                email_end_time: null,
                source_id: $return_request_info->order_id,
                source_type: Order::class
            );
            $this->notificationService->sendNotification(
                $return_request_info->user,
                'Return Request Status Updated',
                "Your Return Request #{$return_request_info->return_code} status has been updated to: " . ucfirst($request->return_status),
                NotificationType::RETURN_REQUEST,
                NotificationPriority::MEDIUM,
                'v3/refund-request/' . $return_request_info->return_code,
                'see the return request details',
                $return_request_info,
                false,
                'admin',
            );
           /* $this->userNotificationServices->saveNotification(
                $return_request_info->user_id,
                NotificationType::ORDER->value,
                $return_request_info->id,
                ReturnRequestInfo::class,
                'Update On Return Request',
                'Admin updated return request status to ' . $request->return_status,
                NotificationPriority::MEDIUM->value,
                0,
                'v3/refund-request/' . $return_request_info->return_code,
                'see the return request details',
                []
            );*/
        }
        if ($request->return_status == ReturnStatus::COMPLETED->value) {

            $refund = new RefundRequest;
            $refund->user_id = auth()->user()->id;
            $refund->return_request_info_id = $return_request_info->id;
            $refund->order_id = $return_request_info->order_id;
            $refund->reason = $request->reason;
            $refund->admin_approval = 0;
            $refund->admin_seen = 0;
            $refund->refund_amount = $return_request_info->amount;
            $refund->refund_status = 0;
            $refund->save();
            $products = [];
            foreach ($return_request_info->return_request_products as $return_request_product) {
                if ($return_request_product->product && $return_request_product->product->name) {
                    $products[] = $return_request_product->product->name;
                }
            }

            $array = array();
            $order = Order::where('id', $return_request_info->order_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Return';
            $array['subject'] = translate(' Your Return Request Has Been Approved  – ') . $order->code . " .";
            $array['product_name'] = implode(', ', $products);

            try {
                $order->user->notify(new ReturnApprovedNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return Approved  email in ReturnRequestController : ' . $e->getMessage());
            }
        }


        flash(translate('Status has been updated successfully'))->success();
        return redirect()->back();
    }
    public function return_configuration()
    {
        if (!BusinessSetting::where('type', 'return_faq')->exists()) {
            // Insert the default data if it doesn't exist
            BusinessSetting::create([
                'type' => 'return_faq',
                'value' => json_encode([
                    [
                        'id' => 'general',
                        'name' => 'General Questions',
                        'description' => 'Basic information about returns and refunds',
                        'order' => 1,
                        'faqs' => [
                            [
                                'id' => 'faq-1',
                                'question' => "What is Buzfi's return policy?",
                                'answer' => "Buzfi offers a 30-day return policy for most items. Products must be in their original condition, with all packaging and accessories. Some product categories such as perishables, personalized items, and digital downloads are not eligible for return.",
                                'is_popular' => true,
                                'order' => 1,
                            ],
                            [
                                'id' => 'faq-2',
                                'question' => 'Are there any fees for returning items?',
                                'answer' => "Standard returns are free if the item is defective, damaged, or not as described. For change-of-mind returns, a restocking fee of 15% may apply for some product categories. Return shipping fees may also apply unless the return is due to our error.",
                                'is_popular' => true,
                                'order' => 2,
                            ]
                        ],
                    ],
                    [
                        'id' => 'eligibility',
                        'name' => 'Return Eligibility',
                        'description' => 'Questions about what items can be returned',
                        'order' => 2,
                        'faqs' => [
                            [
                                'id' => 'faq-3',
                                'question' => 'Can I return opened items?',
                                'answer' => "Yes, you can return opened items as long as they are in resalable condition. However, certain items like software, music, movies, and personal hygiene products cannot be returned once opened unless they are defective.",
                                'is_popular' => false,
                                'order' => 1,
                            ]
                        ],
                    ],
                    [
                        'id' => 'process',
                        'name' => 'Return Process',
                        'description' => 'How to initiate and complete a return',
                        'order' => 3,
                        'faqs' => [
                            [
                                'id' => 'faq-4',
                                'question' => 'How do I start a return?',
                                'answer' => "To initiate a return, go to your order history, select the order containing the item(s) you wish to return, and click 'Return Items'. Follow the guided process to complete your return request.",
                                'is_popular' => true,
                                'order' => 1,
                            ]
                        ],
                    ],
                    [
                        'id' => 'refunds',
                        'name' => 'Refunds',
                        'description' => 'Questions about refund processing and timelines',
                        'order' => 4,
                        'faqs' => [
                            [
                                'id' => 'faq-5',
                                'question' => 'How long do refunds take to process?',
                                'answer' => "Once we receive your returned item(s), it typically takes 1-2 business days to inspect and process the return. After processing, refunds are issued to your original payment method and may take an additional 3-5 business days to appear on your statement, depending on your financial institution.",
                                'is_popular' => true,
                                'order' => 1,
                            ]
                        ],
                    ]
                ], JSON_PRETTY_PRINT),
                'lang' => null, // if required
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $data['return_faq'] = json_decode(BusinessSetting::where('type', 'return_faq')->first()->value, true);
        return view('backend.return_refund.return_configuration', $data);
    }

    public function save_return_faq(Request $request)
    {
        //update  return_faq cache
        $faq_data = json_decode($request->faq_data, true);
        $business_setting = BusinessSetting::where('type', 'return_faq')->first();
        $business_setting->value = json_encode($faq_data);
        $business_setting->save();

        return response()->json(['success' => true]);
    }

    public function return_request_configuration()
    {
        if (!BusinessSetting::where('type', 'return_request_configuration')->exists()) {
            // Insert the default data if it doesn't exist
            BusinessSetting::create([
                'type' => 'return_request_configuration',
                'value' => json_encode([
                    [
                        'return_policy' => [
                            'heading' => 'Return Policy Information',
                            'description' => 'Items must be returned within 30 days of delivery. Please ensure items are in original condition with all tags attached.',
                        ],
                        'reason_for_return' => [
                            ['text' => 'Defective/Damaged', 'value' => 'defective_damaged', 'details' => 'Item arrived damaged or not working'],
                            ['text' => 'Wrong Item', 'value' => 'wrong_item', 'details' => 'Received different item than ordered'],
                            ['text' => 'Not as Described', 'value' => 'not_as_described', 'details' => 'Item doesn\'t match description'],
                            ['text' => 'Changed Mind', 'value' => 'changed_mind', 'details' => 'No longer want the item'],
                        ],
                        'preferred_resolution' => [
                            ['text' => 'Full Refund', 'value' => 'full_refund'],
                            ['text' => 'Exchange for Different Item', 'value' => 'exchange_for_different_item'],
                            ['text' => 'Store Credit', 'value' => 'store_credit'],
                            ['text' => 'Partial Refund', 'value' => 'partial_refund'],
                        ],
                        'terms' => [
                            ['text' => 'Items must be returned within 30 days of delivery', 'value' => 'within_30_days'],
                            ['text' => 'Items must be in original condition with all tags attached', 'value' => 'original_condition_with_tags'],
                            ['text' => 'Refunds will be processed within 5-7 business days after receiving the returned items', 'value' => 'refunds_processed_5_7_days'],
                            ['text' => 'Shipping costs may not be refunded unless the return is due to our error', 'value' => 'shipping_costs_non_refundable'],
                        ],
                    ],
                ], JSON_PRETTY_PRINT),  // Wrap the object inside an array
                'lang' => null, // If you need a language column
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        $return_request_configuration = BusinessSetting::where('type', 'return_request_configuration')->first();
        $data['return_request_config'] = json_decode($return_request_configuration->value, true);
        $return_policy_pdf = BusinessSetting::firstOrCreate(
            ['type' => 'return_policy_pdf'],
            ['value' => null]
        );
        $data['return_policy_pdf'] = $return_policy_pdf->value;

        $return_faq_pdf = BusinessSetting::firstOrCreate(
            ['type' => 'return_faq_pdf'],
            ['value' => null]
        );
        $data['return_faq_pdf'] = $return_faq_pdf->value;
        $return_packaging_pdf = BusinessSetting::firstOrCreate(
            ['type' => 'return_packaging_pdf'],
            ['value' => null]
        );
        $data['return_packaging_pdf'] = $return_packaging_pdf->value;

        return view('backend.return_refund.return_request_configuration', $data);
    }

    public function upload_return_request_pdf(Request $request){
        BusinessSetting::where('type', 'return_policy_pdf')->update(['value' => $request->return_policy_pdf]);
        BusinessSetting::where('type', 'return_faq_pdf')->update(['value' => $request->return_faq_pdf]);
        BusinessSetting::where('type', 'return_packaging_pdf')->update(['value' => $request->return_packaging_pdf]);
        flash(translate('Return Related PDFs uploaded successfully'))->success();
        return redirect()->route('return_request_configuration');

    }

    public function save_return_request_configuration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'return_policy_heading' => 'required|string|max:255',
            'return_policy_description' => 'required|string',
            'reason_for_return' => 'required|array|min:1',
            'reason_for_return.*.text' => 'required|string|max:255',
            'reason_for_return.*.value' => 'required|string|max:255',
            'reason_for_return.*.details' => 'required|string',
            'preferred_resolution' => 'required|array|min:1',
            'preferred_resolution.*.text' => 'required|string|max:255',
            'preferred_resolution.*.value' => 'required|string|max:255',
            'terms' => 'required|array|min:1',
            'terms.*.text' => 'required|string',
            'terms.*.value' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            flash(translate('Validation failed'))->error();
            return back()->withErrors($validator)->withInput();
        }

        // Prepare the configuration data
        $config = [
            [
                'return_policy' => [
                    'heading' => $request->return_policy_heading,
                    'description' => $request->return_policy_description
                ],
                'reason_for_return' => $request->reason_for_return,
                'preferred_resolution' => $request->preferred_resolution,
                'terms' => $request->terms
            ]
        ];

        // Update the business setting
        $business_setting = BusinessSetting::where('type', 'return_request_configuration')->first();
        if (!$business_setting) {
            $business_setting = new BusinessSetting;
            $business_setting->type = 'return_request_configuration';
        }

        $business_setting->value = json_encode($config);
        $business_setting->save();

        // Clear the cache
        Cache::forget('return_request_configuration');

        flash(translate('Return request configuration has been updated successfully'))->success();
        return redirect()->route('return_request_configuration');
    }
}
