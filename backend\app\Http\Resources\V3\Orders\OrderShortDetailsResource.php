<?php

namespace App\Http\Resources\V3\Orders;

use App\Enums\OrderStatus;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderShortDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => (string) $this->code,
            'orderNumber' => (string)$this->code,
            'customer' => [
                'name' => (string) optional($this->user)->name ?? Null,
                'email' =>(string)  optional($this->user)->email ?? Null
            ],
            'total' => (float) $this->grand_total,
            'products' => $this->orderDetails->map(function ($detail) {
                return [
                    'productId' => (string) $detail->product->id,
                    'slug' => (string) $detail->product->slug,
                    'variation' => (string) $detail->variation,
                    'title' => $detail->product->name,
                    'thumbnail' => $detail->product->thumbnail_img ? uploaded_asset($detail->product->thumbnail_img) : "",
                    'quantity' => $detail->quantity,
                    'unit_price' => (string) $detail->unit_price,
                    'commission' => ''
                ];
            }),
            'commission' => '',
            'commissionStatus' => '',
            'orderStatus' => OrderStatus::getLabel($this->delivery_status),
            'paymentStatus' => $this->payment_status,
            'shippingMethod' =>  optional($this->carrier)->name ?? Null,
            'trackingNumber' => $this->carrier_tracking_code,
            'trackingUrl' => $this->tracking_link,
            'referralSource' => '',
            'placedAt' => $this->created_at->toIso8601String(),
            'updatedAt' => $this->updated_at->toIso8601String()
        ];
    }
}
