<?php

namespace App\Http\Controllers;

use App\Models\SellerPackage;
use App\Models\SellerPackagePayment;
use App\Models\Shop;
use App\Models\User;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class SellerPackageController extends Controller
{
    /**
     * Display a listing of seller packages (Admin)
     */
    public function index()
    {
        $seller_packages = SellerPackage::latest()->paginate(20);
        return view('backend.seller_packages.index', compact('seller_packages'));
    }

    /**
     * Show the form for creating a new seller package (Admin)
     */
    public function create()
    {
        return view('backend.seller_packages.create');
    }

    /**
     * Store a newly created seller package (Admin)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'product_upload_limit' => 'required|integer|min:0',
            'duration' => 'required|integer|min:1',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $seller_package = new SellerPackage;
        $seller_package->name = $request->name;
        $seller_package->amount = $request->amount;
        $seller_package->product_upload_limit = $request->product_upload_limit;
        $seller_package->duration = $request->duration;
        
        if ($request->hasFile('logo')) {
            $seller_package->logo = $request->file('logo')->store('uploads/seller_packages');
        }
        
        $seller_package->save();

        flash(translate('Seller package created successfully'))->success();
        return redirect()->route('seller_packages.index');
    }

    /**
     * Display the specified seller package (Admin)
     */
    public function show($id)
    {
        $seller_package = SellerPackage::findOrFail($id);
        return view('backend.seller_packages.show', compact('seller_package'));
    }

    /**
     * Show the form for editing the specified seller package (Admin)
     */
    public function edit($id)
    {
        $seller_package = SellerPackage::findOrFail($id);
        return view('backend.seller_packages.edit', compact('seller_package'));
    }

    /**
     * Update the specified seller package (Admin)
     */
    public function update(Request $request, $id)
    {
        $seller_package = SellerPackage::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'product_upload_limit' => 'required|integer|min:0',
            'duration' => 'required|integer|min:1',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $seller_package->name = $request->name;
        $seller_package->amount = $request->amount;
        $seller_package->product_upload_limit = $request->product_upload_limit;
        $seller_package->duration = $request->duration;
        
        if ($request->hasFile('logo')) {
            $seller_package->logo = $request->file('logo')->store('uploads/seller_packages');
        }
        
        $seller_package->save();

        flash(translate('Seller package updated successfully'))->success();
        return redirect()->route('seller_packages.index');
    }

    /**
     * Remove the specified seller package (Admin)
     */
    public function destroy($id)
    {
        $seller_package = SellerPackage::findOrFail($id);
        
        // Delete associated logo if exists
        if ($seller_package->logo && file_exists(storage_path('app/' . $seller_package->logo))) {
            unlink(storage_path('app/' . $seller_package->logo));
        }
        
        $seller_package->delete();

        flash(translate('Seller package deleted successfully'))->success();
        return redirect()->route('seller_packages.index');
    }

    /**
     * Display seller packages list for sellers (Frontend)
     */
    public function seller_packages_list()
    {
        if (!addon_is_activated('seller_subscription')) {
            flash(translate('Seller subscription is not activated'))->error();
            return redirect()->back();
        }

        $seller_packages = SellerPackage::all();
        $current_package = Auth::user()->shop->seller_package_id;
        
        return view('frontend.seller.seller_packages', compact('seller_packages', 'current_package'));
    }

    /**
     * Display seller package payment history (Frontend)
     */
    public function packages_payment_list()
    {
        $payments = SellerPackagePayment::where('user_id', Auth::id())
                                       ->with('seller_package')
                                       ->latest()
                                       ->paginate(20);
        
        return view('frontend.seller.package_payments', compact('payments'));
    }

    /**
     * Purchase a seller package (Frontend)
     */
    public function purchase_package(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'seller_package_id' => 'required|exists:seller_packages,id',
            'payment_option' => 'required|string'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $seller_package = SellerPackage::findOrFail($request->seller_package_id);
        $user = Auth::user();

        // Check if user has a shop
        if (!$user->shop) {
            flash(translate('You need to have a shop to purchase packages'))->error();
            return redirect()->back();
        }

        // Check product limit restriction for downgrade
        if ($user->shop->seller_package != null && 
            $seller_package->product_upload_limit < $user->shop->seller_package->product_upload_limit) {
            
            $current_products = Product::where('user_id', $user->id)->count();
            if ($current_products > $seller_package->product_upload_limit) {
                flash(translate('You have more uploaded products than this package limit. You need to remove excessive products to downgrade.'))->error();
                return redirect()->back();
            }
        }

        // Handle free package
        if ($seller_package->amount == 0) {
            seller_purchase_payment_done($user->id, $request->seller_package_id, 0, 'Free Package', null);
            flash(translate('Package purchased successfully'))->success();
            return redirect()->route('seller.seller_packages_list');
        }

        // Store payment data in session for payment processing
        $payment_data = [
            'user_id' => $user->id,
            'seller_package_id' => $request->seller_package_id,
            'amount' => $seller_package->amount,
            'payment_method' => $request->payment_option
        ];

        Session::put('payment_type', 'seller_package_payment');
        Session::put('payment_data', $payment_data);

        // Redirect to payment gateway based on payment option
        return $this->redirectToPaymentGateway($request->payment_option, $seller_package->amount);
    }

    /**
     * Purchase package offline (Frontend)
     */
    public function purchase_package_offline(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_id' => 'required|exists:seller_packages,id',
            'payment_option' => 'required|string',
            'trx_id' => 'required|string|max:255',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $seller_package = SellerPackage::findOrFail($request->package_id);
        $user = Auth::user();

        // Check product limit restriction
        if ($user->shop->seller_package != null && 
            $seller_package->product_upload_limit < $user->shop->seller_package->product_upload_limit) {
            
            $current_products = Product::where('user_id', $user->id)->count();
            if ($current_products > $seller_package->product_upload_limit) {
                flash(translate('You have more uploaded products than this package limit. You need to remove excessive products to downgrade.'))->error();
                return redirect()->back();
            }
        }

        // Store payment proof
        $photo_path = $request->file('photo')->store('uploads/offline_payments');

        // Create offline payment record
        $seller_package_payment = new SellerPackagePayment;
        $seller_package_payment->user_id = $user->id;
        $seller_package_payment->seller_package_id = $request->package_id;
        $seller_package_payment->payment_method = $request->payment_option;
        $seller_package_payment->payment_details = $request->trx_id;
        $seller_package_payment->approval = 0;
        $seller_package_payment->offline_payment = 1;
        $seller_package_payment->reciept = $photo_path;
        $seller_package_payment->save();

        flash(translate('Offline payment has been submitted. Please wait for approval.'))->success();
        return redirect()->route('seller.packages_payment_list');
    }

    /**
     * Handle payment completion from payment gateways
     */
    public function purchase_payment_done($payment_data, $payment_details)
    {
        try {
            if (is_string($payment_data)) {
                $payment_data = json_decode($payment_data, true);
            }

            $user_id = $payment_data['user_id'];
            $seller_package_id = $payment_data['seller_package_id'];
            $amount = $payment_data['amount'];
            $payment_method = $payment_data['payment_method'];

            // Process the payment using helper function
            seller_purchase_payment_done($user_id, $seller_package_id, $amount, $payment_method, $payment_details);

            // Clear session data
            Session::forget('payment_type');
            Session::forget('payment_data');

            flash(translate('Package purchased successfully'))->success();
            return redirect()->route('seller.seller_packages_list');

        } catch (\Exception $e) {
            flash(translate('Payment processing failed: ') . $e->getMessage())->error();
            return redirect()->route('seller.seller_packages_list');
        }
    }

    /**
     * Unpublish products for expired packages
     */
    public function unpublish_products()
    {
        $expired_shops = Shop::whereNotNull('seller_package_id')
                           ->where('package_invalid_at', '<', Carbon::today())
                           ->get();

        $unpublished_count = 0;

        foreach ($expired_shops as $shop) {
            // Unpublish products
            Product::where('user_id', $shop->user_id)
                  ->where('published', 1)
                  ->update(['published' => 0]);

            // Reset package
            $shop->seller_package_id = null;
            $shop->product_upload_limit = 0;
            $shop->save();

            $unpublished_count++;
        }

        return response()->json([
            'success' => true,
            'message' => "Unpublished products for {$unpublished_count} expired sellers"
        ]);
    }

    /**
     * Redirect to appropriate payment gateway
     */
    private function redirectToPaymentGateway($payment_method, $amount)
    {
        switch ($payment_method) {
            case 'stripe':
                return redirect()->route('stripe.payment', ['amount' => $amount, 'type' => 'seller_package']);
            case 'paypal':
                return redirect()->route('paypal.payment', ['amount' => $amount, 'type' => 'seller_package']);
            case 'razorpay':
                return redirect()->route('razorpay.payment', ['amount' => $amount, 'type' => 'seller_package']);
            default:
                flash(translate('Invalid payment method'))->error();
                return redirect()->back();
        }
    }

    /**
     * Get package statistics for admin dashboard
     */
    public function getStats()
    {
        $stats = [
            'total_packages' => SellerPackage::count(),
            'active_subscriptions' => Shop::whereNotNull('seller_package_id')->count(),
            'expired_subscriptions' => Shop::whereNotNull('seller_package_id')
                                         ->where('package_invalid_at', '<', Carbon::today())
                                         ->count(),
            'total_revenue' => SellerPackagePayment::where('approval', 1)->sum('amount') ?? 0
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
} 