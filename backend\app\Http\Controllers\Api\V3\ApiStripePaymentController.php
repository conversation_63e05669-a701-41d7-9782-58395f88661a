<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\OrderStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\StripePaymentMethod\PaymentMethodResource;
use App\Http\Resources\V3\StripePaymentMethod\PaymentMethodsResource;
use App\Http\Resources\V3\StripePaymentMethod\StripeCardResource;
use App\Models\CombinedOrder;
use App\Models\Order;
use App\Models\StripeCard;
use App\Services\ActivityLogService;
use App\Services\StripeService;
use App\Utility\NotificationUtility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;

class ApiStripePaymentController extends ApiResponse
{
    protected StripeService $stripeService;
    protected ActivityLogService $activityLogService;

    public function __construct(StripeService $stripeService, ActivityLogService $activityLogService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
        $this->activityLogService = $activityLogService;
    }

    public function payWithStripeCard(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string',
            'order_code' => 'required|string|exists:orders,code',
            'amount' => 'required|numeric|min:0.01',
            'payment_type' => 'required|in:cart_payment,wallet_payment,customer_package_payment,seller_package_payment',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('VALIDATION_ERROR', 'Please provide valid information', $validator->errors()->messages(), 400);
        }

        $user = auth()->user();
        $order = Order::where('code', $request->order_code)
            ->where('user_id', $user->id)
            ->first();

        if (!$order) {
            return $this->error('ORDER_NOT_FOUND', 'Order not found', null, 404);
        }

        if ($order->payment_status === 'paid') {
            return $this->error('PAYMENT_ALREADY_COMPLETED', 'Payment has already been completed for this order', null, 400);
        }

        $stripeCustomer = $user->stripeCustomer;
        if (!$stripeCustomer) {
            return $this->error('STRIPE_CUSTOMER_NOT_FOUND', 'The requested stripe customer could not be found', null, 404);
        }

        $successUrl = route('stripe2.success');
        $cancelUrl = route('stripe2.cancel');

        try {
            $paymentIntent = $this->stripeService->payWithStripeCardApi(
                $stripeCustomer->stripe_customer_id,
                $user->email,
                $request->payment_method,
                $request->order_code,
                $order->combined_order_id,
                $request->amount,
                $successUrl,
                $cancelUrl
            );

            if ($paymentIntent->status !== 'succeeded') {
                Log::channel('api_stripe_payment')->error(
                    'Payment failed',
                    [
                        'order_code' => $request->order_code,
                        'user_id' => $user->id,
                        'payment_intent_status' => $paymentIntent->status
                    ]
                );
                return $this->error('PAYMENT_FAILED', 'Payment was not successful', null, 400);
            }

            DB::beginTransaction();
            try {
                $combined_order = CombinedOrder::findOrFail($order->combined_order_id);
                $card_details = StripeCard::where('stripe_card_id', $request->payment_method)->first();
                $payment = ["status" => "Success",'payment_type' => $request->payment_type, 'card_details' => json_encode($card_details)];
                $chargeId = $paymentIntent->latest_charge ?? null;

                foreach ($combined_order->orders as $order) {
                    $order->update([
                        'payment_status' => 'paid',
                        'delivery_status' => 'order_placed',
                        'payment_details' => $payment,
                        'payment_charge_id' => $chargeId,
                        'pending_order_email' => null,
                        'cancel_pending_order_email' => null,
                        'pending_order_reminder_email' => null
                    ]);
                    $this->activityLogService->log(
                        'order_status_changed',
                        'Customer Make Payment',
                        $order->id,
                        Order::class,
                        auth()->user()->id,
                        get_class(auth()->user()),
                        OrderStatus::PENDING,
                        OrderStatus::ORDER_PLACED,
                        null,
                        email_end_time: null,
                    );
                    calculateCommissionAffilationClubPoint($order);
                   NotificationUtility::sendOrderPlacedNotification($order);
                }

                DB::commit();
                return $this->success(null, 'Payment successful', 200);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::channel('api_stripe_payment')->error('Error processing order after payment', [
                    'order_code' => $request->order_code,
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                return $this->error('ORDER_PROCESSING_ERROR', 'Error processing order after payment', null, 500);
            }

        } catch (CardException $e) {
            Log::channel('api_stripe_payment')->error('Card declined', [
                'order_code' => $request->order_code,
                'user_id' => $user->id,
                'error' => $e->getError()->message
            ]);
            return $this->error('CARD_DECLINED', $e->getError()->message, null, 400);

        } catch (InvalidRequestException $e) {
            Log::channel('api_stripe_payment')->error('Invalid request to Stripe API', [
                'order_code' => $request->order_code,
                'user_id' => $user->id,
                'error' => $e->getError()->message
            ]);
            return $this->error('INVALID_REQUEST', $e->getError()->message, null, 400);

        } catch (ApiErrorException $e) {
            Log::channel('api_stripe_payment')->error('Stripe API error', [
                'order_code' => $request->order_code,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return $this->error('STRIPE_API_ERROR', 'An error occurred while processing payment', null, 400);

        } catch (\Exception $e) {
            Log::channel('api_stripe_payment')->error('Unexpected error', [
                'order_code' => $request->order_code,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return $this->error('UNEXPECTED_ERROR', 'An unexpected error occurred', null, 500);
        }
    }

    /**
     * Process payment for an existing order
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function processOrderPayment(Request $request, string $orderId)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'paymentMethodId' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid payment information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $user = auth()->user();
            
            $order = null;
            $combined_order = null;
            
            // Try to find order by ID first (numeric ID)
            if (is_numeric($orderId)) {
                $order = Order::where('id', $orderId)
                            ->where('user_id', $user->id)
                            ->first();
            }
            
            // If not found by ID, try to find by order code
            if (!$order) {
                $order = Order::where('code', $orderId)
                            ->where('user_id', $user->id)
                            ->first();
            }
            
            // If still not found, try to find as combined order
            if (!$order) {
                $combined_order = CombinedOrder::where('id', $orderId)
                                              ->orWhere('combined_order_code', $orderId)
                                              ->first();
                
                if ($combined_order) {
                    // Get the first order from the combined order
                    $order = $combined_order->orders()
                                          ->where('user_id', $user->id)
                                          ->first();
                }
            }
            
            if (!$order) {
                return $this->error(
                    'ORDER_NOT_FOUND',
                    'Order not found',
                    null,
                    404
                );
            }
            
            // Get the combined order if we don't have it yet
            if (!$combined_order) {
                $combined_order = CombinedOrder::find($order->combined_order_id);
                if (!$combined_order) {
                    // Create missing combined order to fix data integrity issue
                    Log::channel('api_stripe_payment')->warning('Creating missing combined order', [
                        'order_id' => $order->id,
                        'order_code' => $order->code,
                        'combined_order_id' => $order->combined_order_id
                    ]);
                    
                    try {
                        // Get shipping address for combined order
                        $shippingAddress = null;
                        if ($order->shipping_address_id) {
                            $address = \App\Models\ShippingAddress::find($order->shipping_address_id);
                            if ($address) {
                                $shippingAddress = [
                                    'name' => $address->name,
                                    'email' => $address->email ?? $order->user->email,
                                    'address' => $address->address,
                                    'country' => $address->country,
                                    'state' => $address->state,
                                    'city' => $address->city,
                                    'postal_code' => $address->postal_code,
                                    'phone' => $address->phone
                                ];
                            }
                        }
                        
                        $combined_order = CombinedOrder::create([
                            'id' => $order->combined_order_id,
                            'combined_order_code' => $order->code,
                            'user_id' => $order->user_id,
                            'shipping_address' => json_encode($shippingAddress),
                            'grand_total' => $order->grand_total,
                            'created_at' => $order->created_at,
                            'updated_at' => $order->updated_at
                        ]);
                        
                        Log::channel('api_stripe_payment')->info('Combined order created successfully', [
                            'combined_order_id' => $combined_order->id,
                            'order_id' => $order->id
                        ]);
                        
                    } catch (\Exception $e) {
                        Log::channel('api_stripe_payment')->error('Failed to create combined order', [
                            'order_id' => $order->id,
                            'error' => $e->getMessage()
                        ]);
                        
                        return $this->error(
                            'COMBINED_ORDER_CREATE_FAILED',
                            'Failed to create combined order',
                            null,
                            500
                        );
                    }
                }
            }

            // Check if order can be paid
            if ($order->payment_status === 'paid') {
                return $this->error(
                    'INVALID_PAYMENT_STATUS',
                    'Order has already been paid',
                    null,
                    400
                );
            }

            // Check if user has Stripe customer ID
            $stripeCustomer = $user->stripeCustomer;
            if (!$stripeCustomer) {
                return $this->error(
                    'STRIPE_CUSTOMER_NOT_FOUND',
                    'The requested stripe customer could not be found',
                    null,
                    404
                );
            }

            // Create success and cancel URLs
            $successUrl = route('stripe2.success');
            $cancelUrl = route('stripe2.cancel');

            // Use StripeService to create and confirm payment intent
            try {
                $result = $this->stripeService->payWithStripeCardApi(
                    $stripeCustomer->stripe_customer_id,
                    $user->email,
                    $request->paymentMethodId,
                    $orderId,
                    $combined_order->id,
                    $combined_order->grand_total,
                    $successUrl,
                    $cancelUrl
                );

                // If 3D Secure authentication is required
                if (is_array($result) && isset($result['requires_action']) && $result['requires_action']) {
                    DB::commit(); // Commit the transaction as we'll need to handle the completion later
                    return $this->success(
                        [
                            'requires_action' => true,
                            'payment_intent_client_secret' => $result['payment_intent_client_secret'],
                            'payment_intent_id' => $result['payment_intent_id']
                        ],
                        'Additional authentication required',
                        200
                    );
                }

                // If it's a PaymentIntent object, it means payment succeeded
                $paymentIntent = is_array($result) ? null : $result;

            } catch (\Exception $e) {
                DB::rollBack();
                Log::channel('api_stripe_payment')->error('Stripe payment error', [
                    'order_id' => $orderId,
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }

            if (!$paymentIntent || $paymentIntent->status !== 'succeeded') {
                DB::rollBack();
                Log::channel('api_stripe_payment')->error('Payment failed', [
                    'order_id' => $orderId,
                    'user_id' => $user->id,
                    'payment_intent_status' => $paymentIntent ? $paymentIntent->status : 'unknown'
                ]);
                return $this->error(
                    'PAYMENT_FAILED',
                    'Payment was not successful',
                    null,
                    400
                );
            }

            // Process successful payment
            $card_details = StripeCard::where('stripe_card_id', $request->paymentMethodId)->first();
            $payment = [
                "status" => "Success",
                'payment_type' => 'card',
                'card_details' => json_encode($card_details)
            ];
            $chargeId = $paymentIntent->latest_charge ?? null;

            // Update all orders in the combined order
            foreach ($combined_order->orders as $order) {
                $order->update([
                    'payment_status' => 'paid',
                    'delivery_status' => 'order_placed',
                    'payment_details' => $payment,
                    'payment_charge_id' => $chargeId,
                    'pending_order_email' => null,
                    'cancel_pending_order_email' => null,
                    'pending_order_reminder_email' => null
                ]);

                // Log activity
                $this->activityLogService->log(
                    'order_status_changed',
                    'Customer Made Payment',
                    $order->id,
                    Order::class,
                    $user->id,
                    get_class($user),
                    OrderStatus::PENDING,
                    OrderStatus::ORDER_PLACED,
                    null,
                    email_end_time: null,
                );

                // Calculate commission and send notifications
                calculateCommissionAffilationClubPoint($order);
                NotificationUtility::sendOrderPlacedNotification($order);
            }

            DB::commit();
            return $this->success(
                [
                    'message' => 'Payment processed successfully',
                    'orderId' => $orderId,
                    'status' => OrderStatus::ORDER_PLACED,
                    'totalAmount' => $combined_order->grand_total,
                    'paymentStatus' => 'paid',
                ],
                'Payment processed successfully',
                200
            );

        } catch (CardException $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Card error', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->error(
                'CARD_ERROR',
                $e->getMessage(),
                null,
                400
            );
        } catch (InvalidRequestException $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Invalid request', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->error(
                'INVALID_REQUEST',
                'Invalid payment request',
                null,
                400
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Payment error', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error(
                'PAYMENT_ERROR',
                'An error occurred while processing payment',
                null,
                500
            );
        }
    }

    /**
     * Complete payment after 3D Secure authentication
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function completeOrderPayment(Request $request, string $orderId)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid payment information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $user = auth()->user();
            
            // First try to find the combined order
            $combined_order = CombinedOrder::where('combined_order_code', $orderId)
                                         ->first();

            if ($combined_order) {
                // Get the first order from the combined order
                $order = $combined_order->orders()
                                      ->where('user_id', $user->id)
                                      ->first();
                
                if (!$order) {
                    return $this->error(
                        'ORDER_NOT_FOUND',
                        'No orders found for this user in the combined order',
                        null,
                        404
                    );
                }
            } else {
                // If not found as combined order, try to find individual order
                $order = Order::where('code', $orderId)
                            ->where('user_id', $user->id)
                            ->first();

                if (!$order) {
                    return $this->error(
                        'ORDER_NOT_FOUND',
                        'Order not found',
                        null,
                        404
                    );
                }

                $combined_order = CombinedOrder::find($order->combined_order_id);
                if (!$combined_order) {
                    return $this->error(
                        'COMBINED_ORDER_NOT_FOUND',
                        'Combined order not found',
                        null,
                        404
                    );
                }
            }

            // Check if order can be paid
            if ($order->payment_status === 'paid') {
                return $this->error(
                    'INVALID_PAYMENT_STATUS',
                    'Order has already been paid',
                    null,
                    400
                );
            }

            // Retrieve and check payment intent status
            try {
                $paymentIntent = $this->stripeService->retrievePaymentIntent($request->payment_intent_id);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::channel('api_stripe_payment')->error('Error retrieving payment intent', [
                    'order_id' => $orderId,
                    'user_id' => $user->id,
                    'payment_intent_id' => $request->payment_intent_id,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }

            if ($paymentIntent->status !== 'succeeded') {
                DB::rollBack();
                Log::channel('api_stripe_payment')->error('Payment not successful', [
                    'order_id' => $orderId,
                    'user_id' => $user->id,
                    'payment_intent_id' => $request->payment_intent_id,
                    'status' => $paymentIntent->status
                ]);
                return $this->error(
                    'PAYMENT_FAILED',
                    'Payment was not successful',
                    null,
                    400
                );
            }

            // Process successful payment
            $card_details = StripeCard::where('stripe_card_id', $paymentIntent->payment_method)->first();
            $payment = [
                "status" => "Success",
                'payment_type' => 'card',
                'card_details' => json_encode($card_details)
            ];
            $chargeId = $paymentIntent->latest_charge ?? null;

            // Update all orders in the combined order
            foreach ($combined_order->orders as $order) {
                $order->update([
                    'payment_status' => 'paid',
                    'delivery_status' => 'order_placed',
                    'payment_details' => $payment,
                    'payment_charge_id' => $chargeId,
                    'pending_order_email' => null,
                    'cancel_pending_order_email' => null,
                    'pending_order_reminder_email' => null
                ]);

                // Log activity
                $this->activityLogService->log(
                    'order_status_changed',
                    'Customer Made Payment',
                    $order->id,
                    Order::class,
                    $user->id,
                    get_class($user),
                    OrderStatus::PENDING,
                    OrderStatus::ORDER_PLACED,
                    null,
                    email_end_time: null,
                );

                // Calculate commission and send notifications
                calculateCommissionAffilationClubPoint($order);
                NotificationUtility::sendOrderPlacedNotification($order);
            }

            DB::commit();
            return $this->success(
                [
                    'message' => 'Payment processed successfully',
                    'orderId' => $orderId,
                    'status' => OrderStatus::ORDER_PLACED,
                    'totalAmount' => $combined_order->grand_total,
                    'paymentStatus' => 'paid',
                ],
                'Payment processed successfully',
                200
            );

        } catch (CardException $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Card error', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->error(
                'CARD_ERROR',
                $e->getMessage(),
                null,
                400
            );
        } catch (InvalidRequestException $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Invalid request', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);
            return $this->error(
                'INVALID_REQUEST',
                'Invalid payment request',
                null,
                400
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api_stripe_payment')->error('Payment error', [
                'order_id' => $orderId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error(
                'PAYMENT_ERROR',
                'An error occurred while processing payment',
                null,
                500
            );
        }
    }
} 