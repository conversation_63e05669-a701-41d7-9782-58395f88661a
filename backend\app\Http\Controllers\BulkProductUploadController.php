<?php

namespace App\Http\Controllers;

use PDF;
use Auth;
use App\Models\User;
use App\Models\Brand;
use App\Models\Product;
use App\Models\Category;
use App\Models\Supplier;
use App\Models\ImportStatus;
use App\Models\VariantsExport;
use App\Models\ProductsExport;
use App\Models\ProductsImport;
use App\Models\BulkProductsUpdate;
use App\Models\ProductWithoutVariantsImport;
use App\Models\VariantsWithoutProductImport;
use App\Models\ProductVariantsExport2;
use App\Models\ProductsWithoutVariantsExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\ProductService;
use App\Services\ProductTaxService;
use App\Services\ProductStockService;
use App\Services\ProductsBulkUploadImportService;
use App\Jobs\ProcessProductBulkUpload;
use App\Imports\ProductBulkImport;
use App\Imports\VariantBulkImport;
use App\Imports\RowsCountImport;
use App\Exports\ProductExport;
use App\Exports\FiltersProductsExport;
//use Artisan;

class BulkProductUploadController extends Controller
{
    public function __construct(
        ProductService $productService,
        ProductTaxService $productTaxService,
        ProductStockService $productStockService
    ) {
        $this->productService = $productService;
        $this->productTaxService = $productTaxService;
        $this->productStockService = $productStockService;

        $this->middleware(['permission:product_bulk_import'])->only('index');
        $this->middleware(['permission:product_bulk_export'])->only('export');
    }

    public function index()
    {
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.product_bulk_upload.index');
            }
            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_product_upload.index');
        }
    }

    public function bulk_product_upload(Request $request)
    {
        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'insert',
                    'type' => 'product',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessProductBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Products imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing products: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing products: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }

        return redirect()->back();
    }

    public function export()
    {
        //dd('dd');
        return Excel::download(new ProductExport, 'products.xlsx');
    }
    
    
    public function bulk_product_export()
    {
        $categories = Category::all();
        $brands = Brand::all();
        $suppliers = Supplier::all();
        $users = User::all();
        return view('backend.product.bulk_product_upload.product_bulk_export', compact('categories', 'brands', 'suppliers','users'));
    }
    public function product_bulk_export(Request $request)
    {
        $products = $this->filterProducts($request);
        $products = $products->orderBy('created_at', 'desc')->get();
        return Excel::download(new FiltersProductsExport($products), 'products.xlsx');
    }
    public function filter_products(Request $request) {
        $products = $this->filterProducts($request);
        // Fetch the filtered products
        $products = $products->orderBy('created_at', 'desc')->paginate(15);

        // Fetch all categories, brands, suppliers, and users for filters
        $categories = Category::all();
        $brands = Brand::all();
        $suppliers = Supplier::all();
        $users = User::all();

        return view('backend.product.bulk_product_upload.product_bulk_export', compact('products', 'categories', 'brands', 'suppliers', 'users'));
    }

    private function filterProducts(Request $request)
    {
        // Initialize query
        $query = Product::with([
            'product_translations',
            'taxes',
            'thumbnail',
            'category',
            'brand',
            'user',
            'stocks',
        ]);

        // Apply filters
        if ($request->filled('sku')) {
            $query->whereHas('stocks', function($q) use ($request) {
                $q->where('sku', $request->sku);
            });
        }

        if ($request->filled('upc')) {
            $query->whereHas('stocks', function($q) use ($request) {
                $q->where('upc', $request->upc);
            });
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->brand_id);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('date_range')) {
            $dates = explode(' to ', $request->date_range);
            $query->where(function ($query) use ($dates) {
                $query->whereBetween('discount_start_date', [strtotime($dates[0]), strtotime($dates[1])])
                      ->orWhereBetween('discount_end_date', [strtotime($dates[0]), strtotime($dates[1])]);
            });
        }

        if ($request->filled('create_date_range')) {
            $dates = explode(' to ', $request->create_date_range);
            $query->whereBetween('created_at', [date('Y-m-d H:i:s', strtotime($dates[0])), date('Y-m-d H:i:s', strtotime($dates[1]))]);
        }

        if ($request->filled('published')) {
            $query->where('published', $request->published);
        }

        if ($request->filled('stock_range_low') || $request->filled('stock_range_high')) {
            $low = $request->stock_range_low ?? 0;
            $high = $request->stock_range_high ?? 10000;
            $query->whereHas('stocks', function($q) use ($low, $high) {
                $q->whereBetween('qty', [$low, $high]);
            });
        }

        if ($request->filled('variant')) {
            $query->where('variant_product', $request->variant);
        }

        if ($request->filled('todays_deal')) {
            $query->where('todays_deal', $request->todays_deal);
        }

        return $query;
    }
    
    

    public function pdf_download_products()
    {
        $products = Product::all();
        return PDF::loadView('backend.downloads.products', [
            'products' => $products,
        ], [], [])->download('products.pdf');
    }
    public function excel_download_products()
    {
        return Excel::download(new ProductExport, 'products.xlsx');
    }

    //bulk edit
    public function bulkEdit(){
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.product_bulk_upload.index');
            }

            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_product_upload.product_bulk_edit');
        }
    }

    //bulk-update
    public function bulkProductUpdate(Request $request)
    {

        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'update',
                    'type' => 'product',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessProductBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Products imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing products: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing products: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }

        return back();
    }

    public function bulk_product_delete(){
        if (Auth::user()->user_type == 'seller') {
            if(Auth::user()->shop->verification_status){
                return view('seller.product_bulk_upload.index');
            }

            else{
                flash(translate('Your shop is not verified yet!'))->warning();
                return back();
            }
        }
        elseif (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
            return view('backend.product.bulk_product_upload.product_bulk_delete');
        }
    }

    public function bulk_product_destroy(Request $request)
    {

        if ($request->hasFile('bulk_file')) {
            try {
                $file = $request->file('bulk_file');
                $fileExtension = $file->getClientOriginalExtension();
                if (!in_array($fileExtension, ['xlsx', 'xls', 'csv'])) {
                    throw new \Exception('Invalid file type.');
                }

                $import = new RowsCountImport;
                Excel::import($import, $file);

                $totalRows = $import->rowCount - 1;
                
                // Store the file
                $filePath = $file->store('uploads');

                // Create an import status record
                $importStatus = ImportStatus::create([
                    'user_id' => auth()->user()->id,
                    'file_path' => $filePath,
                    'total' => $totalRows,    
                    'success' => 0,
                    'errors' => 0,
                    'pending' => $totalRows,
                    'progress' => 0,
                    'status' => 'pending',
                    'purpose' => 'delete',
                    'type' => 'product',
                    'reject_item' => [], // Initialize with an empty array
                    'error_messages' => [], // Initialize with an empty array
                ]);

                // Dispatch the job
                ProcessProductBulkUpload::dispatch($importStatus, auth()->user()->id, $filePath);

                // Start the queue worker in the background
                $this->startQueueWorker();

                // Set success flash message
                flash('Products imported successfully. Import process started. You will be notified once it is completed.')->success();
            } catch (\Maatwebsite\Excel\Validators\ValidationException $ve) {
                // Handle validation exceptions
                flash('Error importing products: ' . $ve->failures())->error();
            } catch (\Exception $e) {
                // Handle other exceptions
                flash('Error importing products: ' . $e->getMessage())->error();
            }
        } else {
            // Handle the case where no file is provided
            flash('No file was uploaded. Please try again.')->warning();
        }
        return back();
    }

    public function getbulkUploadStatus(Request $request)
    {
        $userId = $request->user()->id;
        $importStatus = ImportStatus::where('user_id', $userId)->orderBy('created_at','desc')->first();

          if ($importStatus) {
            $total = $importStatus->total;
            $successPercent = number_format(($importStatus->success / $total) * 100, 2);
            $errorPercent = number_format(($importStatus->errors / $total) * 100, 2);
            $pendingPercent = number_format(($importStatus->pending / $total) * 100, 2);

            return response()->json([
                'progress' => $importStatus->progress,
                'success' => $importStatus->success,
                'errors' => $importStatus->errors,
                'pending' => $importStatus->pending,
                'status' => $importStatus->status,
                'success_percent' => $successPercent,
                'error_percent' => $errorPercent,
                'pending_percent' => $pendingPercent,
                'rejectItems' => $importStatus->reject_item,
                'errorMessages' => $importStatus->error_messages,

            ]);
        }
        return response()->json(['status' => 'no_import_found'], 404);
    }
    
    private function startQueueWorker()
    {
        $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
        
        //$command = "php /home/<USER>/public_html/buzfi.com/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &";
        
        //dd( $command);
        exec($command);
       /* 
        Artisan::call('queue:work', [
            '--verbose' => true,
            '--tries' => 1,
            '--timeout' => 220,
        ]);
        */
        
    }
}