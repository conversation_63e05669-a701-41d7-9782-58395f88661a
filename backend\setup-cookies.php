<?php

/**
 * Cookie Configuration Setup Script
 * Run this script to validate and configure cookie settings
 */

echo "🍪 Cookie Configuration Setup\n";
echo "=============================\n\n";

// Check if running in CLI
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line.\n");
}

// Load Laravel environment
require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Configuration checks
$checks = [
    'Environment' => [
        'APP_ENV' => env('APP_ENV', 'local'),
        'APP_URL' => env('APP_URL', 'http://localhost'),
        'APP_DEBUG' => env('APP_DEBUG', true) ? 'true' : 'false',
    ],
    'Session Configuration' => [
        'SESSION_DRIVER' => config('session.driver'),
        'SESSION_LIFETIME' => config('session.lifetime'),
        'SESSION_SECURE_COOKIE' => config('session.secure') ? 'true' : 'false',
        'SESSION_SAME_SITE' => config('session.same_site'),
        'SESSION_DOMAIN' => config('session.domain') ?: 'null',
    ],
    'Stripe Configuration' => [
        'STRIPE_KEY' => env('STRIPE_KEY') ? 'Set' : 'Not Set',
        'STRIPE_SECRET' => env('STRIPE_SECRET') ? 'Set' : 'Not Set',
    ],
    'Security Headers' => [
        'CORS_SUPPORTS_CREDENTIALS' => config('cors.supports_credentials') ? 'true' : 'false',
        'CORS_ALLOWED_ORIGINS' => implode(', ', config('cors.allowed_origins')),
    ]
];

// Display current configuration
foreach ($checks as $category => $items) {
    echo "📋 {$category}\n";
    echo str_repeat('-', strlen($category) + 4) . "\n";
    
    foreach ($items as $key => $value) {
        echo sprintf("   %-25s: %s\n", $key, $value);
    }
    echo "\n";
}

// Environment-specific recommendations
echo "💡 Recommendations\n";
echo "==================\n";

$env = env('APP_ENV', 'local');
$isHttps = str_starts_with(env('APP_URL', ''), 'https://');

if ($env === 'production') {
    echo "✅ Production Environment Detected\n";
    
    if (!$isHttps) {
        echo "⚠️  WARNING: HTTPS is not configured. Secure cookies require HTTPS.\n";
    }
    
    if (config('session.same_site') !== 'none') {
        echo "⚠️  Consider setting SESSION_SAME_SITE=none for cross-origin requests.\n";
    }
    
    if (!config('session.secure') && $isHttps) {
        echo "⚠️  Consider enabling SESSION_SECURE_COOKIE=true for HTTPS.\n";
    }
    
} else {
    echo "🔧 Development Environment Detected\n";
    
    if (config('session.secure')) {
        echo "⚠️  SESSION_SECURE_COOKIE is enabled. This may cause issues in HTTP development.\n";
    }
    
    if (config('session.same_site') === 'none') {
        echo "⚠️  SESSION_SAME_SITE=none in development. Consider using 'lax' for local development.\n";
    }
}

// Stripe-specific checks
echo "\n🔒 Stripe Configuration\n";
echo "=======================\n";

if (!env('STRIPE_KEY')) {
    echo "⚠️  STRIPE_KEY is not set. Stripe integration will not work.\n";
} else {
    $key = env('STRIPE_KEY');
    if (str_starts_with($key, 'pk_test_')) {
        echo "🧪 Using Stripe TEST keys\n";
    } elseif (str_starts_with($key, 'pk_live_')) {
        echo "🚀 Using Stripe LIVE keys\n";
    } else {
        echo "⚠️  Invalid Stripe key format\n";
    }
}

// Generate recommended .env settings
echo "\n📝 Recommended .env Settings\n";
echo "============================\n";

if ($env === 'production') {
    echo "# Production Settings\n";
    echo "SESSION_SECURE_COOKIE=true\n";
    echo "SESSION_SAME_SITE=none\n";
    echo "SESSION_DOMAIN=yourdomain.com\n";
} else {
    echo "# Development Settings\n";
    echo "SESSION_SECURE_COOKIE=false\n";
    echo "SESSION_SAME_SITE=lax\n";
    echo "SESSION_DOMAIN=\n";
}

echo "SESSION_LIFETIME=120\n";
echo "\n# Stripe Settings\n";
echo "STRIPE_KEY=your_stripe_publishable_key\n";
echo "STRIPE_SECRET=your_stripe_secret_key\n";
echo "STRIPE_WEBHOOK_SECRET=your_webhook_secret\n";

echo "\n✅ Setup Complete!\n";
echo "Run 'php artisan config:cache' to apply changes.\n"; 