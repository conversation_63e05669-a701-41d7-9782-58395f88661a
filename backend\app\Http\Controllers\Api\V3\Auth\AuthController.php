<?php

namespace App\Http\Controllers\Api\V3\Auth;
use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\UserResource;
use App\Models\User;
use App\Models\EmailVerify;
use App\Notifications\RegistrationOTPNotification;
use App\Services\EmailVerificationMonitoringService;
use App\Services\EmailVerificationPerformanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class AuthController extends ApiResponse
{
    protected $monitoringService;
    protected $performanceService;

    public function __construct(
        EmailVerificationMonitoringService $monitoringService,
        EmailVerificationPerformanceService $performanceService
    ) {
        $this->monitoringService = $monitoringService;
        $this->performanceService = $performanceService;
    }


    /**
     * Handle user login request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // Validate request data
        $validator = $this->validateLoginRequest($request);
        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                401
            );
        }

        // Find user by email or phone
        $user = $this->findUserByCredentials($request);
        if (!$user) {
            return $this->error(
                'Unauthorized',
                'User not found',
                401
            );
        }

        // Check if user is banned
        if ($user->banned) {
            return $this->error(
                'Banned User',
                'Your account is banned',
                403);
        }

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return $this->error(
                'Unauthorized',
                'Invalid credentials',
                401
            );
        }

        // Generate authentication data with cookie
        return $this->generateAuthResponse($user);
    }

    /**
     * Validate login request data
     *
     * @param Request $request
     * @return \Illuminate\Contracts\Validation\Validator
     */
    private function validateLoginRequest(Request $request)
    {
        return Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);
    }

    /**
     * Find user by email or phone number
     *
     * @param Request $request
     * @return User|null
     */
    private function findUserByCredentials(Request $request)
    {
        $userType = $this->determineUserType($request);
//whereIn('user_type', [$userType])
//            ->
        return User::where(function ($query) use ($request) {
                $query->where('email', $request->email) ;          })
            ->first();
    }

    /**
     * Determine user type based on request
     *
     * @param Request $request
     * @return string
     */
    private function determineUserType(Request $request)
    {
        if ($request->has('user_type')) {
            switch ($request->user_type) {
                case 'delivery_boy':
                    return 'delivery_boy';
                case 'seller':
                    return 'seller';
            }
        }
        return 'customer';
    }

    /**
     * Generate authentication data for successful login
     *
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    protected function generateAuthResponse(User $user)
    {
        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;
        
        // Create JWT cookie
        $cookie = cookie('jwt', $token, 60 * 24 * 30, null, null, false, true); // 30 days
        
        return $this->success([
            'user' => new UserResource($user),
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => 60 * 24 * 30 * 60 // 30 days in seconds
        ], 'Login successful')->withCookie($cookie);
    }

    public function user(Request $request)
    {
        try {
            $user = $request->user();
            
            if (!$user) {
                return $this->error(
                    'Unauthenticated',
                    'No authenticated user found',
                    401
                );
            }
            
            return $this->success(new UserResource($user));
        } catch (\Exception $e) {
            return $this->error(
                'Server Error',
                'Failed to get user data: ' . $e->getMessage(),
                500
            );
        }
    }

    public function logout(Request $request)
    {
        // Delete the current access token
        Auth::user()->currentAccessToken()->delete();
        
        // Clear the JWT cookie
        $cookie = cookie()->forget('jwt');
        
        return response()->json([
            'success' => true,
            'status' => 'success',
            'message' => 'Successfully logged out'
        ])->withCookie($cookie);
    }

    /**
     * Refresh the authentication token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return $this->error(
                'Unauthorized',
                'User not authenticated',
                401
            );
        }
        
        // Delete the current token
        $user->currentAccessToken()->delete();
        
        // Generate new authentication data
        return $this->generateAuthResponse($user);
    }

    public function socialLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'provider' => 'required|string|in:google,facebook,twitter,gmail',
            'token' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                422
            );
        }
        $provider = $request->provider;
        $token = $request->token;
        DB::beginTransaction();
        try {
            $socialUser = Socialite::driver($provider)->userFromToken($token);
            $user = User::where('email', $socialUser->getEmail())->first();
            if (!$user) {
                // Create a new user
                $user = User::create([
                    'name' => $socialUser->getName() ?? 'Unknown User',
                    'email' => $socialUser->getEmail(),
                    'user_type' => 'customer',
                    'password' => bcrypt(Str::random(16)), // Random password
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                ]);
            }
            
            return $this->generateAuthResponse($user);
        }catch (\Exception $e) {
            Log::channel('api_auth')->error('Error in user social login : AuthController ' . print_r($e->getMessage(),true));
                DB::rollBack();
                return $this->error(400,
                    'Social login failed.',
                    'Social login .Please try again later.'
                );
            }
        }

    /**
     * Perform dummy OTP generation to maintain consistent timing
     */
    private function performDummyOTPGeneration()
    {
        // Simulate OTP generation time to prevent timing attacks
        usleep(rand(100000, 300000)); // 0.1-0.3 seconds
    }

    /**
     * Get appropriate message for existing user based on user types
     */
    private function getExistingUserMessage($existingUserType, $requestedUserType)
    {
        if ($existingUserType === $requestedUserType) {
            return "An account with this email already exists. Please login instead.";
        }
        
        return "An account with this email already exists as a {$existingUserType}. Please use a different email or login to your existing account.";
    }

    /**
     * Ensure consistent response timing to prevent timing attacks
     */
    private function ensureConsistentTiming($startTime, $targetTimeMs = 500)
    {
        $elapsedTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
        $remainingTime = max(0, $targetTimeMs - $elapsedTime);
        
        if ($remainingTime > 0) {
            usleep($remainingTime * 1000); // Convert to microseconds
        }
    }

}
