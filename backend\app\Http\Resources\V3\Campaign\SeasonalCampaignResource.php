<?php

namespace App\Http\Resources\V3\Campaign;

use App\Http\Resources\V3\Offer\PersonalizedOfferResource;
use Illuminate\Http\Resources\Json\JsonResource;

class SeasonalCampaignResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'start_date' => $this->start_date->toIso8601String(),
            'end_date' => $this->end_date->toIso8601String(),
            'banner_image' => $this->banner_image,
            'cta_text' => $this->cta_text,
            'cta_link' => $this->cta_link,
            'is_active' => (bool) $this->is_active,
            'terms_conditions' => $this->terms_conditions,
            'related_offers' => PersonalizedOfferResource::collection($this->whenLoaded('relatedOffers')),
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
} 