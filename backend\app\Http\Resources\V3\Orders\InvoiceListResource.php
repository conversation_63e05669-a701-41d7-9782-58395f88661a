<?php

namespace App\Http\Resources\V3\Orders;

use Illuminate\Http\Resources\Json\JsonResource;

class InvoiceListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // We'll use the order data directly

        // Calculate due date (15 days from issue date)
        $dateIssued = $this->date ? date('Y-m-d\TH:i:s\Z', $this->date) : null;
        //$dueDate = $this->date ? date('Y-m-d\TH:i:s\Z', strtotime('+15 days', $this->date)) : null;
        $dueDate = $this->date ? date('Y-m-d\TH:i:s\Z', $this->date) : null;

        // Determine payment date
        $datePaid = null;
        if ($this->payment_status === 'paid') {
            $datePaid = $dateIssued;
        }

        // Determine invoice status
        $status = $this->payment_status;

        // Format the invoice data
        return [
            'id' => 'inv-' . $this->code,
            'invoiceNumber' => 'INV-'   . $this->id,
            'orderId' => $this->code,
            'customerId' => optional($this->user)->name,
            'dateIssued' => $dateIssued,
            'datePaid' => $datePaid,
            'dueDate' => $dueDate,
            'status' => $status,
            'total' => (float) $this->grand_total, // Convert to cents
            'subtotal' => (float) $this->orderDetails->sum('price'),
            'tax' => (float) $this->orderDetails->sum('tax'),
            'shipping' => (float) $this->orderDetails->sum('shipping_cost') ,
            'discount' => (float) $this->coupon_discount ,
            'paymentMethod' => $this->getReadablePaymentMethodDetails($this->payment_details,$this->payment_status),
            'viewed' => $this->invoice_view_count > 0,
            'downloadCount' => $this->invoice_download_count,
            'lastDownloaded' => $this->invoice_last_downloaded ? (is_string($this->invoice_last_downloaded) ? $this->invoice_last_downloaded : $this->invoice_last_downloaded->toIso8601String()) : null
        ];
    }
}
