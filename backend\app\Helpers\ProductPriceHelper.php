<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

class ProductPriceHelper
{
    /**
     * Get display price and regular price for a product
     * 
     * @param mixed $product The product object
     * @return array Array containing displayPrice and regularPrice
     */
    public static function getProductPrices($product)
    {
        // Get user type
        $user = Auth::user('sanctum');
        $userType = $user ? $user->user_type : null;

        // Get regular price (unit price)
        $regularPrice = is_numeric($product->unit_price) ? (float)$product->unit_price : 0;

        // Calculate display price based on user type and discounts
        if ($userType === 'b2b') {
            // For B2B users, use b2b_price if valid, otherwise fallback to regular price
            $displayPrice = is_numeric($product->b2b_price) && $product->b2b_price > 0 ? (float)$product->b2b_price : $regularPrice;
        } else {
            // For customers or no login
            if ($product->discount && is_numeric($product->discount) && $product->discount > 0) {
                // If product has valid discount
                if ($product->discount_type === 'percent' && $product->discount <= 100) {
                    $displayPrice = $regularPrice - ($regularPrice * ($product->discount / 100));
                } elseif ($product->discount_type === 'amount' && $product->discount <= $regularPrice) {
                    $displayPrice = $regularPrice - $product->discount;
                } else {
                    $displayPrice = $regularPrice;
                }
            } else {
                // No discount
                $displayPrice = $regularPrice;
            }
        }

        // Ensure prices are not negative
        $displayPrice = max(0, $displayPrice);
        $regularPrice = max(0, $regularPrice);

        return [
            'displayPrice' => round($displayPrice, 2),
            'regularPrice' => round($regularPrice, 2)
        ];
    }
} 