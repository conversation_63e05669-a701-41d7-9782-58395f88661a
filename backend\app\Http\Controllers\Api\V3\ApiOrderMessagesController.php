<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderMessage;
use App\Models\Attachment;
use App\Services\ActivityLogService;
use App\Services\ApiOrderService;
use App\Services\FileUploadService;
use App\Utility\NotificationUtility;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ApiOrderMessagesController extends ApiResponse
{
    protected ApiOrderService $apiOrderService;
    protected ActivityLogService $activityLogService;
    protected FileUploadService $fileUploadService;

    public function __construct(
        ApiOrderService $apiOrderService,
        ActivityLogService $activityLogService,
        FileUploadService $fileUploadService
    ) {
        parent::__construct();
        $this->apiOrderService = $apiOrderService;
        $this->activityLogService = $activityLogService;
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Get messages for a specific order
     *
     * @param Request $request
     * @param string $orderId The order code/ID
     * @return JsonResponse
     */
    public function getMessages(Request $request, string $orderId): JsonResponse
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();
            
            // Check permissions based on user type
            if (!$this->canAccessOrder($order)) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access messages for this order',
                    '',
                    403
                );
            }

            $messages = OrderMessage::where('order_id', $order->id)
                ->with(['sender', 'attachments'])
                ->orderBy('created_at', 'desc')
                ->get();

            return $this->success(
                $messages,
                'Order messages retrieved successfully'
            );
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Order not found in getMessages: ' . $e->getMessage());
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in getMessages: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving messages',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Send a new message for an order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'orderId' => 'required|string|exists:orders,code',
            'content' => 'required|string|max:5000',
            'attachmentIds' => 'nullable|array',
            'attachmentIds.*' => 'exists:attachments,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();

            $order = Order::where('code', $request->orderId)->firstOrFail();
            
            // Check permissions based on user type
            if (!$this->canAccessOrder($order)) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to send messages for this order',
                    '',
                    403
                );
            }

            // Determine sender type based on user
            $user = Auth::user();
            $senderType = $this->determineSenderType();

            $message = new OrderMessage();
            $message->order_id = $order->id;
            $message->sender_id = $user->id;
            $message->sender_type = $senderType;
            $message->content = $request->content;
            $message->save();

            // Handle attachments if any
            if ($request->has('attachmentIds') && is_array($request->attachmentIds)) {
                $message->attachments()->attach($request->attachmentIds);
            }

            // Send notification to other parties
            $this->sendMessageNotification($order, $message);
            
            // Log activity
            $this->activityLogService->log(
                'order_message',
                'Message sent',
                $message->id,
                OrderMessage::class,
                $user->id,
                get_class($user),
                $order->code,
                null,
                'Message sent by ' . $senderType,
                null
            );

            DB::commit();

            return $this->success(
                ['messageId' => $message->id],
                'Message sent successfully'
            );
        } catch (ModelNotFoundException $e) {
            DB::rollBack();
            Log::channel('api_order')->error('Order not found in sendMessage: ' . $e->getMessage());
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api_order')->error('Error in sendMessage: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while sending message',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Mark messages as read for an order
     *
     * @param Request $request
     * @param string $orderId The order code/ID
     * @return JsonResponse
     */
    public function markAsRead(Request $request, string $orderId): JsonResponse
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();
            
            // Check permissions based on user type
            if (!$this->canAccessOrder($order)) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access messages for this order',
                    '',
                    403
                );
            }

            $user = Auth::user();
            $userType = $this->determineSenderType();
            
            // For customer, mark all seller and admin messages as read
            // For seller, mark all customer and admin messages as read
            // For dropshipper, mark all customer and admin messages as read
            
            $messagesToMark = OrderMessage::where('order_id', $order->id)
                ->whereNull('read_at')
                ->where('sender_type', '!=', $userType);
            
            $count = $messagesToMark->count();
            
            $messagesToMark->update([
                'read_at' => now()
            ]);

            // Log activity
            $this->activityLogService->log(
                'order_message',
                'Messages marked as read',
                $order->id,
                Order::class,
                $user->id,
                get_class($user),
                $order->code,
                null,
                'Marked ' . $count . ' messages as read',
                null
            );

            return $this->success(
                ['success' => true, 'count' => $count],
                'Messages marked as read successfully'
            );
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Order not found in markAsRead: ' . $e->getMessage());
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in markAsRead: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while marking messages as read',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get unread message count for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUnreadCount(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $userType = $this->determineSenderType();
            
            // For customers: count messages from sellers/admin in their orders
            // For sellers: count messages from customers/admin in orders they fulfill
            // For dropshippers: count messages from customers/admin in their orders
            
            $query = OrderMessage::whereNull('read_at')
                ->where('sender_type', '!=', $userType)
                ->whereHas('order', function ($query) use ($user, $userType) {
                    if ($userType === 'customer') {
                        $query->where('user_id', $user->id);
                    } elseif ($userType === 'seller') {
                        $query->where('seller_id', $user->id);
                    } elseif ($userType === 'dropshipper') {
                        $query->where('dropshipper_id', $user->id);
                    }
                });
            
            $count = $query->count();

            return $this->success(
                ['count' => $count],
                'Unread message count retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in getUnreadCount: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while getting unread message count',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Determine if the authenticated user can access this order
     *
     * @param Order $order
     * @return bool
     */
    private function canAccessOrder(Order $order): bool
    {
        $user = Auth::user();
        $userType = $this->getUserType();
        
        if ($userType === 'admin') {
            return true;
        } elseif ($userType === 'customer') {
            return $order->user_id === $user->id;
        } elseif ($userType === 'seller') {
            return $order->seller_id === $user->id;
        } elseif ($userType === 'dropshipper') {
            return $order->dropshipper_id === $user->id;
        }
        
        return false;
    }

    /**
     * Determine the sender type based on authenticated user
     *
     * @return string
     */
    private function determineSenderType(): string
    {
        $userType = $this->getUserType();
        
        if ($userType === 'admin') {
            return 'admin';
        } elseif ($userType === 'customer') {
            return 'customer';
        } elseif ($userType === 'seller') {
            return 'seller';
        } elseif ($userType === 'dropshipper') {
            return 'dropshipper';
        }
        
        return 'system';
    }

    /**
     * Get the user type of authenticated user
     *
     * @return string
     */
    private function getUserType(): string
    {
        $user = Auth::user();
        
        if ($user->user_type === 'admin') {
            return 'admin';
        } elseif ($user->user_type === 'seller') {
            return 'seller';
        } elseif ($user->user_type === 'customer') {
            return 'customer';
        } elseif ($user->user_type === 'dropshipper') {
            return 'dropshipper';
        }
        
        return 'customer'; // Default
    }

    /**
     * Send notification to relevant parties about new message
     *
     * @param Order $order
     * @param OrderMessage $message
     * @return void
     */
    private function sendMessageNotification(Order $order, OrderMessage $message): void
    {
        $senderType = $message->sender_type;
        
        if ($senderType === 'customer') {
            // Notify seller and/or dropshipper
            if ($order->seller_id) {
                NotificationUtility::sendNotification(
                    'New message from customer', 
                    $message->content, 
                    $order->seller_id, 
                    'order_message',
                    ['order_id' => $order->id, 'message_id' => $message->id]
                );
            }
            
            if ($order->dropshipper_id) {
                NotificationUtility::sendNotification(
                    'New message from customer', 
                    $message->content, 
                    $order->dropshipper_id, 
                    'order_message',
                    ['order_id' => $order->id, 'message_id' => $message->id]
                );
            }
        } elseif ($senderType === 'seller' || $senderType === 'admin') {
            // Notify customer
            NotificationUtility::sendNotification(
                'New message about your order', 
                $message->content, 
                $order->user_id, 
                'order_message',
                ['order_id' => $order->id, 'message_id' => $message->id]
            );
            
            // Also notify dropshipper if message is from seller
            if ($senderType === 'seller' && $order->dropshipper_id) {
                NotificationUtility::sendNotification(
                    'New message from seller', 
                    $message->content, 
                    $order->dropshipper_id, 
                    'order_message',
                    ['order_id' => $order->id, 'message_id' => $message->id]
                );
            }
        } elseif ($senderType === 'dropshipper') {
            // Notify customer
            NotificationUtility::sendNotification(
                'New message about your order', 
                $message->content, 
                $order->user_id, 
                'order_message',
                ['order_id' => $order->id, 'message_id' => $message->id]
            );
            
            // Also notify seller if applicable
            if ($order->seller_id) {
                NotificationUtility::sendNotification(
                    'New message from dropshipper', 
                    $message->content, 
                    $order->seller_id, 
                    'order_message',
                    ['order_id' => $order->id, 'message_id' => $message->id]
                );
            }
        }
    }
} 