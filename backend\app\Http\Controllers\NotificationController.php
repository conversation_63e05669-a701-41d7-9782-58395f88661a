<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use App\Models\User;
use App\Events\AdminNotificationEvent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use App\Notifications\CustomNotification;
use Carbon\Carbon;

class NotificationController extends Controller
{
    public function index()
    {
        $notifications = auth()->user()->notifications()->paginate(15);

        auth()->user()->markUnreadNotificationsAsRead();

        if (Auth::user()->user_type == 'admin') {
            return view('backend.notification.index', compact('notifications'));
        }

        if (Auth::user()->user_type == 'seller') {
            return view('seller.notification.index', compact('notifications'));
        }

        if (Auth::user()->user_type == 'customer') {
            return view('frontend.user.customer.notification.index', compact('notifications'));
        }
    }

    // Admin Management Methods
    public function adminIndex(Request $request)
    {
        $query = DB::table('notifications')
            ->join('users', 'notifications.notifiable_id', '=', 'users.id')
            ->select(
                'notifications.*',
                'users.name as user_name',
                'users.email as user_email'
            )
            ->where('notifications.notifiable_type', 'App\\Models\\User');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('notifications.data', 'like', "%{$search}%")
                  ->orWhere('users.name', 'like', "%{$search}%")
                  ->orWhere('users.email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('type')) {
            $query->where('notifications.type', 'like', "%{$request->type}%");
        }

        if ($request->filled('read')) {
            if ($request->read === 'true') {
                $query->whereNotNull('notifications.read_at');
            } else {
                $query->whereNull('notifications.read_at');
            }
        }

        $notifications = $query->orderBy('notifications.created_at', 'desc')
            ->paginate(50);

        $stats = $this->getNotificationStats();
        $users = User::where('user_type', '!=', 'admin')->get(['id', 'name', 'email']);

        return view('backend.notification.admin_manage', compact('notifications', 'stats', 'users'));
    }

    public function sendBulkNotification(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'user_ids' => 'required|array|min:1',
            'user_ids.*' => 'exists:users,id'
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        
        $notificationData = [
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'priority' => $request->priority,
            'link' => $request->link,
            'link_text' => $request->link_text,
            'admin_id' => auth()->id(),
            'sent_at' => now()
        ];

        foreach ($users as $user) {
            $user->notify(new CustomNotification($notificationData));
        }

        // Broadcast to real-time
        broadcast(new AdminNotificationEvent($notificationData, $request->user_ids));

        return response()->json([
            'success' => true,
            'message' => "Notification sent to {$users->count()} users successfully"
        ]);
    }

    public function bulkDelete(Request $request)
    {
        $request->validate([
            'notification_ids' => 'required|array|min:1',
            'notification_ids.*' => 'string'
        ]);

        $deleted = DB::table('notifications')
            ->whereIn('id', $request->notification_ids)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => "Deleted {$deleted} notifications successfully"
        ]);
    }

    public function getStats()
    {
        $stats = $this->getNotificationStats();
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    private function getNotificationStats()
    {
        $totalSent = DB::table('notifications')->count();
        $totalRead = DB::table('notifications')->whereNotNull('read_at')->count();
        $totalUnread = $totalSent - $totalRead;
        $recentActivity = DB::table('notifications')
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        $byType = DB::table('notifications')
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type');

        $readRate = $totalSent > 0 ? ($totalRead / $totalSent) * 100 : 0;

        return [
            'total_sent' => $totalSent,
            'total_read' => $totalRead,
            'total_unread' => $totalUnread,
            'recent_activity' => $recentActivity,
            'by_type' => $byType,
            'read_rate' => round($readRate, 1)
        ];
    }

    public function exportNotifications(Request $request)
    {
        $notifications = DB::table('notifications')
            ->join('users', 'notifications.notifiable_id', '=', 'users.id')
            ->select(
                'notifications.id',
                'notifications.type',
                'notifications.data',
                'notifications.read_at',
                'notifications.created_at',
                'users.name as user_name',
                'users.email as user_email'
            )
            ->orderBy('notifications.created_at', 'desc')
            ->get();

        $csvData = [];
        $csvData[] = ['ID', 'Type', 'User Name', 'User Email', 'Title', 'Message', 'Read Status', 'Created At'];

        foreach ($notifications as $notification) {
            $data = json_decode($notification->data, true);
            $csvData[] = [
                $notification->id,
                $notification->type,
                $notification->user_name,
                $notification->user_email,
                $data['title'] ?? '',
                $data['message'] ?? '',
                $notification->read_at ? 'Read' : 'Unread',
                $notification->created_at
            ];
        }

        $filename = 'notifications-' . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getUsers(Request $request)
    {
        $users = User::where('user_type', '!=', 'admin')
            ->when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', "%{$request->search}%")
                      ->orWhere('email', 'like', "%{$request->search}%");
            })
            ->select('id', 'name', 'email', 'user_type')
            ->paginate(100);

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    public function markAsRead(Request $request, $id)
    {
        $notification = DB::table('notifications')->where('id', $id)->first();
        
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Notification not found'], 404);
        }

        DB::table('notifications')->where('id', $id)->update(['read_at' => now()]);

        return response()->json(['success' => true, 'message' => 'Notification marked as read']);
    }

    public function deleteNotification($id)
    {
        $deleted = DB::table('notifications')->where('id', $id)->delete();
        
        if (!$deleted) {
            return response()->json(['success' => false, 'message' => 'Notification not found'], 404);
        }

        return response()->json(['success' => true, 'message' => 'Notification deleted successfully']);
    }
}
