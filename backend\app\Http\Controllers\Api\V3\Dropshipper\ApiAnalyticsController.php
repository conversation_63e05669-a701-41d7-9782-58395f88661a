<?php

namespace App\Http\Controllers\Api\v3\Dropshipper;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Controllers\Controller;
use App\Services\Analytics\DropshipperAnalyticsService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\RefundRequest;
use App\Models\Wishlist;
use App\Models\StockRequest;
use App\Models\Cart;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Helpers\OrderStatusHelper;
use App\Helpers\ProductPriceHelper;

class ApiAnalyticsController extends ApiResponse
{
    protected $analyticsService;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get comprehensive analytics overview for dropshipper dashboard
     * Returns all analytics data in a single API call
     */
    public function analytics_overview(Request $request)
    {
        try {
            Log::info('Analytics overview request started', [
                'user_id' => auth()->id(),
                'period' => $request->input('period', 'year')
            ]);

            // Check if user is authenticated
            if (!auth()->check()) {
                Log::warning('Unauthenticated access attempt to analytics overview');
                return $this->error('UNAUTHENTICATED', 'Authentication required', null, 401);
            }

            $validator = Validator::make($request->all(), [
                'period' => 'nullable|string|in:month,quarter,year,all',
            ]);

            if ($validator->fails()) {
                Log::warning('Validation failed for analytics overview', [
                    'errors' => $validator->errors()->messages()
                ]);
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $period = $request->input('period', 'year');
            $userId = auth()->id();

            Log::info('Starting analytics data collection', [
                'user_id' => $userId,
                'period' => $period
            ]);

            // Calculate date range based on period
            $dates = $this->calculatePeriodDates($period);
            $startDate = $dates['start'];
            $endDate = $dates['end'];
            $previousStartDate = $dates['previous_start'];
            $previousEndDate = $dates['previous_end'];

            Log::info('Date ranges calculated', [
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            // Get all analytics data
            $overview = $this->getOverviewData($userId, $startDate, $endDate, $previousStartDate, $previousEndDate);
            $salesTrend = $this->getSalesTrendData($userId, $startDate, $endDate);
            $categoryBreakdown = $this->getCategoryBreakdownData($userId, $startDate, $endDate);
            $recentOrders = $this->getRecentOrdersData($userId, 5);
            
            Log::info('About to get top products data');
            $topProducts = $this->getTopProductsData($userId, $startDate, $endDate);
            Log::info('Top products data retrieved', ['count' => count($topProducts)]);
            
            $buzfiCredits = $this->getBuzfiCreditsData($userId);
            $refundStats = $this->getRefundStatsData($userId, $startDate, $endDate);

            Log::info('Analytics data collected successfully');

            $responseData = [
                'overview' => $overview,
                'salesTrend' => $salesTrend,
                'categoryBreakdown' => $categoryBreakdown,
                'recentOrders' => $recentOrders,
                'topProducts' => $topProducts,
                'buzfiCredits' => $buzfiCredits,
                'refundStats' => $refundStats,
                'period' => [
                    'name' => $period,
                    'start' => $startDate->toDateString(),
                    'end' => $endDate->toDateString()
                ]
            ];

            return $this->success($responseData, 'Analytics data retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Analytics overview error', [
                'user_id' => auth()->id() ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error(
                'ANALYTICS_ERROR',
                'Failed to retrieve analytics data: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Calculate date ranges based on period
     */
    private function calculatePeriodDates($period)
    {
        try {
            $now = Carbon::now();

            switch ($period) {
                case 'month':
                    $start = $now->copy()->subMonth()->startOfDay();
                    $end = $now->copy()->endOfDay();
                    $previousStart = $now->copy()->subMonths(2)->startOfDay();
                    $previousEnd = $now->copy()->subMonth()->endOfDay();
                    break;

                case 'quarter':
                    $start = $now->copy()->subMonths(3)->startOfDay();
                    $end = $now->copy()->endOfDay();
                    $previousStart = $now->copy()->subMonths(6)->startOfDay();
                    $previousEnd = $now->copy()->subMonths(3)->endOfDay();
                    break;

                case 'year':
                    $start = $now->copy()->subYear()->startOfDay();
                    $end = $now->copy()->endOfDay();
                    $previousStart = $now->copy()->subYears(2)->startOfDay();
                    $previousEnd = $now->copy()->subYear()->endOfDay();
                    break;

                case 'all':
                default:
                    $start = $now->copy()->subYears(5)->startOfDay();
                    $end = $now->copy()->endOfDay();
                    $previousStart = $now->copy()->subYears(10)->startOfDay();
                    $previousEnd = $now->copy()->subYears(5)->endOfDay();
                    break;
            }

            return [
                'start' => $start,
                'end' => $end,
                'previous_start' => $previousStart,
                'previous_end' => $previousEnd
            ];
        } catch (\Exception $e) {
            Log::error('Error calculating period dates', [
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get overview data (cards data)
     */
    private function getOverviewData($userId, $startDate, $endDate, $previousStartDate, $previousEndDate)
    {
        try {
            // Current period data
            $currentOrders = Order::where('user_id', $userId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->get();

            $previousOrders = Order::where('user_id', $userId)
                ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
                ->get();

            $totalOrders = $currentOrders->count();
            $totalRevenue = $currentOrders->sum('grand_total') ?? 0;
            
            // Calculate savings with null checks
            $totalSavings = $currentOrders->sum(function($order) {
                return $order->orderDetails->sum(function($detail) {
                    $prices = ProductPriceHelper::getProductPrices($detail->product);
                    $regular_price = $prices['regularPrice'];
                    $dropshipper_price = $prices['displayPrice'];
                    $savings = round(($regular_price - $dropshipper_price)*$detail->quantity, 2);
                    return $savings;
                });
            }) ?? 0;

            // Get orders in progress
            $ordersInProgress = Order::where('user_id', $userId)
                ->whereIn('delivery_status', ['pending', 'confirmed', 'picked_up', 'on_the_way'])
                ->count();

            // Get Buzfi credits
            $wallet = Wallet::where('user_id', $userId)->first();
            $buzfiCredits = $wallet ? $wallet->balance : 0;

            // Get preordered products count from stock requests table
            $preorderedProducts = StockRequest::where('user_id', $userId)
                ->where('status', 'pending')
                ->count();

            // Get favorite products count from wishlist
            $favoriteProducts = Wishlist::where('user_id', $userId)->count();

            // Calculate growth percentages for previous period
            $previousTotalOrders = $previousOrders->count();
            $previousTotalRevenue = $previousOrders->sum('grand_total') ?? 0;
            $previousTotalSavings = $previousOrders->sum(function($order) {
                return $order->orderDetails->sum(function($detail) {
                    $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                    return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                });
            }) ?? 0;

            return [
                'totalOrders' => $totalOrders,
                'totalRevenue' => round($totalRevenue, 2),
                'totalProducts' => $currentOrders->sum(function($order) {
                    return $order->orderDetails->count();
                }),
                'totalSavings' => round($totalSavings, 2),
                'buzfiCredits' => round($buzfiCredits, 2),
                'preorderedProducts' => $preorderedProducts,
                'favoriteProducts' => $favoriteProducts,
                'ordersInProgress' => $ordersInProgress,
                'growth' => [
                    'orders' => $this->calculateGrowthPercentage($totalOrders, $previousTotalOrders),
                    'revenue' => $this->calculateGrowthPercentage($totalRevenue, $previousTotalRevenue),
                    'products' => 0, // Can be calculated if needed
                    'savings' => $this->calculateGrowthPercentage($totalSavings, $previousTotalSavings),
                ]
            ];
        } catch (\Exception $e) {
            Log::error('Error getting overview data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get sales trend data for charts
     */
    private function getSalesTrendData($userId, $startDate, $endDate)
    {
        try {
            $salesTrend = [];
            $days = $endDate->diffInDays($startDate);
            
            // If period is too long, group by months instead of days
            if ($days > 90) {
                $current = $startDate->copy()->startOfMonth();
                while ($current->lte($endDate)) {
                    $monthEnd = $current->copy()->endOfMonth();
                    if ($monthEnd->gt($endDate)) {
                        $monthEnd = $endDate;
                    }

                    $monthOrders = Order::where('user_id', $userId)
                        ->whereBetween('created_at', [$current, $monthEnd])
                        ->get();

                    $revenue = $monthOrders->sum('grand_total') ?? 0;
                    $orders = $monthOrders->count();
                    $savings = $monthOrders->sum(function($order) {
                        return $order->orderDetails->sum(function($detail) {
                            $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                            return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                        });
                    }) ?? 0;

                    $salesTrend[] = [
                        'date' => $current->format('M Y'),
                        'orders' => $orders,
                        'revenue' => round($revenue, 2),
                        'savings' => round($savings, 2)
                    ];

                    $current->addMonth();
                }
            } else {
                // Group by days
                for ($i = 0; $i <= $days; $i++) {
                    $date = $startDate->copy()->addDays($i);
                    
                    $dayOrders = Order::where('user_id', $userId)
                        ->whereDate('created_at', $date)
                        ->get();

                    $revenue = $dayOrders->sum('grand_total') ?? 0;
                    $orders = $dayOrders->count();
                    $savings = $dayOrders->sum(function($order) {
                        return $order->orderDetails->sum(function($detail) {
                            $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                            return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                        });
                    }) ?? 0;

                    $salesTrend[] = [
                        'date' => $date->format('M d'),
                        'orders' => $orders,
                        'revenue' => round($revenue, 2),
                        'savings' => round($savings, 2)
                    ];
                }
            }

            return $salesTrend;
        } catch (\Exception $e) {
            Log::error('Error getting sales trend data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return []; // Return empty array on error
        }
    }

    /**
     * Get category breakdown data
     */
    private function getCategoryBreakdownData($userId, $startDate, $endDate)
    {
        try {
            $categoryData = OrderDetail::with(['product.category', 'order'])
                ->whereHas('order', function($query) use ($userId, $startDate, $endDate) {
                    $query->where('user_id', $userId)
                          ->whereBetween('created_at', [$startDate, $endDate]);
                })
                ->get()
                ->groupBy(function($detail) {
                    return $detail->product && $detail->product->category 
                        ? $detail->product->category->name 
                        : 'Uncategorized';
                })
                ->map(function($details, $categoryName) {
                    $revenue = $details->sum(function($detail) {
                        return $detail->price * $detail->quantity;
                    });
                    $savings = $details->sum(function($detail) {
                        $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                        return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                    });
                    $count = $details->count();

                    return [
                        'name' => $categoryName,
                        'value' => $count,
                        'revenue' => round($revenue, 2),
                        'savings' => round($savings, 2)
                    ];
                })
                ->values()
                ->sortByDesc('revenue')
                ->take(10)
                ->values()
                ->all();

            return $categoryData;
        } catch (\Exception $e) {
            Log::error('Error getting category breakdown data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return []; // Return empty array on error
        }
    }

    /**
     * Get recent orders data
     */
    private function getRecentOrdersData($userId, $limit = 5)
    {
        try {
            $recentOrders = Order::with('orderDetails.product')
                ->where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->take($limit)
                ->get()
                ->map(function($order) {
                    $firstProduct = $order->orderDetails->first();
                    $productName = 'Unknown Product';
                    
                    if ($firstProduct && $firstProduct->product) {
                        $productName = $firstProduct->product->name;
                        if ($order->orderDetails->count() > 1) {
                            $productName .= ' (and ' . ($order->orderDetails->count() - 1) . ' more)';
                        }
                    }

                    $savings = $order->orderDetails->sum(function($detail) {
                        $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                        return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                    });

                    $retailPrice = $order->orderDetails->sum(function($detail) {
                        $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                        return $retailPrice * $detail->quantity;
                    });

                    return [
                        'id' => $order->code ?? 'ORD-' . $order->id,
                        'date' => $order->created_at->format('M d, Y'),
                        'status' => $this->getReadableOrderStatus($order->delivery_status ?? 'pending'),
                        'product' => $productName,
                        'revenue' => round($order->grand_total ?? 0, 2),
                        'savings' => round($savings, 2),
                        'retailPrice' => round($retailPrice, 2)
                    ];
                });

            return $recentOrders->toArray();
        } catch (\Exception $e) {
            Log::error('Error getting recent orders data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return []; // Return empty array on error
        }
    }

    /**
     * Convert order status keys to human-readable labels
     */
    private function getReadableOrderStatus($status)
    {
        return OrderStatusHelper::getReadableDeliveryStatus($status);
    }

    /**
     * Get top products data
     */
    private function getTopProductsData($userId, $startDate, $endDate)
    {
        try {
            Log::info('Getting top products data', [
                'user_id' => $userId,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString()
            ]);

            // First, get all order details for this user in the given period
            $orderDetailsQuery = OrderDetail::with(['product.category', 'order'])
                ->whereHas('order', function($query) use ($userId, $startDate, $endDate) {
                    $query->where('user_id', $userId)
                          ->whereBetween('created_at', [$startDate, $endDate]);
                })
                ->whereNotNull('product_id');

            $orderDetailsCount = $orderDetailsQuery->count();
            Log::info('Order details found', ['count' => $orderDetailsCount]);

            if ($orderDetailsCount === 0) {
                Log::info('No order details found for user in period');
                return [];
            }

            // Group by product and calculate metrics
            $productMetrics = $orderDetailsQuery->get()
                ->groupBy('product_id')
                ->map(function($details, $productId) {
                    $product = Product::find($productId);
                    if (!$product) {
                        return null;
                    }

                    $totalOrders = $details->count();
                    $totalQuantity = $details->sum('quantity');
                    $totalRevenue = $details->sum(function($detail) {
                        return $detail->price * $detail->quantity;
                    });

                    // Calculate savings properly
                    $totalSavings = $details->sum(function($detail) {
                        $retailPrice = $detail->retail_price ?? ($detail->price * 1.2);
                        return ($retailPrice * $detail->quantity) - ($detail->price * $detail->quantity);
                    });

                    // For now, we'll calculate views based on orders since we don't have view tracking
                    // In a real system, this would come from a separate views/analytics table
                    $estimatedViews = max($totalOrders * 10, 50); // Conservative estimate
                    $conversionRate = $estimatedViews > 0 ? round(($totalOrders / $estimatedViews) * 100, 1) : 0;

                    return [
                        'product_id' => $productId,
                        'name' => $product->name,
                        'orders' => $totalOrders,
                        'quantity' => $totalQuantity,
                        'views' => $estimatedViews,
                        'conversionRate' => $conversionRate,
                        'revenue' => round($totalRevenue, 2),
                        'savings' => round($totalSavings, 2),
                        'averageOrderValue' => $totalOrders > 0 ? round($totalRevenue / $totalOrders, 2) : 0
                    ];
                })
                ->filter() // Remove null values
                ->sortByDesc('revenue')
                ->take(10)
                ->values()
                ->all();

            Log::info('Top products calculated', [
                'count' => count($productMetrics),
                'top_product' => count($productMetrics) > 0 ? $productMetrics[0]['name'] ?? 'Unknown' : 'None'
            ]);

            return $productMetrics;

        } catch (\Exception $e) {
            Log::error('Error getting top products data', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return []; // Return empty array on error
        }
    }

    /**
     * Get Buzfi credits data
     */
    private function getBuzfiCreditsData($userId)
    {
        try {
            $wallet = Wallet::where('user_id', $userId)->first();
            $available = $wallet ? $wallet->balance : 0;

            // Get credit history
            $history = WalletTransaction::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get()
                ->map(function($transaction) {
                    return [
                        'date' => $transaction->created_at->format('M d'),
                        'type' => $transaction->amount > 0 ? 'Earned' : 'Used',
                        'amount' => abs($transaction->amount),
                        'source' => $transaction->reference_type ?? 'System'
                    ];
                });

            return [
                'available' => round($available, 2),
                'history' => $history->toArray()
            ];
        } catch (\Exception $e) {
            Log::error('Error getting buzfi credits data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [
                'available' => 0,
                'history' => []
            ];
        }
    }

    /**
     * Get refund statistics data
     */
    private function getRefundStatsData($userId, $startDate, $endDate)
    {
        try {
            $refunds = RefundRequest::whereHas('orderDetail.order', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })->whereBetween('created_at', [$startDate, $endDate])->get();

            $totalRefunds = $refunds->count();
            $pendingRefunds = $refunds->where('status', 'pending')->count();
            $completedRefunds = $refunds->where('status', 'approved')->count();
            $totalRefundAmount = $refunds->where('status', 'approved')->sum('refund_amount') ?? 0;

            // Refunds by method (simplified)
            $refundsByMethod = [
                [
                    'method' => 'Original Payment',
                    'count' => intval($completedRefunds * 0.6),
                    'amount' => round($totalRefundAmount * 0.6, 2)
                ],
                [
                    'method' => 'Buzfi Credits',
                    'count' => intval($completedRefunds * 0.4),
                    'amount' => round($totalRefundAmount * 0.4, 2)
                ]
            ];

            return [
                'totalRefunds' => $totalRefunds,
                'pendingRefunds' => $pendingRefunds,
                'completedRefunds' => $completedRefunds,
                'totalRefundAmount' => round($totalRefundAmount, 2),
                'refundsByMethod' => $refundsByMethod
            ];
        } catch (\Exception $e) {
            Log::error('Error getting refund stats data', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [
                'totalRefunds' => 0,
                'pendingRefunds' => 0,
                'completedRefunds' => 0,
                'totalRefundAmount' => 0,
                'refundsByMethod' => []
            ];
        }
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage($current, $previous)
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return round((($current - $previous) / $previous) * 100, 1);
    }

    public function analytics_dashboard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,this_month,last_month,this_year,custom',
            'start_date' => 'required_if:period,custom|date_format:Y-m-d',
            'end_date' => 'required_if:period,custom|date_format:Y-m-d|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $period = $request->input('period', 'last_30_days');

        // Initialize analytics service with current user ID
        $this->analyticsService = new DropshipperAnalyticsService(auth()->id());

        // Calculate date range based on period
        $dates = $this->analyticsService->calculateDateRange($period, $request->start_date, $request->end_date);
        $startDate = $dates['start'];
        $endDate = $dates['end'];
        $previousStartDate = $dates['previous_start'];
        $previousEndDate = $dates['previous_end'];

        // Get current period data
        $currentData = $this->analyticsService->getAnalyticsData($startDate, $endDate);

        // Get previous period data for comparison
        $previousData = $this->analyticsService->getAnalyticsData($previousStartDate, $previousEndDate);

        // Calculate comparisons
        $comparisons = $this->analyticsService->calculateComparisons($currentData, $previousData);

        // Get chart data
        $charts = $this->analyticsService->generateChartData($startDate, $endDate);

        // Get top products
        $topProducts = $this->analyticsService->getTopProducts($startDate, $endDate);

        return $this->success([
            'period' => [
                'name' => $period,
                'start' => $startDate->toIso8601String(),
                'end' => $endDate->toIso8601String()
            ],
            'summary' => [
                'total_sales' => $currentData['total_sales'],
                'total_orders' => $currentData['total_orders'],
                'total_commission' => $currentData['total_commission'],
                'average_order_value' => $currentData['average_order_value'],
                'conversion_rate' => $currentData['conversion_rate']
            ],
            'comparisons' => $comparisons,
            'charts' => $charts,
            'top_products' => $topProducts
        ]);
    }
    public function analytics_sales(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,this_month,last_month,this_year,custom',
            'start_date' => 'required_if:period,custom|date_format:Y-m-d',
            'end_date' => 'required_if:period,custom|date_format:Y-m-d|after_or_equal:start_date',
            'group_by' => 'nullable|string|in:day,week,month'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $period = $request->input('period', 'last_30_days');
        $groupBy = $request->input('group_by', 'day');

        // Initialize analytics service with current user ID
        $this->analyticsService = new DropshipperAnalyticsService(auth()->id());

        // Calculate date range based on period
        $dates = $this->analyticsService->calculateDateRange($period, $request->start_date, $request->end_date);
        $startDate = $dates['start'];
        $endDate = $dates['end'];

        // Get orders within date range
        $orders = Order::with(['orderDetails.product.category'])
            ->where('user_id', auth()->id())
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        // Calculate summary
        $totalSales = $orders->sum('grand_total');
        $totalOrders = $orders->count();
        $totalCommission = $orders->sum(function($order) {
            return $order->orderDetails->sum('seller_commission');
        });

        // Group data by time
        $timeData = $this->analyticsService->groupDataByTime($orders, $groupBy, $startDate, $endDate);

        // Calculate sales by product category
        $salesByCategory = $this->analyticsService->calculateSalesByCategory($orders);

        return $this->success([
            'period' => [
                'name' => $period,
                'start' => $startDate->toIso8601String(),
                'end' => $endDate->toIso8601String()
            ],
            'summary' => [
                'total_sales' => round($totalSales, 2),
                'total_orders' => $totalOrders,
                'average_order_value' => $totalOrders > 0 ? round($totalSales / $totalOrders, 2) : 0,
                'total_commission' => round($totalCommission, 2)
            ],
            'sales_by_time' => [
                'labels' => $timeData['labels'],
                'datasets' => [
                    [
                        'label' => 'Sales',
                        'data' => $timeData['sales']
                    ]
                ]
            ],
            'orders_by_time' => [
                'labels' => $timeData['labels'],
                'datasets' => [
                    [
                        'label' => 'Orders',
                        'data' => $timeData['orders']
                    ]
                ]
            ],
            'sales_by_product_category' => $salesByCategory
        ]);
    }




    public function analytics_products(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,this_month,last_month,this_year,custom',
            'start_date' => 'required_if:period,custom|date_format:Y-m-d',
            'end_date' => 'required_if:period,custom|date_format:Y-m-d|after_or_equal:start_date',
            'sort_by' => 'nullable|string|in:revenue,orders,commission,conversion_rate',
            'sort_order' => 'nullable|string|in:asc,desc',
            'limit' => 'nullable|integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $period = $request->input('period', 'last_30_days');
        $sortBy = $request->input('sort_by', 'revenue');
        $sortOrder = $request->input('sort_order', 'desc');
        $limit = min($request->input('limit', 10), 100);

        // Initialize analytics service with current user ID
        $this->analyticsService = new DropshipperAnalyticsService(auth()->id());

        // Calculate date range based on period
        $dates = $this->analyticsService->calculateDateRange($period, $request->start_date, $request->end_date);
        $startDate = $dates['start'];
        $endDate = $dates['end'];

        // Get product analytics data
        $analyticsData = $this->analyticsService->getProductAnalytics(
            $startDate, 
            $endDate, 
            $sortBy, 
            $sortOrder, 
            $limit
        );

        return $this->success([
            'period' => [
                'name' => $period,
                'start' => $startDate->toIso8601String(),
                'end' => $endDate->toIso8601String()
            ],
            'summary' => $analyticsData['summary'],
            'products' => $analyticsData['products']
        ]);
    }
    public function analytics_performance_report(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,this_month,last_month,this_year,custom',
            'start_date' => 'required_if:period,custom|date_format:Y-m-d',
            'end_date' => 'required_if:period,custom|date_format:Y-m-d|after_or_equal:start_date',
            'format' => 'nullable|string|in:json,pdf,csv'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $period = $request->input('period', 'last_30_days');
        $format = $request->input('format', 'json');

        // Initialize analytics service with current user ID
        $this->analyticsService = new DropshipperAnalyticsService(auth()->id());

        // Calculate date range based on period
        $dates = $this->analyticsService->calculateDateRange($period, $request->start_date, $request->end_date);
        $startDate = $dates['start'];
        $endDate = $dates['end'];
        $previousStartDate = $dates['previous_start'];
        $previousEndDate = $dates['previous_end'];

        // Get performance report data
        $reportData = $this->analyticsService->getPerformanceReport($startDate, $endDate, $previousStartDate, $previousEndDate);

        // Handle different output formats
        switch ($format) {
            case 'pdf':
                return $this->analyticsService->generatePdfReport($reportData);
            case 'csv':
                return $this->analyticsService->generateCsvReport($reportData);
            default:
                return $this->success($reportData);
        }
    }
    public function analytics_export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'period' => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,this_month,last_month,this_year,custom',
            'metrics' => 'required|array',
            'metrics.*' => 'required|string|in:sales,orders,commission,products',
            'group_by' => 'nullable|string|in:day,week,month'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $period = $request->input('period', 'last_30_days');
        $metrics = $request->input('metrics', []);
        $groupBy = $request->input('group_by', 'day');

        // Initialize analytics service with current user ID
        $this->analyticsService = new DropshipperAnalyticsService(auth()->id());

        // Calculate date range based on period
        $dates = $this->analyticsService->calculateDateRange($period, $request->start_date, $request->end_date);
        $startDate = $dates['start'];
        $endDate = $dates['end'];

        // Generate export
        $exportData = $this->analyticsService->generateExport($startDate, $endDate, $metrics, $groupBy);

        return $this->success([
            'export_id' => $exportData['export_id'],
            'export_url' => $exportData['export_url'],
            'expires_at' => $exportData['expires_at']
        ], 'Analytics data export created successfully');
    }

    /**
     * Test endpoint to debug authentication and response formatting
     */
    public function test_endpoint()
    {
        try {
            Log::info('Test endpoint called');
            
            $user = Auth::user();
            Log::info('User authentication check', [
                'user' => $user ? $user->id : 'null',
                'authenticated' => Auth::check()
            ]);

            if (!$user) {
                Log::warning('No authenticated user found');
                return $this->error('AUTH_FAILED', 'Authentication required', null, 401);
            }

            return $this->success([
                'message' => 'Test endpoint working',
                'user_id' => $user->id,
                'user_email' => $user->email,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Test endpoint error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error('SERVER_ERROR', 'Internal server error: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * Simple test endpoint to debug the response issue
     */
    public function debug_test()
    {
        try {
            // Check if the user is authenticated
            $user = auth()->user();
            if (!$user) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Test basic response
            return response()->json([
                'status' => 'success',
                'message' => 'Debug test working',
                'user_id' => $user->id,
                'timestamp' => now()->toString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Exception: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Debug endpoint for troubleshooting top products data
     */
    public function debug_top_products(Request $request)
    {
        try {
            Log::info('Debug top products endpoint called');
            
            $user = Auth::user();
            if (!$user) {
                return $this->error('AUTH_FAILED', 'Authentication required', null, 401);
            }

            $period = $request->input('period', 'year');
            $userId = $user->id;

            // Calculate date range
            $dates = $this->calculatePeriodDates($period);
            $startDate = $dates['start'];
            $endDate = $dates['end'];

            // Debug information
            $debugInfo = [
                'user_id' => $userId,
                'period' => $period,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ];

            // Check if user has any orders
            $totalOrders = Order::where('user_id', $userId)->count();
            $ordersInPeriod = Order::where('user_id', $userId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $debugInfo['total_orders_all_time'] = $totalOrders;
            $debugInfo['orders_in_period'] = $ordersInPeriod;

            // Check order details
            $totalOrderDetails = OrderDetail::whereHas('order', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })->count();

            $orderDetailsInPeriod = OrderDetail::whereHas('order', function($query) use ($userId, $startDate, $endDate) {
                $query->where('user_id', $userId)
                      ->whereBetween('created_at', [$startDate, $endDate]);
            })->count();

            $debugInfo['total_order_details_all_time'] = $totalOrderDetails;
            $debugInfo['order_details_in_period'] = $orderDetailsInPeriod;

            // Get sample order details
            $sampleOrderDetails = OrderDetail::with(['product', 'order'])
                ->whereHas('order', function($query) use ($userId) {
                    $query->where('user_id', $userId);
                })
                ->take(5)
                ->get()
                ->map(function($detail) {
                    return [
                        'id' => $detail->id,
                        'product_id' => $detail->product_id,
                        'product_name' => $detail->product ? $detail->product->name : 'No Product',
                        'price' => $detail->price,
                        'quantity' => $detail->quantity,
                        'order_id' => $detail->order_id,
                        'order_date' => $detail->order ? $detail->order->created_at->toDateString() : 'No Order'
                    ];
                });

            $debugInfo['sample_order_details'] = $sampleOrderDetails;

            // Try to get top products data
            $topProducts = $this->getTopProductsData($userId, $startDate, $endDate);
            $debugInfo['top_products_count'] = count($topProducts);
            $debugInfo['top_products'] = $topProducts;

            // Check for products with null product_id
            $nullProductDetails = OrderDetail::whereNull('product_id')
                ->whereHas('order', function($query) use ($userId) {
                    $query->where('user_id', $userId);
                })
                ->count();

            $debugInfo['null_product_details_count'] = $nullProductDetails;

            return $this->success($debugInfo, 'Debug information retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Debug top products error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error('DEBUG_ERROR', 'Debug error: ' . $e->getMessage(), null, 500);
        }
    }

}
