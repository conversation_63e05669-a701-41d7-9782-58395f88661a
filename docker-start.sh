#!/bin/bash

# Docker Startup Script for Laravel Backend with PHP 8.2, MySQL, Nginx, phpMyAdmin, and Composer
# This script starts the essential services for your Laravel backend development

echo "🚀 Starting Docker containers for Laravel Backend..."
echo "Services: PHP 8.2, MySQL, Nginx, php<PERSON><PERSON><PERSON><PERSON><PERSON>, Composer"

# Navigate to laradock directory
cd laradock

# Start the essential services
echo "📦 Starting containers..."
docker-compose up -d nginx mysql phpmyadmin workspace

echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if containers are running
echo "🔍 Checking container status..."
docker-compose ps

echo ""
echo "✅ Docker containers are now running!"
echo ""
echo "🌐 Access your application:"
echo "   - Laravel App: http://localhost/buzfi"
echo "   - phpMyAdmin: http://localhost:8081"
echo "   - MySQL Port: 3306"
echo ""
echo "🔧 Database Configuration:"
echo "   - Host: mysql"
echo "   - Database: default"
echo "   - Username: default"
echo "   - Password: secret"
echo "   - Root Password: root"
echo ""
echo "💻 To access the workspace container:"
echo "   docker-compose exec workspace bash"
echo ""
echo "🛑 To stop all containers:"
echo "   docker-compose down"
echo ""
echo "📝 Your backend folder is mounted at /var/www in the containers"
