<?php

namespace App\Http\Resources\V3\Rerurn;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\V3\Rerurn\RerurnRequestResource;
class RerurnRequestsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->map(function($item) {
            return new RerurnRequestResource($item);
        });
    }
}
