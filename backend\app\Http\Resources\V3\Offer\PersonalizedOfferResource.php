<?php

namespace App\Http\Resources\V3\Offer;

use Illuminate\Http\Resources\Json\JsonResource;

class PersonalizedOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'offer_type' => $this->offer_type,
            'discount_type' => $this->discount_type,
            'discount_value' => (float) $this->discount_value,
            'start_date' => $this->start_date->toIso8601String(),
            'end_date' => isset($this->end_date) ? $this->end_date->toIso8601String() : null,
            'is_active' => (bool) $this->is_active,
            'is_exclusive' => (bool) $this->is_exclusive,
            'single_use' => (bool) $this->single_use,
            'redemption_count' => (int) $this->redemption_count,
            'priority' => (int) $this->priority,
            'product_ids' => $this->product_ids,
            'category_ids' => $this->category_ids,
            'customer_groups' => $this->customer_groups,
            'min_purchase_amount' => isset($this->min_purchase_amount) ? (float) $this->min_purchase_amount : null,
            'max_discount_amount' => isset($this->max_discount_amount) ? (float) $this->max_discount_amount : null,
            'image_url' => $this->image_url,
            'banner_image' => $this->banner_image,
            'cta_text' => $this->cta_text,
            'cta_url' => $this->cta_url,
            'terms_conditions' => $this->terms_conditions,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
            'is_saved' => $this->when(auth()->check(), function () {
                return auth()->user()->savedOffers()->where('offer_id', $this->id)->exists();
            }, false),
        ];
    }
} 