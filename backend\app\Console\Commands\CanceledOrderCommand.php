<?php

namespace App\Console\Commands;

use App\Models\OrderDetail;
use App\Notifications\order\OrderCanceledEmailNotification;
use Illuminate\Console\Command;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
class CanceledOrderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'CanceledOrderCommand';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $endRange = Carbon::now();
        $orders = Order::where('payment_status', 'unpaid')
            ->where('delivery_status', 'pending')
            ->whereNull('pending_order_email')
            ->whereNotNull('cancel_pending_order_email')
            ->where('cancel_pending_order_email', '<=', $endRange)
            ->get();
        if ($orders) {
            try {
                foreach ($orders as $order) {
                    $order->delivery_status = 'cancelled';
                    $order->cancel_pending_order_email = null;
                    $order->save();
                    $orderDetail = OrderDetail::where('order_id', $order->id)->first();
                    if ($orderDetail) {
                        $orderDetail->cancel_reason = 11; // Set the attribute
                        $orderDetail->save();            // Save the changes to the database
                    }
                    $array = array();
                    $array['order'] = $order;
                    $array['cancel_reason'] = 'Did Not  Pay On Time';
                    $array['cancel_by'] = "Admin";
                    $array['user_name'] = $order->user->name;
                    $array['subject'] = translate('Update on Your Order ') . ' - ' . $order->code . ' ' . ' Canceled';
                    try {
                        $order->user->notify(new OrderCanceledEmailNotification($array));
                    } catch (\Exception $e) {
                        Log::channel('command_logs')->error('Error occurred while sending Has Been Order Cancelled email in CanceledOrderCommand 2: ' . $e->getMessage());
                    }
                }
            } catch (\Exception $e) {
                Log::channel('command_logs')->error('Error occurred while sending Has Been Order Cancelled email in CanceledOrderCommand 3: ' . $e->getMessage());
            }
        }
        return Command::SUCCESS;
    }
}
