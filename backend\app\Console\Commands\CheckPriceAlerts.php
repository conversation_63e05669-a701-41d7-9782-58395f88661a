<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ProductMeta;
use App\Models\User;
use App\Notifications\PriceDropAlert;
use App\Events\PriceAlertEvent;
use App\Http\Controllers\Api\V3\NotificationController;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckPriceAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'price-alerts:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for price drops and send notifications for price alerts';

    /**
     * The notification controller instance.
     *
     * @var NotificationController
     */
    protected $notificationController;

    /**
     * Create a new command instance.
     *
     * @param NotificationController $notificationController
     * @return void
     */
    public function __construct(NotificationController $notificationController)
    {
        parent::__construct();
        $this->notificationController = $notificationController;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking price alerts...');
        
        // Get all price alerts
        $alerts = ProductMeta::where('meta_key', 'price_alert')->get();
        
        $notifiedCount = 0;
        $processedCount = 0;
        
        foreach ($alerts as $alert) {
            $processedCount++;
            
            $alertData = json_decode($alert->meta_value, true);
            
            // Skip already triggered alerts
            if ($alertData['status'] !== 'active') {
                continue;
            }
            
            // Get product and user
            $product = Product::find($alert->product_id);
            $user = User::find($alert->user_id);
            
            if (!$product || !$user) {
                continue;
            }
            
            $currentPrice = $product->unit_price;
            $originalPrice = $alertData['currentPrice'];
            
            // Check if price has dropped
            $triggered = false;
            
            if ($alertData['alertType'] === 'any' && $currentPrice < $originalPrice) {
                $triggered = true;
            } elseif ($alertData['alertType'] === 'percentage') {
                $threshold = $alertData['percentageThreshold'];
                $percentageOff = (($originalPrice - $currentPrice) / $originalPrice) * 100;
                
                if ($percentageOff >= $threshold) {
                    $triggered = true;
                }
            } elseif ($alertData['alertType'] === 'specific' && $currentPrice <= $alertData['targetPrice']) {
                $triggered = true;
            }
            
            if ($triggered) {
                // Update status in the database
                $alertData['status'] = 'triggered';
                $alert->meta_value = json_encode($alertData);
                $alert->save();
                
                // Send notification
                try {
                    // Send email notification
                    $user->notify(new PriceDropAlert($alertData, $product, $originalPrice, $currentPrice));
                    
                    // Create in-app notification
                    $this->notificationController->createPriceAlertNotification($user->id, $alertData, $currentPrice);
                    
                    // Also broadcast a real-time event via Pusher
                    event(new PriceAlertEvent($user->id, $alertData, $product, $originalPrice, $currentPrice));
                    
                    $notifiedCount++;
                } catch (\Exception $e) {
                    Log::error('Failed to send price alert notification: ' . $e->getMessage());
                }
            }
        }
        
        $this->info("Processed {$processedCount} alerts. Sent {$notifiedCount} notifications.");
        
        return Command::SUCCESS;
    }
} 