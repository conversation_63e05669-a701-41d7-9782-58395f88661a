# 🚀 Quick Reference - Docker Laravel Setup

## 🏁 Getting Started

### First Time Setup
```bash
./complete-setup.sh
```

### Quick Start (after initial setup)
```bash
./docker-start.sh
```

## 🌐 Access Points

| Service | URL | Credentials |
|---------|-----|-------------|
| **<PERSON>vel App** | http://localhost | - |
| **phpMyAdmin** | http://localhost:8081 | user: `default`, pass: `secret` |
| **MySQL** | localhost:3306 | user: `default`, pass: `secret` |

## 💻 Essential Commands

### Container Management
```bash
# Start containers
cd laradock && docker-compose up -d nginx mysql phpmyadmin workspace

# Stop containers
cd laradock && docker-compose down

# Restart containers
cd laradock && docker-compose restart

# Check status
cd laradock && docker-compose ps
```

### Laravel Development
```bash
# Access workspace container
docker-compose exec workspace bash

# Inside container - navigate to project
cd /var/www/backend

# Common Laravel commands
php artisan migrate
php artisan make:model Product
php artisan make:controller ProductController
php artisan cache:clear
composer install
composer require package-name
```

### Database Operations
```bash
# Run migrations
docker-compose exec workspace php artisan migrate

# Fresh migration with seeding
docker-compose exec workspace php artisan migrate:fresh --seed

# Create migration
docker-compose exec workspace php artisan make:migration create_products_table

# Create seeder
docker-compose exec workspace php artisan make:seeder ProductSeeder
```

### File Permissions (if needed)
```bash
# Fix Laravel permissions
docker-compose exec workspace chmod -R 775 /var/www/backend/storage
docker-compose exec workspace chmod -R 775 /var/www/backend/bootstrap/cache
```

## 🔧 Configuration Files

### Laravel Database Config (.env)
```env
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=default
DB_USERNAME=default
DB_PASSWORD=secret
```

### Docker Services (laradock/.env)
```env
PHP_VERSION=8.2
MYSQL_VERSION=8.4
MYSQL_DATABASE=default
MYSQL_USER=default
MYSQL_PASSWORD=secret
MYSQL_ROOT_PASSWORD=root
```

## 🐛 Troubleshooting

### Common Issues & Solutions

**Port 80 already in use:**
```bash
# Stop other web servers
sudo service apache2 stop
sudo service nginx stop
```

**Permission denied:**
```bash
# Fix file permissions
docker-compose exec workspace chown -R laradock:laradock /var/www/backend
```

**Database connection failed:**
```bash
# Wait for MySQL to start completely
sleep 10
# Check if MySQL is running
docker-compose ps mysql
```

**Composer issues:**
```bash
# Clear Composer cache
docker-compose exec workspace composer clear-cache
# Update Composer
docker-compose exec workspace composer self-update
```

## 📁 Project Structure
```
buzfi-docker/
├── backend/                    # Laravel application
│   ├── app/
│   ├── public/
│   ├── .env                   # Laravel environment config
│   └── composer.json
├── laradock/                  # Docker configuration
│   ├── .env                   # Docker environment config
│   ├── docker-compose.yml
│   └── nginx/sites/default.conf
├── complete-setup.sh          # Full setup script
├── docker-start.sh           # Quick start script
└── DOCKER_README.md          # Detailed documentation
```

## 🔄 Daily Workflow

1. **Start development:**
   ```bash
   ./docker-start.sh
   ```

2. **Access workspace:**
   ```bash
   docker-compose exec workspace bash
   cd /var/www/backend
   ```

3. **Make changes to your Laravel code**

4. **Test in browser:** http://localhost

5. **Stop when done:**
   ```bash
   cd laradock && docker-compose down
   ```

## 📚 Need More Help?

- **Detailed docs:** See `DOCKER_README.md`
- **Laradock docs:** https://laradock.io/
- **Laravel docs:** https://laravel.com/docs
