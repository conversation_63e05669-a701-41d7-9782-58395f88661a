<?php

namespace App\Http\Resources\V3\Notifications;

use Illuminate\Http\Resources\Json\JsonResource;

class NotificationPreferencesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Default preferences
        $defaultPreferences = [
            'order_updates' => true,
            'stock_alerts' => true,
            'product_changes' => true,
            'commission_notifications' => true,
            'support_messages' => true,
            'system_announcements' => true,
            'email_notifications' => true,
            'push_notifications' => false,
            'notification_sound' => true
        ];
        
        // Merge with user preferences, defaulting to system defaults if not set
        return array_merge($defaultPreferences, $this->resource);
    }
}
