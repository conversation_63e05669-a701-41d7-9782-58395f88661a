<?php

namespace App\Http\Controllers;

use App\Models\Collection;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;

class CollectionController extends Controller
{
    public function index()
    {
        try {
            $collections = Collection::all();
            return view('backend.collections.index', compact('collections'));
        } catch (\Exception $e) {
            Log::error('Error fetching collections: ' . $e->getMessage());
            flash(translate('An error occurred while fetching collections.'))->error();
            return redirect()->back();
        }
    }

    public function create()
    {
        try {
            $products = Product::where('published', 1)->get();
            $categories = Category::all();
            $brands = Brand::all();
            $suppliers = Supplier::all();
            return view('backend.collections.create', compact('categories', 'brands', 'suppliers', 'products'));
        } catch (\Exception $e) {
            Log::error('Error preparing collection creation: ' . $e->getMessage());
            flash(translate('An error occurred while preparing the creation form.'))->error();
            return redirect()->back();
        }
    }

    public function store(Request $request)
    {
        try {
            $validated = $this->validateCollection($request);

            $collection = new Collection;
            $collection->name = $request->name;
            $collection->description = $request->description;
            $collection->type = $request->type;
            $collection->sort_field = $request->sort_field;
            $collection->sort_order = $request->sort_order;
            $collection->visible = true;

            if ($request->type === 'automated') {
                $collection->conditions = $this->prepareConditions($request);
                $collection->product_ids = null; // Ensure product_ids is not set for automated type
            } else {
                $collection->product_ids = json_encode($request->input('product_ids', [])); // Store product_ids as JSON
            }

            $collection->save();

            flash(translate('Collection created successfully.'))->success();
            return redirect()->route('collections.index');
        } catch (ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Error storing collection: ' . $e->getMessage());
            flash(translate('An error occurred while creating the collection.'))->error();
            return redirect()->back();
        }
    }

    public function edit($id)
    {
        try {
            $collection = Collection::findOrFail($id);
            $products = $this->applyConditions($collection);
            // dd($products);
            $suppliers = Supplier::all();
            $categories = Category::all();
            $brands = Brand::all();
            $tags = [];
            if ($collection->type === 'automated') {
                $tags = preg_split('/\s*,\s*(?![^(]*\))/', $collection->conditions['tags']);
            }

            return view('backend.collections.edit', compact('collection', 'products', 'suppliers', 'categories', 'brands', 'tags'));
        } catch (ModelNotFoundException $e) {
            Log::error('Collection not found: ' . $e->getMessage());
            flash(translate('Collection not found.'))->error();
            return redirect()->back();
        } catch (\Exception $e) {
            Log::error('Error preparing collection edit: ' . $e->getMessage());
            flash(translate('An error occurred while preparing the edit form.'))->error();
            return redirect()->back();
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $validated = $this->validateCollection($request);

            $collection = Collection::findOrFail($id);
            $collection->name = $request->name;
            $collection->description = $request->description;
            $collection->type = $request->type;
            $collection->sort_field = $request->sort_field;
            $collection->sort_order = $request->sort_order;
            $collection->visible =  true;

            if ($request->type === 'automated') {
                $collection->conditions = $this->prepareConditions($request);
                $collection->product_ids = null; // Ensure product_ids is not set for automated type
            } else {
                $collection->conditions = null; // Clear conditions for manual type
                $collection->product_ids = json_encode($request->input('product_ids', [])); // Store product_ids as JSON
            }

            $collection->save();
            flash(translate('Collection updated successfully.'))->success();
            return redirect()->route('collections.index');
        } catch (ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (ModelNotFoundException $e) {
            Log::error('Collection not found: ' . $e->getMessage());
            flash(translate('Collection not found.'))->error();
            return redirect()->back();
        } catch (\Exception $e) {
            Log::error('Error updating collection: ' . $e->getMessage());
            flash(translate('An error occurred while updating the collection.'))->error();
            return redirect()->back();
        }
    }

    public function destroy(Collection $collection)
    {
        try {
            // $collection = Collection::findOrFail($id);
            $collection->delete();
            return redirect()->route('collections.index')->with('success', 'Collection deleted successfully.');
        } catch (ModelNotFoundException $e) {
            Log::error('Collection not found: ' . $e->getMessage());
            flash(translate('Collection not found.'))->error();
            return redirect()->back()->with('error', 'Collection not found.');
        } catch (\Exception $e) {
            Log::error('Error deleting collection: ' . $e->getMessage());
            flash(translate('An error occurred while deleting the collection.'))->error();
            return redirect()->back();
        }
    }

    public function show(Collection $collection)
    {
        try {
            $products = $this->applyConditions($collection);
            // dd($products);
            return view('backend.collections.show', compact('collection', 'products'));
        } catch (\Exception $e) {
            Log::error('Error showing collection: ' . $e->getMessage());
            flash(translate('An error occurred while showing the collection.'))->error();
            return redirect()->back();
        }
    }

    private function validateCollection(Request $request)
    {
        return $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:manual,automated',
            'tags' => 'nullable|string',
        ]);
    }

    private function prepareConditions(Request $request)
    {
        return [
            'tags' => $request->input('tags', []) // Adding tags here
        ];
    }

    public function apiIndex(Request $request)
    {
        $search = $request->input('q');
        $products = Product::query()
            ->where('name', 'LIKE', "%{$search}%")
            ->limit(10)
            ->get(['id', 'name', 'thumbnail_img']);

        $products->each(function ($product) {
            $product->thumbnail_img = uploaded_asset($product->thumbnail_img);
        });

        return response()->json($products);
    }

    public function searchProduct(Request $request, $collectionId)
    {
        $collection = Collection::findOrFail($collectionId);

        $searchTerm = $request->input('search', '');

        // Query for products based on the search term
        $products = $this->applyConditions($collection);
        $products->where(function ($q) use ($searchTerm) {
            foreach (explode(' ', trim($searchTerm)) as $word) {
                $q->where('name', 'like', '%' . $word . '%')
                    ->orWhere('tags', 'like', '%' . $word . '%')
                    ->orWhereHas('product_translations', function ($q) use ($word) {
                        $q->where('name', 'like', '%' . $word . '%');
                    })
                    ->orWhereHas('stocks', function ($q) use ($word) {
                        $q->where('sku', 'like', '%' . $word . '%');
                    });
            }
        });

        return view('backend.collections.show', compact('collection', 'products', 'searchTerm'));
    }

    private function applyConditions(Collection $collection)
    {
        $query = Product::query();

        if ($collection->type === 'automated') {
            $conditions = $collection->conditions;

            if (!empty($conditions['tags']) && $conditions['tags']) {
                $tags = preg_split('/\s*,\s*(?![^(]*\))/', $conditions['tags']);
                // dd($tags);
                $query->where(function ($p) use ($tags) {
                    foreach ($tags as $tag) {
                        $p->orWhere('tags', 'like', "%{$tag}%"); // Assuming `tags` is a searchable field
                    }
                });
            }
        } elseif ($collection->type === 'manual') {
            // Load products based on product_ids for manual collections
            $query->whereIn('id', $collection->product_ids);
        }
        $query->orderBy($collection->sort_field, $collection->sort_order);
        $products = filter_products($query)->with('taxes')->paginate(24);
        return $products;
    }
}
