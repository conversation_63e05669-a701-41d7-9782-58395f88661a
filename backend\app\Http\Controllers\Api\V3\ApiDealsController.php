<?php

namespace App\Http\Controllers\Api\V3;

use App\Services\Product\TodaysDealsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ApiDealsController extends ApiResponse
{
    protected TodaysDealsService $todaysDealsService;

    public function __construct(TodaysDealsService $todaysDealsService)
    {
        parent::__construct();
        $this->todaysDealsService = $todaysDealsService;
    }

    /**
     * Get all today's deals
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTodaysDeals(Request $request): JsonResponse
    {
        try {
            $deals = $this->todaysDealsService->getAllTodaysDeals();
            
            return $this->success($deals);
        } catch (\Exception $e) {
            Log::error('Error fetching today\'s deals: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch today\'s deals',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get deal of the day (highest discount deal)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDealOfTheDay(Request $request): JsonResponse
    {
        try {
            $dealOfTheDay = $this->todaysDealsService->getDealOfTheDay();
            
            if (!$dealOfTheDay) {
                return $this->error(
                    'NOT_FOUND',
                    'No deal of the day available',
                    null,
                    404
                );
            }
            
            return $this->success($dealOfTheDay);
        } catch (\Exception $e) {
            Log::error('Error fetching deal of the day: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch deal of the day',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get deals by category
     *
     * @param Request $request
     * @param string $category
     * @return JsonResponse
     */
    public function getDealsByCategory(Request $request, string $category): JsonResponse
    {
        try {
            $categoryDeals = $this->todaysDealsService->getDealsByCategory($category);
            
            if ($categoryDeals->isEmpty()) {
                return $this->error(
                    'NOT_FOUND',
                    'No deals found for this category',
                    null,
                    404
                );
            }
            
            return $this->success($categoryDeals);
        } catch (\Exception $e) {
            Log::error('Error fetching category deals: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch category deals',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a specific deal by ID
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function getDealById(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make(['id' => $id], [
                'id' => 'required|string',
            ]);
            
            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Invalid deal ID',
                    $validator->errors()->messages(),
                    400
                );
            }
            
            $deal = $this->todaysDealsService->getDealById($id);
            
            if (!$deal) {
                return $this->error(
                    'NOT_FOUND',
                    'Deal not found',
                    null,
                    404
                );
            }
            
            return $this->success($deal);
        } catch (\Exception $e) {
            Log::error('Error fetching deal: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch deal',
                $e->getMessage(),
                500
            );
        }
    }
} 