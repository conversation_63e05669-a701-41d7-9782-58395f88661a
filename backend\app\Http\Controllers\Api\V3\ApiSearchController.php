<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SearchController;
use App\Http\Resources\V2\ProductMiniCollection;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Search;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Services\ProductPriceHelper;

class ApiSearchController extends ApiResponse
{
    public function search(Request $request){
        $validator = Validator::make($request->all(), [
            'keyword' => 'nullable|string',
            'query' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        // Accept either 'keyword' or 'query' parameter for consistency
        $keyword = $request->input('keyword', $request->input('query', ''));
        
        // Debug the received keyword
        \Log::info('Search keyword: ' . $keyword);

        $products = Product::with(['stocks', 'product_translations', 'brand', 'category', 'thumbnail']);
        $products = $products->where('published', '1')->where('featured', '1')->where('auction_product', 0)->where('approved', '1');

        if ($keyword && strlen(trim($keyword)) > 0) {
            $products->where(function ($q) use ($keyword) {
                $q->where('name', 'like', '%' . $keyword . '%')
                  ->orWhere('tags', 'like', '%' . $keyword . '%')
                  ->orWhereHas('product_translations', function($query) use ($keyword) {
                      $query->where('name', 'like', '%' . $keyword . '%');
                  })
                  ->orWhereHas('brand', function($query) use ($keyword) {
                      $query->where('name', 'like', '%' . $keyword . '%');
                  });
            });

            // Enhanced prioritization for better match ordering
            $exactMatch = addslashes($keyword);
            $startsWith = addslashes($keyword) . '%';
            $contains = '%' . addslashes($keyword) . '%';

            $products->orderByRaw("CASE
                WHEN name = '$exactMatch' THEN 1
                WHEN name LIKE '$startsWith' THEN 2
                WHEN name LIKE '$contains' THEN 3
                WHEN tags LIKE '$contains' THEN 4
                ELSE 5
                END");
        } else {
            // If no keyword, order by newest
            $products->orderBy('created_at', 'desc');
        }

        $products = $products->take(8)->get();

        $categories = Category::where('name', 'like', '%' . $keyword . '%')
            ->orWhere('slug', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'name', 'slug', 'banner as image', DB::raw('(SELECT COUNT(*) FROM products WHERE category_id = categories.id) as product_count')]);

        $brands = Brand::where('name', 'like', '%' . $keyword . '%')
            ->orWhere('slug', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'name', 'slug', 'logo', DB::raw('(SELECT COUNT(*) FROM products WHERE brand_id = brands.id) as product_count')]);

        $tags = Product::where('tags', 'like', '%' . $keyword . '%')
            ->where('published', '1')
            ->where('approved', '1')
            ->pluck('tags')
            ->filter()
            ->map(function($tags) {
                return explode(',', $tags);
            })
            ->flatten()
            ->unique()
            ->take(10)
            ->values();

        $related_searches = Search::where('query', 'like', '%' . $keyword . '%')
            ->take(10)
            ->get(['id', 'query as text']);

        $popular_searches = Search::query()
            ->orderBy('count', 'desc')
            ->take(5)
            ->get(['id', 'query as text', 'count']);

        // Update search count
        if ($keyword) {
            $search = Search::firstOrCreate(
                ['query' => $keyword],
                ['count' => 0]
            );
            $search->increment('count');
        }

        $formattedProducts = $products->map(function ($product) {
            $prices = ProductPriceHelper::getProductPrices($product);
            return [
                'id' => $product->id,
                'title' => $product->name,
                'slug' => $product->slug,
                'price' => $prices['displayPrice'],
                'regularPrice' => $prices['regularPrice'],
                'has_discount' => $product->discount > 0,
                'discount_percent' => (float)$product->discount,
                'image' => uploaded_asset($product->thumbnail_img),
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                    'slug' => $product->category->slug,
                ] : null,
                'brand' => $product->brand ? [
                    'id' => $product->brand->id,
                    'name' => $product->brand->name,
                    'slug' => $product->brand->slug,
                ] : null,
            ];
        });

        return $this->success([
            'products' => $formattedProducts,
            'categories' => $categories,
            'brands' => $brands,
            'tags' => $tags,
            'related_searches' => $related_searches,
            'popular_searches' => $popular_searches
        ]);
    }
    public function suggestions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $keyword = $request->input('query'); // Fixed: Use input() method to get the query parameter
        $per_page = min((int)$request->input('limit', 10), 50);

        // Get search suggestions
        $searches = Search::where('query', 'like', '%' . $keyword . '%')
            ->orderBy('count', 'desc')
            ->take($per_page)
            ->get(['id', 'query as text'])
            ->map(function($item) {
                return [
                    'id' => 'srch-' . $item->id,
                    'text' => $item->text,
                    'type' => 'search'
                ];
            });



        return $this->success($searches);
    }
    public function popular(Request $request)
    {
        /*$validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
            'limit' => 'nullable|integer|min:1|max:50'
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }*/
        $keyword = $request->input('query'); // Fixed: Use input() method to get the query parameter
        $per_page = min((int)$request->input('limit', 10), 50);

        // Get search suggestions
        $searches = Search::query()//where('query', 'like', '%' . $keyword . '%')
            ->orderBy('count', 'desc')
            ->take($per_page)
            ->get(['id', 'query as text'])
            ->map(function($item) {
                return [
                    'id' => 'srch-' . $item->id,
                    'text' => $item->text,
                    'type' => 'search'
                ];
            });



        return $this->success($searches);
    }

    public function search_products(Request $request) {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort' => 'nullable|string|in:relevance,newest,price_asc,price_desc,popular,rating',
            'category' => 'nullable|string',
            'brand' => 'nullable|string',
            'price_min' => 'nullable|numeric|min:0',
            'price_max' => 'nullable|numeric|min:0',
            'tag' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
            'filters' => 'nullable|json'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $query = $request->input('query');
        $tag = $request->input('tag');
        $page = $request->input('page', 1);
        $per_page = min($request->input('per_page', 20), 100);
        $sort = $request->input('sort', 'relevance');

        $products = Product::with(['stocks', 'product_translations', 'brand', 'category', 'thumbnail'])
            ->where('published', '1')
            ->where('auction_product', 0)
            ->where('approved', '1');
        
        // Apply tag filter
        if ($tag) {
            $products->where(function ($q) use ($tag) {
                $q->where('tags', 'like', '%' . $tag . '%')
                    ->orWhere('tags', 'like', '%' . $tag . ',%')
                    ->orWhere('tags', 'like', '%,' . $tag . '%')
                    ->orWhere('tags', 'like', '%,' . $tag . ',%');
            });
        }
        
        // Apply search query with improved relevance
        if ($query) {
            $products->where(function ($q) use ($query) {
                $words = array_filter(explode(' ', trim($query)));
                foreach ($words as $word) {
                    $searchTerm = '%' . $word . '%';
                    $q->where(function ($subQuery) use ($searchTerm) {
                        $subQuery->where('name', 'like', $searchTerm)
                            ->orWhere('tags', 'like', $searchTerm)
                            ->orWhereHas('brand', function($brandQuery) use ($searchTerm) {
                                $brandQuery->where('name', 'like', $searchTerm);
                            })
                            ->orWhereHas('category', function($catQuery) use ($searchTerm) {
                                $catQuery->where('name', 'like', $searchTerm);
                            });
                    });
                }
            });
            
            // Enhanced relevance scoring for better ordering
            $exactMatch = addslashes($query);
            $startsWith = addslashes($query) . '%';
            $contains = '%' . addslashes($query) . '%';
            
            $products->orderByRaw("CASE
                WHEN name = '$exactMatch' THEN 1
                WHEN name LIKE '$startsWith' THEN 2
                WHEN name LIKE '$contains' THEN 3
                WHEN tags LIKE '$contains' THEN 4
                ELSE 5
                END");
        }

        // Apply category filter
        if ($request->category) {
            $categoryId = is_numeric($request->category)
                ? $request->category
                : Category::where('slug', $request->category)->value('id');
            if ($categoryId) {
                $products->where('category_id', $categoryId);
            }
        }

        // Apply brand filter
        if ($request->brand) {
            $brandId = is_numeric($request->brand)
                ? $request->brand
                : Brand::where('slug', $request->brand)->value('id');
            if ($brandId) {
                $products->where('brand_id', $brandId);
            }
        }

        // Apply price range filter
        if ($request->price_min) {
            $products->where('unit_price', '>=', $request->price_min);
        }
        if ($request->price_max) {
            $products->where('unit_price', '<=', $request->price_max);
        }

        // Apply rating filter
        if ($request->rating) {
            $products->where('rating', '>=', $request->rating);
        }

        // Apply sorting
        switch ($sort) {
            case 'newest':
                $products->orderBy('created_at', 'desc');
                break;
            case 'price_asc':
                $products->orderBy('unit_price', 'asc');
                break;
            case 'price_desc':
                $products->orderBy('unit_price', 'desc');
                break;
            case 'popular':
                $products->orderBy('num_of_sale', 'desc');
                break;
            case 'rating':
                $products->orderBy('rating', 'desc');
                break;
        }

        // Apply additional filters
        if ($request->filters) {
            try {
                $additionalFilters = json_decode($request->filters, true);
                if (is_array($additionalFilters)) {
                    foreach ($additionalFilters as $key => $value) {
                        if (is_array($value)) {
                            $products->whereIn($key, $value);
                        } else {
                            $products->where($key, $value);
                        }
                    }
                }
            } catch (\Exception $e) {
                // Invalid JSON format, ignore additional filters
            }
        }

        $filter_values = array();
        if (true) {
            // Get Unique Categories
            $unique_category_ids = $products->pluck('category_id')->unique()->values();
            $unique_categories = Category::whereIn('id', $unique_category_ids)->get(['id', 'name', 'slug']);

            // Get Unique Brands
            $unique_rating = $products->pluck('rating')->unique()->values();
            $unique_brand_ids = $products->pluck('brand_id')->unique()->values();
            $unique_brands = Brand::whereIn('id', $unique_brand_ids)->get(['id', 'name', 'slug']);

            // Get Min & Max Price
            $min_price = $products->min('unit_price');
            $max_price = $products->max('unit_price');

            // Get Unique Colors
            $unique_colors = $products->pluck('colors')->filter()->flatten()->unique()->values();

            // Get Unique Attributes
            $unique_attributes = $products->pluck('choice_options')->filter()->flatten()->unique()->values();
            $filter_values = [
                'categories' => $unique_categories,
                'brands' => $unique_brands,
                'min_price' => $min_price,
                'max_price' => $max_price,
                'colors' => $unique_colors,
                'attributes' => $unique_attributes,
                'rating' => $unique_rating,
            ];
        }



        // Get paginated results
        //$paginatedProducts = $products->paginate($per_page, ['*'], 'page', $page);
        $paginatedProducts = $products->paginate($per_page);


        // Store search query
      /*  if ($query) {
            $search = Search::firstOrCreate(
                ['query' => $query],
                ['count' => 0]
            );
            $search->increment('count');
        }*/

        return $this->success([
            'products' => new ProductsResource($paginatedProducts),
            'filter_values' => $filter_values,
            'pagination' => [
                'current_page' => $paginatedProducts->currentPage(),
                'total_pages' => $paginatedProducts->lastPage(),
                'total_items' => $paginatedProducts->total(),
                'items_per_page' => $paginatedProducts->perPage(),
            ]
        ]);
    }
}
