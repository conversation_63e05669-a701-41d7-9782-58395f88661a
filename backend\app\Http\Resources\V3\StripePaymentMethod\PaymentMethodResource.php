<?php

namespace App\Http\Resources\V3\StripePaymentMethod;

use App\Models\StripeCard;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class PaymentMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        return [
            'id'=> $this->id,
            'type'=> $this->type,
            'is_default'=> (boolean) $this->is_default,
            'details' =>[
                    'brand'=> $this->card->brand,
                    'last4'=> $this->card->last4,
                    'expiry_month'=> $this->card->exp_month,
                    'exp_year'=> $this->card->exp_year,
                    'cardholder_name'=> $this->card->name,
                ],
            'created_at'=>  $this->created_at != null ? $this->created_at->toIso8601String() : null,
            ];
    }
}
