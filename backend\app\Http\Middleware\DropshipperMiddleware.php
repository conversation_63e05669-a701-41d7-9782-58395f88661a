<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\V3\ApiResponse;

class DropshipperMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if the user is authenticated
        if (!auth()->check()) {
            $apiResponse = new ApiResponse();
            return $apiResponse->unauthenticatedResponse('You need to be logged in to access this resource');
        }

        // Check if the user is a dropshipper
        if (!auth()->user()->isDropshipper()) {
            $apiResponse = new ApiResponse();
            return $apiResponse->notAuthorizedResponse('You need to be a dropshipper to access this resource');
        }

        return $next($request);
    }
} 