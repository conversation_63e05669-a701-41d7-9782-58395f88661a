{"__meta": {"id": "01K1147KP3EBAC8NZBY40KG0R7", "datetime": "2025-07-25 08:46:36", "utime": **********.867941, "method": "GET", "uri": "/buzfi-new-backend/api/v3/banners/hero", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"count": 5, "start": **********.671727, "end": **********.867952, "duration": 0.19622516632080078, "duration_str": "196ms", "measures": [{"label": "Booting", "start": **********.671727, "relative_start": 0, "end": **********.819189, "relative_end": **********.819189, "duration": 0.****************, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.819197, "relative_start": 0.*****************, "end": **********.867954, "relative_end": 1.9073486328125e-06, "duration": 0.048757076263427734, "duration_str": "48.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.82707, "relative_start": 0.*****************, "end": **********.829676, "relative_end": **********.829676, "duration": 0.002605915069580078, "duration_str": "2.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.865779, "relative_start": 0.*****************, "end": **********.865958, "relative_end": **********.865958, "duration": 0.00017905235290527344, "duration_str": "179μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.866862, "relative_start": 0.*****************, "end": **********.866896, "relative_end": **********.866896, "duration": 3.3855438232421875e-05, "duration_str": "34μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.2.29", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost/", "Timezone": "America/Phoenix", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/banners/hero", "action_name": "api.v3.banners.hero", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners", "uri": "GET api/v3/banners/hero", "controller": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=25\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/v3", "file": "<a href=\"phpstorm://open?file=D%3A%2FDevelopment%2Flaragon%2Fwww%2Fbuzfi-new-backend%2Fapp%2FHttp%2FControllers%2FApi%2FV3%2FApiHomePageController.php&line=25\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/V3/ApiHomePageController.php:25-77</a>", "middleware": "api", "duration": "196ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1503576794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1503576794\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1186528320 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1186528320\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1502859755 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">public, max-age=600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-US,en;q=0.9,de-BE;q=0.8,de;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"615 characters\">pma_lang=en; __stripe_mid=d3e10378-1c3f-49e2-b06d-7ed944beed25e40f04; __stripe_sid=53f38e04-bc62-4f2f-b30c-96e218791aa0d605f0; __next_hmr_refresh_hash__=c19fe2b18bb75e416c964220cfcc23acd0535e0932f1c781; XSRF-TOKEN=eyJpdiI6InVIdGoxV2UzRFk5SU5LVGp6OGU1MGc9PSIsInZhbHVlIjoielRVOFE3eXJTdEliYnN1dkEydUFicGdKR0dJQ0dtTzBXZDErcFFVMEllOFVaWTFldkJTKytCTHo1TVYyTkdlL2RLeGFzbysrdEFzdWZwbHZhd3B5Ui9keG5zNFpxeHZ0OHloNXdGWlVCYmJoUThCVjJwc2N3enQ5dkQwZFpLWWoiLCJtYWMiOiI0NGE1YWY3MzcxY2EwOTE0NTBhNWE3ZjJhZTdlNmJmZTk5MGRkYmU4OGIzYmJjZjZkZGQ3MWZlZjE5ZTMzMWFiIiwidGFnIjoiIn0%3D; buzficom_session=hySJgNH979p6fQWWD1f2qpKXnIG9qkvpxk8GqviS</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502859755\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1439833150 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>pma_lang</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_sid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__next_hmr_refresh_hash__</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>buzficom_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439833150\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389292460 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">max-age=2592000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 15:46:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">600</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">589</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">GET, POST, PUT, DELETE, OPTIONS, PATCH</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"159 characters\">Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-credentials</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-max-age</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">86400</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-expose-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-security-policy</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"852 characters\">default-src &#039;self&#039;; script-src &#039;self&#039; &#039;unsafe-inline&#039; &#039;unsafe-eval&#039; https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src &#039;self&#039; &#039;unsafe-inline&#039; https://fonts.googleapis.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; img-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; font-src &#039;self&#039; https://fonts.gstatic.com; media-src &#039;self&#039; data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; connect-src &#039;self&#039; https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com; frame-src https://js.stripe.com https://*.stripe.com; object-src &#039;none&#039;;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjdXNWYwVkt6S0tCb3RTM3Z2Nk4zQkE9PSIsInZhbHVlIjoiVS9IZTRPK2FZTUZTQm1EbjIrUWluQWtMWVJuRDdLK2lxY3VBVi9LSm5wV1lUUTQxMFBpZUpFcitmb3luRkpnbTZNdnl0bEo4RmsxS0w3S3l6aENtRzZHclh3NG0wUVR2Z011N0tacjQxZ25LLzFFTHFyRVNmYmRROXJRR2Z5VmsiLCJtYWMiOiI1MjUwZjBjN2NjZThhZDkyZWMyNDNjNDdmMTlmYWFkNmNjM2FhYWI0NGRjMjc5MTllMzljMWFiMjI0MjJkODBhIiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"444 characters\">buzficom_session=eyJpdiI6Ikp2MXh4azV2d2hpUTBvYUl2TE14TUE9PSIsInZhbHVlIjoieXhUT3ZHMDgrQUpxUjZuZFBjOENzOTh2bWJNUVV0NXg5WSt2NFpZYmRqS2F5bGVGQVpUTytRbGJQNlVuYXc4bCtnTWVlNU9oY0RoaVozYkFQTHdUalVBL2VUOFh2MzU2RCtMNWhXYjd6aU5jR252Tjc2OTBrTGtRZUZmTzlQTG4iLCJtYWMiOiI3NjNiMTFhZDQwM2FiNjBhYzkyY2RkN2MxNTI2YjZhM2IxOWU4NGY0MjQzZmYzODhlYmJkOWMyOGUwZDJiNTk0IiwidGFnIjoiIn0%3D; expires=Fri, 25 Jul 2025 17:46:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjdXNWYwVkt6S0tCb3RTM3Z2Nk4zQkE9PSIsInZhbHVlIjoiVS9IZTRPK2FZTUZTQm1EbjIrUWluQWtMWVJuRDdLK2lxY3VBVi9LSm5wV1lUUTQxMFBpZUpFcitmb3luRkpnbTZNdnl0bEo4RmsxS0w3S3l6aENtRzZHclh3NG0wUVR2Z011N0tacjQxZ25LLzFFTHFyRVNmYmRROXJRR2Z5VmsiLCJtYWMiOiI1MjUwZjBjN2NjZThhZDkyZWMyNDNjNDdmMTlmYWFkNmNjM2FhYWI0NGRjMjc5MTllMzljMWFiMjI0MjJkODBhIiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">buzficom_session=eyJpdiI6Ikp2MXh4azV2d2hpUTBvYUl2TE14TUE9PSIsInZhbHVlIjoieXhUT3ZHMDgrQUpxUjZuZFBjOENzOTh2bWJNUVV0NXg5WSt2NFpZYmRqS2F5bGVGQVpUTytRbGJQNlVuYXc4bCtnTWVlNU9oY0RoaVozYkFQTHdUalVBL2VUOFh2MzU2RCtMNWhXYjd6aU5jR252Tjc2OTBrTGtRZUZmTzlQTG4iLCJtYWMiOiI3NjNiMTFhZDQwM2FiNjBhYzkyY2RkN2MxNTI2YjZhM2IxOWU4NGY0MjQzZmYzODhlYmJkOWMyOGUwZDJiNTk0IiwidGFnIjoiIn0%3D; expires=Fri, 25-Jul-2025 17:46:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389292460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-520109228 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SIVbtQL7OzMOak1VWwq2q8flEd1r24oML0tpqsn0</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"54 characters\">http://localhost/buzfi-new-backend/api/v3/banners/hero</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/buzfi-new-backend/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520109228\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/buzfi-new-backend/api/v3/banners/hero", "action_name": "api.v3.banners.hero", "controller_action": "App\\Http\\Controllers\\Api\\V3\\ApiHomePageController@hero_banners"}, "badge": null}}