<?php

namespace App\Http\Resources\V3\ReturnMethods;

use Illuminate\Http\Resources\Json\JsonResource;

class ReturnMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'processingTime' => $this->processing_time,
            'isDefault' => (bool) $this->is_default,
            'restrictions' => $this->restrictions,
            'fee' => (float) $this->fee
        ];
    }
}
