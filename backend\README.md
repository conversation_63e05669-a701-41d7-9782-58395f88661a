## Technology and tools
<pre>
-> php 8.0
-> MySql
-> laravel 10
-> postman 
-> 
</pre>

# References

> [TIP]  
> **N.B:** Please enable **;extension=gd**  and **;extension=zip** extension if your are using xampp

> [DOCKER]  
> **N.B:**


> [WARNING]  
> **N.B:** 

> [CAUTION]  
> **N.B:** 


## Project Setup
<pre>1. Clone The git repository</pre><br>
<pre>2. Rename The .env file.<pre>cp .env.example .env</pre></pre><br>
<pre>3.create a database and set the database details in the .env file.There is a sql file in sqlDatabase folder <br></pre>
<pre>4.Run <pre>composer update</pre></pre>
<pre>5.Run <pre>php artisan key:generate</pre></pre>


## Large DB Import
<pre>
1.Go to xampp> mysql > bin folder
2.Open cmd (write cmd in the foldet address bar and press enter)
3.run this comment
    mysql -u root -p lifemecu_lpbd_12_04_2023  < C:\import2.sql

here lifemecu_lpbd_12_04_2023 is the database name and import2.sql is the 

import file name which is placed in C folder
</pre>



