<?php

namespace App\Events;

use App\Models\Order;
use App\Models\UserNotification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdateEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $previousStatus;
    public $newStatus;
    public $notification;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Order $order, $previousStatus, $newStatus, UserNotification $notification = null)
    {
        $this->order = $order;
        $this->previousStatus = $previousStatus;
        $this->newStatus = $newStatus;
        $this->notification = $notification;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return [
            new PrivateChannel('notifications.' . $this->order->user_id),
            new PrivateChannel('order.updates.' . $this->order->id)
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'order.status.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $data = [
            'order_id' => $this->order->id,
            'order_code' => $this->order->code ?? $this->order->id,
            'user_id' => $this->order->user_id,
            'previous_status' => $this->previousStatus,
            'new_status' => $this->newStatus,
            'timestamp' => now()->toIso8601String(),
        ];

        if ($this->notification) {
            $data['notification'] = [
                'id' => $this->notification->id,
                'type' => $this->notification->type,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'date' => $this->notification->created_at->toIso8601String(),
                'read' => (bool) $this->notification->read,
                'priority' => $this->notification->priority,
                'link' => $this->notification->link,
                'linkText' => $this->notification->link_text,
            ];
        }

        return $data;
    }
} 