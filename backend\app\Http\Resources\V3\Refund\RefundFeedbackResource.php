<?php

namespace App\Http\Resources\V3\Refund;

use Illuminate\Http\Resources\Json\JsonResource;

class RefundFeedbackResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'returnId' => $this['returnId'],
            'feedbackId' => $this['feedbackId'],
            'message' => $this['message'],
            'submittedAt' => $this['submittedAt'],
            'rewardPoints' => $this['rewardPoints'],
            'totalRewardPoints' => $this['totalRewardPoints']
        ];
    }
}
