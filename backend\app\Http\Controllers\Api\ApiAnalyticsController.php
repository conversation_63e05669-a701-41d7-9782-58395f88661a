<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Review;
use App\Models\Wishlist;
use Illuminate\Http\Request;

class ApiAnalyticsController extends Controller
{
    /**
     * Get customer analytics data
     */
    public function getCustomerAnalyticsData(Request $request)
    {
        try {
            $timeframe = $request->query('timeframe', 'month');
            $user = auth()->user();

            // Get orders for the specified timeframe
            $orders = Order::where('user_id', $user->id)
                ->when($timeframe === 'month', function ($query) {
                    return $query->whereMonth('created_at', now()->month);
                })
                ->when($timeframe === 'year', function ($query) {
                    return $query->whereYear('created_at', now()->year);
                })
                ->when($timeframe === 'all', function ($query) {
                    return $query;
                })
                ->get();

            // Calculate summary data
            $totalSpent = $orders->sum('total_amount');
            $totalOrders = $orders->count();
            $averageOrder = $totalOrders > 0 ? $totalSpent / $totalOrders : 0;

            // Get previous period data for comparison
            $previousOrders = Order::where('user_id', $user->id)
                ->when($timeframe === 'month', function ($query) {
                    return $query->whereMonth('created_at', now()->subMonth()->month);
                })
                ->when($timeframe === 'year', function ($query) {
                    return $query->whereYear('created_at', now()->subYear()->year);
                })
                ->get();

            $previousTotalSpent = $previousOrders->sum('total_amount');
            $previousTotalOrders = $previousOrders->count();
            $previousAverageOrder = $previousTotalOrders > 0 ? $previousTotalSpent / $previousTotalOrders : 0;

            // Calculate changes
            $spentChange = $previousTotalSpent > 0 
                ? (($totalSpent - $previousTotalSpent) / $previousTotalSpent) * 100 
                : 0;
            $ordersChange = $previousTotalOrders > 0 
                ? (($totalOrders - $previousTotalOrders) / $previousTotalOrders) * 100 
                : 0;
            $avgOrderChange = $previousAverageOrder > 0 
                ? (($averageOrder - $previousAverageOrder) / $previousAverageOrder) * 100 
                : 0;

            // Calculate purchase streak
            $streak = $this->calculatePurchaseStreak($user->id);

            // Get spending over time
            $spendingOverTime = $this->getSpendingOverTime($user->id, $timeframe);

            // Get recent orders
            $recentOrders = Order::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get()
                ->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'date' => $order->created_at->format('Y-m-d'),
                        'status' => $order->status,
                        'amount' => $order->total_amount
                    ];
                });

            // Get spending by category
            $spendingByCategory = $this->getSpendingByCategory($user->id, $timeframe);

            return response()->json([
                'summary' => [
                    'totalSpent' => $totalSpent,
                    'spentChange' => round($spentChange, 2),
                    'totalOrders' => $totalOrders,
                    'ordersChange' => round($ordersChange, 2),
                    'averageOrder' => $averageOrder,
                    'avgOrderChange' => round($avgOrderChange, 2),
                    'streak' => $streak
                ],
                'spendingOverTime' => $spendingOverTime,
                'recentOrders' => $recentOrders,
                'spendingByCategory' => $spendingByCategory
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get customer activity timeline
     */
    public function getCustomerActivity(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Get recent activities from orders, reviews, and wishlist
            $activities = collect();

            // Add orders
            $orders = Order::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get()
                ->map(function ($order) {
                    return [
                        'id' => 'order_' . $order->id,
                        'type' => 'purchase',
                        'date' => $order->created_at->format('Y-m-d H:i:s'),
                        'description' => "Ordered {$order->items_count} items",
                        'amount' => $order->total_amount,
                        'status' => $order->status
                    ];
                });

            // Add reviews
            $reviews = Review::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get()
                ->map(function ($review) {
                    return [
                        'id' => 'review_' . $review->id,
                        'type' => 'review',
                        'date' => $review->created_at->format('Y-m-d H:i:s'),
                        'description' => "Reviewed {$review->product->name}",
                    ];
                });

            // Add wishlist items
            $wishlist = Wishlist::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => 'wishlist_' . $item->id,
                        'type' => 'wishlist',
                        'date' => $item->created_at->format('Y-m-d H:i:s'),
                        'description' => "Added {$item->product->name} to wishlist",
                    ];
                });

            // Combine and sort all activities
            $activities = $orders->concat($reviews)->concat($wishlist)
                ->sortByDesc('date')
                ->take(20)
                ->values();

            return response()->json(['activities' => $activities]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get customer spending trends
     */
    public function getCustomerSpendingTrend(Request $request)
    {
        try {
            $period = $request->query('period', 'year');
            $interval = $request->query('interval', 'month');
            $user = auth()->user();

            // Get orders for the specified period
            $orders = Order::where('user_id', $user->id)
                ->when($period === 'year', function ($query) {
                    return $query->whereYear('created_at', now()->year);
                })
                ->when($period === 'month', function ($query) {
                    return $query->whereMonth('created_at', now()->month);
                })
                ->get();

            // Calculate trends based on interval
            $trends = $this->calculateTrends($orders, $interval);

            // Get category breakdown
            $categories = $this->getCategoryBreakdown($orders);

            return response()->json([
                'trends' => $trends,
                'categories' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Calculate purchase streak
     */
    private function calculatePurchaseStreak($userId)
    {
        $streak = 0;
        $currentDate = now();
        
        while (true) {
            $hasOrder = Order::where('user_id', $userId)
                ->whereDate('created_at', $currentDate->format('Y-m-d'))
                ->exists();
            
            if (!$hasOrder) {
                break;
            }
            
            $streak++;
            $currentDate->subDay();
        }
        
        return $streak;
    }

    /**
     * Get spending over time
     */
    private function getSpendingOverTime($userId, $timeframe)
    {
        $query = Order::where('user_id', $userId);
        
        if ($timeframe === 'month') {
            $query->whereMonth('created_at', now()->month);
        } elseif ($timeframe === 'year') {
            $query->whereYear('created_at', now()->year);
        }
        
        return $query->get()
            ->groupBy(function ($order) {
                return $order->created_at->format('Y-m-d');
            })
            ->map(function ($orders) {
                return [
                    'date' => $orders->first()->created_at->format('Y-m-d'),
                    'amount' => $orders->sum('total_amount')
                ];
            })
            ->values();
    }

    /**
     * Get spending by category
     */
    private function getSpendingByCategory($userId, $timeframe)
    {
        $query = Order::where('user_id', $userId)
            ->with('items.product.category');
        
        if ($timeframe === 'month') {
            $query->whereMonth('created_at', now()->month);
        } elseif ($timeframe === 'year') {
            $query->whereYear('created_at', now()->year);
        }
        
        $orders = $query->get();
        
        $categorySpending = [];
        
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $category = $item->product->category;
                if (!isset($categorySpending[$category->id])) {
                    $categorySpending[$category->id] = [
                        'name' => $category->name,
                        'value' => 0
                    ];
                }
                $categorySpending[$category->id]['value'] += $item->price * $item->quantity;
            }
        }
        
        return array_values($categorySpending);
    }

    /**
     * Calculate trends based on interval
     */
    private function calculateTrends($orders, $interval)
    {
        $trends = [];
        $groupedOrders = $orders->groupBy(function ($order) use ($interval) {
            if ($interval === 'month') {
                return $order->created_at->format('Y-m');
            } elseif ($interval === 'week') {
                return $order->created_at->format('Y-W');
            } else {
                return $order->created_at->format('Y-m-d');
            }
        });

        foreach ($groupedOrders as $period => $periodOrders) {
            $amount = $periodOrders->sum('total_amount');
            $previousPeriod = $this->getPreviousPeriod($period, $interval);
            $previousAmount = $this->getPreviousPeriodAmount($orders, $previousPeriod, $interval);
            
            $change = $previousAmount > 0 
                ? (($amount - $previousAmount) / $previousAmount) * 100 
                : 0;

            $trends[] = [
                'period' => $period,
                'amount' => $amount,
                'change' => round($change, 2)
            ];
        }

        return $trends;
    }

    /**
     * Get previous period
     */
    private function getPreviousPeriod($period, $interval)
    {
        if ($interval === 'month') {
            return date('Y-m', strtotime($period . '-01 -1 month'));
        } elseif ($interval === 'week') {
            return date('Y-W', strtotime($period . '-1 week'));
        } else {
            return date('Y-m-d', strtotime($period . '-1 day'));
        }
    }

    /**
     * Get previous period amount
     */
    private function getPreviousPeriodAmount($orders, $previousPeriod, $interval)
    {
        return $orders->filter(function ($order) use ($previousPeriod, $interval) {
            if ($interval === 'month') {
                return $order->created_at->format('Y-m') === $previousPeriod;
            } elseif ($interval === 'week') {
                return $order->created_at->format('Y-W') === $previousPeriod;
            } else {
                return $order->created_at->format('Y-m-d') === $previousPeriod;
            }
        })->sum('total_amount');
    }

    /**
     * Get category breakdown
     */
    private function getCategoryBreakdown($orders)
    {
        $categoryTotals = [];
        $totalSpent = $orders->sum('total_amount');

        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $category = $item->product->category;
                if (!isset($categoryTotals[$category->id])) {
                    $categoryTotals[$category->id] = [
                        'name' => $category->name,
                        'amount' => 0,
                        'percentage' => 0
                    ];
                }
                $categoryTotals[$category->id]['amount'] += $item->price * $item->quantity;
            }
        }

        // Calculate percentages
        foreach ($categoryTotals as &$category) {
            $category['percentage'] = $totalSpent > 0 
                ? round(($category['amount'] / $totalSpent) * 100, 2) 
                : 0;
        }

        return array_values($categoryTotals);
    }
} 