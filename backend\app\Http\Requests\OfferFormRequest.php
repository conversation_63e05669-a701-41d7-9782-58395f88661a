<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OfferFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'offer_banner' => 'nullable|string',
            'promo_code' => 'required|string|max:50|unique:offers,promo_code',
            'user_type' => 'required|in:all,customer,seller,dropshipper',
            'discount_type' => 'required|in:percentage,amount',
            'discount_percentage' => 'required_if:discount_type,percentage|nullable|numeric|min:0|max:100',
            'discount_amount' => 'required_if:discount_type,amount|nullable|numeric|min:0',
            'min_order_value' => 'nullable|numeric|min:0',
            'priority' => 'nullable|integer|min:0|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'usage_limit' => 'nullable|integer|min:0',
            'savings_amount' => 'nullable|numeric|min:0',
            'terms_conditions' => 'nullable|string',
            'tags' => 'nullable|string',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
            'products' => 'nullable|array',
            'products.*' => 'exists:products,id',
            'is_exclusive' => 'nullable|boolean',
            'is_seasonal' => 'nullable|boolean',
            'is_personalized' => 'nullable|boolean',
            'is_dropshipper_only' => 'nullable|boolean',
            'bulk_discount_quantities' => 'nullable|array',
            'bulk_discount_quantities.*' => 'nullable|integer|min:1',
            'bulk_discount_percentages' => 'nullable|array',
            'bulk_discount_percentages.*' => 'nullable|numeric|min:0|max:100',
        ];

        // If updating, make promo_code unique except for current record
        if ($this->route('offer') || $this->route('id')) {
            $offerId = $this->route('offer') ?? $this->route('id');
            $rules['promo_code'] = 'required|string|max:50|unique:offers,promo_code,' . $offerId;
            // For updates, start_date doesn't need to be after today
            $rules['start_date'] = 'required|date';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'title.required' => 'The offer title is required.',
            'description.required' => 'The offer description is required.',
            'promo_code.required' => 'The promo code is required.',
            'promo_code.unique' => 'This promo code is already in use.',
            'user_type.required' => 'The user type is required.',
            'user_type.in' => 'The selected user type is invalid.',
            'discount_type.required' => 'The discount type is required.',
            'discount_type.in' => 'The selected discount type is invalid.',
            'discount_percentage.required_if' => 'The discount percentage is required when discount type is percentage.',
            'discount_percentage.min' => 'The discount percentage must be at least 0.',
            'discount_percentage.max' => 'The discount percentage cannot exceed 100%.',
            'discount_amount.required_if' => 'The discount amount is required when discount type is amount.',
            'discount_amount.min' => 'The discount amount must be at least 0.',
            'min_order_value.min' => 'Minimum order value must be at least 0.',
            'priority.min' => 'Priority must be at least 0.',
            'priority.max' => 'Priority cannot exceed 100.',
            'start_date.required' => 'The start date is required.',
            'start_date.after_or_equal' => 'The start date must be today or later.',
            'end_date.required' => 'The end date is required.',
            'end_date.after' => 'The end date must be after the start date.',
            'usage_limit.min' => 'Usage limit must be at least 0.',
            'savings_amount.min' => 'Savings amount must be at least 0.',
            'categories.array' => 'Categories must be an array.',
            'categories.*.exists' => 'One or more selected categories do not exist.',
            'products.array' => 'Products must be an array.',
            'products.*.exists' => 'One or more selected products do not exist.',
            'bulk_discount_quantities.array' => 'Bulk discount quantities must be an array.',
            'bulk_discount_quantities.*.min' => 'Bulk discount quantity must be at least 1.',
            'bulk_discount_percentages.array' => 'Bulk discount percentages must be an array.',
            'bulk_discount_percentages.*.min' => 'Bulk discount percentage must be at least 0.',
            'bulk_discount_percentages.*.max' => 'Bulk discount percentage cannot exceed 100.',
            'is_exclusive.boolean' => 'The exclusive offer field must be true or false.',
            'is_seasonal.boolean' => 'The seasonal offer field must be true or false.',
            'is_personalized.boolean' => 'The personalized offer field must be true or false.',
            'is_dropshipper_only.boolean' => 'The dropshipper only offer field must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'title' => 'offer title',
            'description' => 'description',
            'promo_code' => 'promo code',
            'user_type' => 'user type',
            'discount_type' => 'discount type',
            'discount_percentage' => 'discount percentage',
            'discount_amount' => 'discount amount',
            'min_order_value' => 'minimum order value',
            'priority' => 'priority',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'usage_limit' => 'usage limit',
            'savings_amount' => 'savings amount',
            'terms_conditions' => 'terms and conditions',
            'tags' => 'tags',
            'categories' => 'categories',
            'products' => 'products',
            'bulk_discount_quantities' => 'bulk discount quantities',
            'bulk_discount_percentages' => 'bulk discount percentages',
        ];
    }
} 