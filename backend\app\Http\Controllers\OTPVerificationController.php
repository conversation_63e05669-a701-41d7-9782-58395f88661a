<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\OtpConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Utility\SmsUtility;
use Carbon\Carbon;

class OTPVerificationController extends Controller
{
    /**
     * Show phone verification form
     */
    public function verification()
    {
        if (Auth::check() && Auth::user()->email_verified_at != null) {
            return redirect()->route('home');
        }
        
        return view('auth.verification');
    }

    /**
     * Verify phone number with OTP code
     */
    public function verify_phone(Request $request)
    {
        $request->validate([
            'verification_code' => 'required|numeric'
        ]);

        $user = Auth::user();
        
        if ($user->verification_code == $request->verification_code) {
            $user->email_verified_at = Carbon::now();
            $user->verification_code = null;
            $user->save();

            flash(translate('Your phone number has been verified successfully'))->success();
            
            if (session()->has('link')) {
                return redirect(session('link'));
            } else {
                return redirect()->route('home');
            }
        } else {
            flash(translate('Invalid verification code'))->error();
            return redirect()->back();
        }
    }

    /**
     * Resend verification code
     */
    public function resend_verificcation_code()
    {
        $user = Auth::user();
        $user->verification_code = rand(100000, 999999);
        $user->save();

        $this->send_code($user);

        flash(translate('Verification code has been sent again'))->success();
        return redirect()->back();
    }

    /**
     * Show password reset form
     */
    public function show_reset_password_form()
    {
        return view('auth.passwords.reset_phone');
    }

    /**
     * Reset password with OTP code
     */
    public function reset_password_with_code(Request $request)
    {
        $request->validate([
            'phone' => 'required',
            'verification_code' => 'required|numeric',
            'password' => 'required|min:6|confirmed'
        ]);

        $user = User::where('phone', $request->phone)->first();
        
        if (!$user) {
            flash(translate('Phone number not found'))->error();
            return redirect()->back();
        }

        if ($user->verification_code == $request->verification_code) {
            $user->password = Hash::make($request->password);
            $user->verification_code = null;
            $user->save();

            flash(translate('Password has been reset successfully'))->success();
            return redirect()->route('login');
        } else {
            flash(translate('Invalid verification code'))->error();
            return redirect()->back();
        }
    }

    /**
     * Send OTP code to user
     */
    public function send_code($user)
    {
        try {
            // Check if OTP system is enabled
            if (get_setting('otp_system') != 1) {
                return false;
            }

            $verification_code = $user->verification_code ?? rand(100000, 999999);
            
            if (!$user->verification_code) {
                $user->verification_code = $verification_code;
                $user->save();
            }

            // Send SMS
            if (!empty($user->phone)) {
                $message = translate('Your verification code is: ') . $verification_code;
                SmsUtility::send_sms($user->phone, $message);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('OTP Send Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send password reset OTP
     */
    public function send_password_reset_code($phone)
    {
        try {
            $user = User::where('phone', $phone)->first();
            
            if (!$user) {
                return false;
            }

            $user->verification_code = rand(100000, 999999);
            $user->save();

            $message = translate('Your password reset code is: ') . $user->verification_code;
            SmsUtility::send_sms($phone, $message);
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Password Reset OTP Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify OTP code for API
     */
    public function verify_code($user, $code)
    {
        if ($user->verification_code == $code) {
            $user->email_verified_at = Carbon::now();
            $user->verification_code = null;
            $user->save();
            return true;
        }
        return false;
    }

    /**
     * Generate and send new verification code
     */
    public function generate_and_send_code($user)
    {
        $user->verification_code = rand(100000, 999999);
        $user->save();
        
        return $this->send_code($user);
    }
} 