<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\ReturnKnowledgeBase;
use App\Models\ReturnProcessStep;
use App\Models\ReturnImportantNote;
use Illuminate\Http\JsonResponse;

class ReturnKnowledgeBaseController extends Controller
{
    public function index(): JsonResponse
    {
        $knowledgeBase = ReturnKnowledgeBase::query()
            ->with(['processSteps', 'importantNotes'])
            ->first();

        if (!$knowledgeBase) {
            return response()->json([
                'success' => true,
                'data' => [
                    'process_steps' => [],
                    'important_notes' => [],
                ],
            ]);
        }

        $processSteps = $knowledgeBase->processSteps()
            ->orderBy('order')
            ->get();

        $importantNotes = $knowledgeBase->importantNotes()
            ->orderBy('order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'process_steps' => $processSteps,
                'important_notes' => $importantNotes,
            ],
        ]);
    }
} 