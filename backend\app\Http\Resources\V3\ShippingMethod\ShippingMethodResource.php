<?php

namespace App\Http\Resources\V3\ShippingMethod;

use Illuminate\Http\Resources\Json\JsonResource;

class ShippingMethodResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Calculate estimated delivery dates
        $minDate = now()->addDays($this->min_days)->format('Y-m-d');
        $maxDate = now()->addDays($this->max_days)->format('Y-m-d');

        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => (float) $this->price,
            'estimatedDelivery' => [
                'minDays' => $this->min_days,
                'maxDays' => $this->max_days,
                'minDate' => $minDate,
                'maxDate' => $maxDate
            ],
            'isDefault' => (bool) $this->is_default
        ];
    }
}
