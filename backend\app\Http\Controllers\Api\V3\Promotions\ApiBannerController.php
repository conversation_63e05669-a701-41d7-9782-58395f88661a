<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Banner\BannerResource;
use App\Models\Promotions\Banner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiBannerController extends ApiResponse
{
    /**
     * Get banners by position
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBannersByPosition(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'position' => 'required|string|max:50',
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $position = $request->input('position');
            $limit = $request->input('limit', 5);
            
            $banners = Banner::active()
                ->position($position)
                ->orderBy('priority', 'desc')
                ->limit($limit)
                ->get();
                
            return $this->success(BannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get banners by page location
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBannersByPageLocation(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page_location' => 'required|string|max:50',
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $pageLocation = $request->input('page_location');
            $limit = $request->input('limit', 5);
            
            $banners = Banner::active()
                ->pageLocation($pageLocation)
                ->orderBy('priority', 'desc')
                ->limit($limit)
                ->get();
                
            return $this->success(BannerResource::collection($banners));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get banner by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getBanner(int $id): JsonResponse
    {
        try {
            $banner = Banner::find($id);
            
            if (!$banner) {
                return $this->error('Banner not found', 'The requested banner does not exist', 404);
            }
            
            // Load campaign if exists
            if ($banner->campaign_id) {
                $banner->load('campaign');
            }
            
            return $this->success(new BannerResource($banner));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Record a banner impression
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recordImpression(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'banner_id' => 'required|integer|exists:banners,id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $bannerId = $request->input('banner_id');
            
            $banner = Banner::find($bannerId);
            $banner->recordImpression();
            
            return $this->success(['message' => 'Impression recorded successfully']);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Record a banner click
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recordClick(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'banner_id' => 'required|integer|exists:banners,id'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $bannerId = $request->input('banner_id');
            
            $banner = Banner::find($bannerId);
            $banner->recordClick();
            
            return $this->success(['message' => 'Click recorded successfully']);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 