<?php

namespace App\Http\Controllers\Api\V3\Pusher;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Order;
use App\Models\OrderTracking;
use Pusher\Pusher;

class ApiRealTimeTrackingController extends Controller
{
    /**
     * Get real-time tracking data for an order
     *
     * @param Request $request
     * @param int $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrderTracking(Request $request, $orderId)
    {
        $user = Auth::user();

        // Retrieve the order and ensure it belongs to the authenticated user
        $order = Order::where('id', $orderId)
            ->where('user_id', $user->id)
            ->first();

        if (!$order) {
            return response()->json([
                'status' => false,
                'message' => 'Order not found or you do not have permission to access it'
            ], 404);
        }

        // Get all tracking events for the order
        $trackingEvents = OrderTracking::where('order_id', $orderId)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get the latest tracking information
        $trackingInfo = [
            'order_code' => $order->code,
            'current_status' => $order->delivery_status,
            'estimated_delivery' => $order->estimated_delivery_date,
            'events' => $trackingEvents->map(function($event) {
                return [
                    'status' => $event->status,
                    'location' => $event->location,
                    'message' => $event->message,
                    'timestamp' => $event->created_at->toIso8601String(),
                    'details' => json_decode($event->details)
                ];
            })
        ];

        // Set up Pusher channel for real-time updates
        $channelName = 'private-order-tracking-' . $orderId;
        $eventName = 'tracking-update';

        $appId = config('broadcasting.connections.pusher.app_id');
        $appKey = config('broadcasting.connections.pusher.key');
        $appSecret = config('broadcasting.connections.pusher.secret');
        $options = [
            'cluster' => config('broadcasting.connections.pusher.options.cluster'),
            'useTLS' => true
        ];
        
        // Include Pusher channel information in the response
        $pusherInfo = [
            'channel' => $channelName,
            'event' => $eventName,
            'key' => $appKey,
            'cluster' => $options['cluster']
        ];

        return response()->json([
            'status' => true,
            'message' => 'Tracking information retrieved successfully',
            'data' => [
                'tracking' => $trackingInfo,
                'pusher' => $pusherInfo
            ]
        ]);
    }

    /**
     * Update tracking information for an order (admin/seller endpoint)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateOrderTracking(Request $request)
    {
        $request->validate([
            'order_id' => 'required|integer|exists:orders,id',
            'status' => 'required|string',
            'location' => 'required|string',
            'message' => 'required|string',
            'details' => 'nullable|json'
        ]);

        try {
            // Create a new tracking event
            $trackingEvent = new OrderTracking;
            $trackingEvent->order_id = $request->order_id;
            $trackingEvent->status = $request->status;
            $trackingEvent->location = $request->location;
            $trackingEvent->message = $request->message;
            $trackingEvent->details = $request->details ?? "{}";
            $trackingEvent->save();

            // Update the order status
            $order = Order::find($request->order_id);
            $order->delivery_status = $request->status;
            $order->save();

            // Prepare the data to be sent via Pusher
            $trackingData = [
                'status' => $trackingEvent->status,
                'location' => $trackingEvent->location,
                'message' => $trackingEvent->message,
                'timestamp' => $trackingEvent->created_at->toIso8601String(),
                'details' => json_decode($trackingEvent->details)
            ];

            // Send real-time update using Pusher
            $this->sendTrackingUpdate($request->order_id, $trackingData);

            return response()->json([
                'status' => true,
                'message' => 'Tracking information updated successfully',
                'data' => $trackingData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update tracking information: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a real-time tracking update via Pusher
     *
     * @param int $orderId
     * @param array $trackingData
     * @return void
     */
    private function sendTrackingUpdate($orderId, $trackingData)
    {
        try {
            // Get Pusher credentials from config
            $appId = config('broadcasting.connections.pusher.app_id');
            $appKey = config('broadcasting.connections.pusher.key');
            $appSecret = config('broadcasting.connections.pusher.secret');
            $options = [
                'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                'useTLS' => true
            ];
            
            // Create Pusher instance
            $pusher = new Pusher($appKey, $appSecret, $appId, $options);
            
            // Channel name for the specific order
            $channelName = 'private-order-tracking-' . $orderId;
            
            // Trigger event on channel
            $pusher->trigger(
                $channelName,
                'tracking-update',
                $trackingData
            );
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::error('Failed to send Pusher notification: ' . $e->getMessage());
        }
    }
} 