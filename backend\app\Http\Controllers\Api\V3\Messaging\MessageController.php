<?php

namespace App\Http\Controllers\Api\V3\Messaging;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Messaging\MessageThread;
use App\Models\Messaging\Message;
use App\Models\Messaging\MessageAttachment;
use App\Events\Messaging\MessageSent;
use App\Events\Messaging\ThreadUpdated;
use App\Events\Messaging\UnreadCountUpdated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class MessageController extends Controller
{
    /**
     * Send a message to a thread
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $threadId
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, $threadId)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find thread and check if user is a participant
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($threadId);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Check if thread is closed
        if ($thread->is_closed) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot send messages to a closed thread',
            ], 403);
        }
        
        DB::beginTransaction();
        
        try {
            // Create message
            $message = $thread->messages()->create([
                'sender_id' => $user->id,
                'content' => $request->content,
            ]);
            
            // Handle attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('message-attachments', 'public');
                    $url = Storage::url($path);
                    
                    $message->attachments()->create([
                        'name' => $file->getClientOriginalName(),
                        'url' => $url,
                        'type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                    ]);
                }
            }
            
            // Mark as read by sender
            $message->markAsReadBy($user->id);
            
            // Update thread updated_at timestamp
            $thread->touch();
            
            DB::commit();
            
            // Load relationships for response
            $message->load(['sender', 'attachments']);
            
            // Broadcast events
            broadcast(new MessageSent($message))->toOthers();
            broadcast(new ThreadUpdated($thread))->toOthers();
            
            // Update unread counts for all participants except sender
            $this->broadcastUnreadCountUpdates($thread->participants, [$user->id]);
            
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message,
            ], 201);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get messages for a thread with pagination
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $threadId
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, $threadId)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($threadId);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Query parameters
        $perPage = $request->input('perPage', 50);
        $page = $request->input('page', 1);
        $sortDir = $request->input('sortDir', 'desc');
        
        // Get messages with pagination
        $messages = $thread->messages()
            ->with(['sender', 'attachments', 'readStatus'])
            ->orderBy('created_at', $sortDir)
            ->paginate($perPage, ['*'], 'page', $page);
        
        return response()->json([
            'success' => true,
            'data' => $messages,
        ]);
    }

    /**
     * Mark a message as read
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead($id)
    {
        $user = Auth::user();
        
        // Find message and check if user is a participant in the thread
        $message = Message::whereHas('thread.participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($id);
        
        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Message not found or you do not have access',
            ], 404);
        }
        
        // Check if already read
        $alreadyRead = $message->readStatus()
            ->where('user_id', $user->id)
            ->exists();
            
        if (!$alreadyRead) {
            // Mark as read
            $message->markAsReadBy($user->id);
            
            // Broadcast unread count update
            $this->broadcastUnreadCountUpdate($user->id);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Message marked as read',
        ]);
    }

    /**
     * Helper to broadcast unread count updates to multiple users
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $participants
     * @param  array  $excludeUserIds
     * @return void
     */
    private function broadcastUnreadCountUpdates($participants, $excludeUserIds = [])
    {
        foreach ($participants as $participant) {
            if (!in_array($participant->user_id, $excludeUserIds)) {
                $this->broadcastUnreadCountUpdate($participant->user_id);
            }
        }
    }

    /**
     * Helper to broadcast unread count update to a user
     *
     * @param  int  $userId
     * @return void
     */
    private function broadcastUnreadCountUpdate($userId)
    {
        $user = \App\Models\User::find($userId);
        
        if (!$user) {
            return;
        }
        
        // Count unread messages in threads
        $threadUnreadCount = DB::table('messages')
            ->join('thread_participants', 'messages.thread_id', '=', 'thread_participants.thread_id')
            ->leftJoin('message_read_status', function($join) use ($userId) {
                $join->on('messages.id', '=', 'message_read_status.message_id')
                     ->where('message_read_status.user_id', '=', $userId);
            })
            ->where('thread_participants.user_id', $userId)
            ->where('messages.sender_id', '!=', $userId)
            ->whereNull('message_read_status.id')
            ->whereNull('messages.deleted_at')
            ->whereNull('thread_participants.deleted_at')
            ->count();
        
        // Count unread messages in seller chats (if exists)
        $sellerChatUnreadCount = 0;
        
        // Total count
        $totalUnreadCount = $threadUnreadCount + $sellerChatUnreadCount;
        
        // Broadcast update
        broadcast(new UnreadCountUpdated($userId, [
            'threads' => $threadUnreadCount,
            'sellerChats' => $sellerChatUnreadCount,
            'total' => $totalUnreadCount,
        ]));
    }

    /**
     * Add a reaction to a message
     *
     * @param  string  $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addReaction($id, Request $request)
    {
        \Log::info('addReaction called', [
            'message_id' => $id,
            'request_data' => $request->all(),
            'request_headers' => $request->headers->all(),
            'user_id' => Auth::id()
        ]);
        
        $user = Auth::user();
        
        if (!$user) {
            \Log::error('User not authenticated');
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated',
            ], 401);
        }
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'reaction' => 'required|string',
            'message_type' => 'required|string|in:thread,ticket,conversation,order'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed', [
                'errors' => $validator->errors()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find message based on type
        $message = null;
        switch ($request->message_type) {
            case 'thread':
                $message = Message::whereHas('thread.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'ticket':
                $message = \App\Models\TicketMessage::whereHas('ticket', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('assigned_to', $user->id);
                })->find($id);
                break;
            case 'conversation':
                $message = \App\Models\Messaging\ConversationMessage::whereHas('conversation.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'order':
                $message = \App\Models\OrderMessage::whereHas('order', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('seller_id', $user->id);
                })->find($id);
                break;
        }
        
        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Message not found or you do not have access',
            ], 404);
        }

        // Remove any existing reactions from this user before adding the new one
        $currentReactions = $message->getReactions();
        foreach ($currentReactions as $emoji => $users) {
            if (in_array($user->id, $users)) {
                $message->removeReaction($user->id, $emoji);
            }
        }
        
        // Add the new reaction
        $message->addReaction($user->id, $request->reaction);
        
        // Get updated reactions with user details
        $reactions = [];
        foreach ($message->getReactions() as $emoji => $userIds) {
            $users = \App\Models\User::whereIn('id', $userIds)
                ->select('id', 'name')
                ->get()
                ->toArray();
            
            $reactions[$emoji] = $users;
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Reaction added successfully',
            'data' => [
                'reactions' => $reactions
            ]
        ]);
    }
    
    /**
     * Remove a reaction from a message
     *
     * @param  string  $id
     * @param  string  $reaction
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeReaction($id, $reaction)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make(request()->all(), [
            'message_type' => 'required|string|in:thread,ticket,conversation,order'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find message based on type
        $message = null;
        switch (request()->message_type) {
            case 'thread':
                $message = Message::whereHas('thread.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'ticket':
                $message = \App\Models\TicketMessage::whereHas('ticket', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('assigned_to', $user->id);
                })->find($id);
                break;
            case 'conversation':
                $message = \App\Models\Messaging\ConversationMessage::whereHas('conversation.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'order':
                $message = \App\Models\OrderMessage::whereHas('order', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('seller_id', $user->id);
                })->find($id);
                break;
        }
        
        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Message not found or you do not have access',
            ], 404);
        }
        
        // Remove the reaction
        $message->removeReaction($user->id, $reaction);
        
        // Get updated reactions with user details
        $reactions = [];
        foreach ($message->getReactions() as $emoji => $userIds) {
            $users = \App\Models\User::whereIn('id', $userIds)
                ->select('id', 'name')
                ->get()
                ->toArray();
            
            $reactions[$emoji] = $users;
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Reaction removed successfully',
            'data' => [
                'reactions' => $reactions
            ]
        ]);
    }
    
    /**
     * Get reactions for a message
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReactions($id)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make(request()->all(), [
            'message_type' => 'required|string|in:thread,ticket,conversation,order'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Find message based on type
        $message = null;
        switch (request()->message_type) {
            case 'thread':
                $message = Message::whereHas('thread.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'ticket':
                $message = \App\Models\TicketMessage::whereHas('ticket', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('assigned_to', $user->id);
                })->find($id);
                break;
            case 'conversation':
                $message = \App\Models\Messaging\ConversationMessage::whereHas('conversation.participants', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->find($id);
                break;
            case 'order':
                $message = \App\Models\OrderMessage::whereHas('order', function($q) use ($user) {
                    $q->where('user_id', $user->id)
                      ->orWhere('seller_id', $user->id);
                })->find($id);
                break;
        }
        
        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Message not found or you do not have access',
            ], 404);
        }
        
        // Get reactions
        $reactions = $message->getReactions();
        
        // Format reactions for response
        $formattedReactions = [];
        foreach ($reactions as $reaction => $userIds) {
            $formattedReactions[$reaction] = [
                'reaction' => $reaction,
                'users' => \App\Models\User::whereIn('id', $userIds)
                    ->select('id', 'name')
                    ->get()
                    ->toArray()
            ];
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'reactions' => $formattedReactions
            ],
        ]);
    }
} 