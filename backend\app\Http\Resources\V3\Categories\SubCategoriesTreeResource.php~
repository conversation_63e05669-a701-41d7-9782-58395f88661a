<?php

namespace App\Http\Resources\V3\Categories;

use Illuminate\Http\Resources\Json\JsonResource;

class SubCategoriesTreeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

            return [
                'id' => (int)$item->id,
                'name' => $item->name,
                'slug' => $item->slug,
                'level' => $item->level,
                'children' => $this->categories ? SubCategoriesTreeResource::collection($item->childrenCategories) : [],
            ];
       
    }
}
